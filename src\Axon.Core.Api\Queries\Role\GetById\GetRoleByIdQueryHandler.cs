﻿using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Role;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Extensions;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services.Access;
using MediatR;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.Role.GetById
{
    internal sealed class GetRoleByIdQueryHandler : IRequestHandler<GetRoleByIdQueryRequest, CommandResponse<RoleModel>>
    {
        private readonly IRoleRepository roleRepository;
        private readonly IRoleDefinitionRepository roleDefinitionRepository;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IMapper mapper;

        public GetRoleByIdQueryHandler(IRoleRepository roleRepository, IRoleDefinitionRepository roleDefinitionRepository, IOrganisationRepository organisationRepository, IMapper mapper)
        {
            this.roleRepository = roleRepository;
            this.roleDefinitionRepository = roleDefinitionRepository;
            this.organisationRepository = organisationRepository;
            this.mapper = mapper;
        }

        public async Task<CommandResponse<RoleModel>> Handle(GetRoleByIdQueryRequest request, CancellationToken cancellationToken)
        {
            var definition = await roleDefinitionRepository.GetItemByAppCodeAsync(request.AppCodeName);

            if (definition == null)
            {
                return CommandResponse<RoleModel>.NotFound(nameof(RoleDefinitionEntity), request.AppCodeName);
            }

            var organisation = await organisationRepository.GetItemByCodeNameAsync(request.OrgCodeName);

            if (organisation == null)
            {
                return CommandResponse<RoleModel>.NotFound(nameof(OrganisationEntity), request.OrgCodeName);
            }

            var filteredDefinition = definition.FilterByAccessLevel(organisation.AccessLevel ?? AccessLevel.Restricted);

            var role = await roleRepository.GetItemAsync(request.Id);
            if (role == null)
            {
                return CommandResponse<RoleModel>.NotFound("Role Not Found", request.Id);
            }
            if (role.RoleType == RoleType.System && role.InheritRoleId != null)
            {
                return CommandResponse<RoleModel>.BadRequest("Cannot request overrides directly, please request using the system role id instead", request.Id);
            }
            var roleMap = mapper.Map<RoleModel>(role);

            var isDefaultRole = roleMap.OrganisationCodeName == null;
            foreach (var rolePermission in roleMap.Permissions)
            {
                rolePermission.IsInherited = isDefaultRole;
            }
   
            if (role.RoleType == RoleType.System)
            {
                var roleOverride = await roleRepository.GetOverrideAsync(role.Id, request.OrgCodeName, request.AppCodeName);
                if(roleOverride != null)
                {
                    var layeredRole = LayerDefaultRole(mapper.Map<RoleModel>(roleOverride), role);
                    layeredRole.Id = role.Id;
                    return new CommandResponse<RoleModel>() { data = FilterPermissionsByDefinition(filteredDefinition, layeredRole) };
                }
            }

            return new CommandResponse<RoleModel>() { data = FilterPermissionsByDefinition(filteredDefinition, roleMap) };
        }

        private static RoleModel FilterPermissionsByDefinition(RoleDefinitionEntity definition, RoleModel roleModel)
        {
            roleModel.Permissions = roleModel.Permissions.Where(mp => definition.Permissions.Exists(dp => dp.Name.Equals(mp.Name,
                                                                                                           System.StringComparison.OrdinalIgnoreCase))).ToList();

            return roleModel;
        }

        private RoleModel LayerDefaultRole(RoleModel roleMap, RoleEntity defaultRole)
        {
            foreach (var defaultPermission in defaultRole.Permissions)
            {
                if (!roleMap.Permissions.Exists(x => x.Name.Equals(defaultPermission.Name)))
                {
                    var newPermission = mapper.Map<RoleModel.Permission>(defaultPermission);
                    newPermission.IsInherited = true;
                    roleMap.Permissions.Add(newPermission);
                }
            }
            roleMap.RoleName = defaultRole.RoleName;
            return roleMap;
        }
    }
}
