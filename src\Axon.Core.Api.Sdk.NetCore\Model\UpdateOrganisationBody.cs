/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// UpdateOrganisationBody
    /// </summary>
    [DataContract(Name = "UpdateOrganisationBody")]
    public partial class UpdateOrganisationBody : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateOrganisationBody" /> class.
        /// </summary>
        /// <param name="displayName">displayName.</param>
        /// <param name="description">description.</param>
        /// <param name="icon">icon.</param>
        /// <param name="accessLevel">accessLevel.</param>
        /// <param name="isDeleted">isDeleted.</param>
        /// <param name="isEnabled">isEnabled.</param>
        /// <param name="theme">theme.</param>
        /// <param name="parentOrganisationId">parentOrganisationId.</param>
        public UpdateOrganisationBody(string displayName = default(string), string description = default(string), string icon = default(string), string accessLevel = default(string), bool isDeleted = default(bool), bool isEnabled = default(bool), SetupThemeBody theme = default(SetupThemeBody), string parentOrganisationId = default(string))
        {
            this.DisplayName = displayName;
            this.Description = description;
            this.Icon = icon;
            this.AccessLevel = accessLevel;
            this.IsDeleted = isDeleted;
            this.IsEnabled = isEnabled;
            this.Theme = theme;
            this.ParentOrganisationId = parentOrganisationId;
        }

        /// <summary>
        /// Gets or Sets DisplayName
        /// </summary>
        [DataMember(Name = "displayName", EmitDefaultValue = true)]
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or Sets Description
        /// </summary>
        [DataMember(Name = "description", EmitDefaultValue = true)]
        public string Description { get; set; }

        /// <summary>
        /// Gets or Sets Icon
        /// </summary>
        [DataMember(Name = "icon", EmitDefaultValue = true)]
        public string Icon { get; set; }

        /// <summary>
        /// Gets or Sets AccessLevel
        /// </summary>
        [DataMember(Name = "accessLevel", EmitDefaultValue = true)]
        public string AccessLevel { get; set; }

        /// <summary>
        /// Gets or Sets IsDeleted
        /// </summary>
        [DataMember(Name = "isDeleted", EmitDefaultValue = true)]
        public bool IsDeleted { get; set; }

        /// <summary>
        /// Gets or Sets IsEnabled
        /// </summary>
        [DataMember(Name = "isEnabled", EmitDefaultValue = true)]
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Gets or Sets Theme
        /// </summary>
        [DataMember(Name = "theme", EmitDefaultValue = false)]
        public SetupThemeBody Theme { get; set; }

        /// <summary>
        /// Gets or Sets ParentOrganisationId
        /// </summary>
        [DataMember(Name = "parentOrganisationId", EmitDefaultValue = true)]
        public string ParentOrganisationId { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class UpdateOrganisationBody {\n");
            sb.Append("  DisplayName: ").Append(DisplayName).Append("\n");
            sb.Append("  Description: ").Append(Description).Append("\n");
            sb.Append("  Icon: ").Append(Icon).Append("\n");
            sb.Append("  AccessLevel: ").Append(AccessLevel).Append("\n");
            sb.Append("  IsDeleted: ").Append(IsDeleted).Append("\n");
            sb.Append("  IsEnabled: ").Append(IsEnabled).Append("\n");
            sb.Append("  Theme: ").Append(Theme).Append("\n");
            sb.Append("  ParentOrganisationId: ").Append(ParentOrganisationId).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
