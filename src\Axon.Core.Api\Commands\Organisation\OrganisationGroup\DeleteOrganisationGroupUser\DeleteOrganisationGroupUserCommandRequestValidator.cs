﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.DeleteOrganisationGroupUser;

[UsedImplicitly]
public class AddOrganisationGroupUserCommandRequestValidator : AbstractValidator<DeleteOrganisationGroupUserCommandRequest>
{
    public AddOrganisationGroupUserCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.GroupId)
            .MustBeAValidGuid();
        RuleFor(x => x.UserId)
            .MustBeAValidGuid();
    }
}