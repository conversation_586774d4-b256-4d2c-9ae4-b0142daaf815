﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.AppAccess.ScopeResourcesQuery;

[UsedImplicitly]
public class GetScopesAndResourcesQueryRequestValidator : AbstractValidator<GetScopesAndResourcesQueryRequest>
{
    public GetScopesAndResourcesQueryRequestValidator()
    {
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.OrgCodeName)
            .MustBeAValidCodeName();
    }
}