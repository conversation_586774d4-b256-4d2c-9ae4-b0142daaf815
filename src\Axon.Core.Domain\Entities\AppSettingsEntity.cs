﻿using Axon.Core.Domain.Entities.Base;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Axon.Core.Domain.Converters;
using Newtonsoft.Json.Converters;

namespace Axon.Core.Domain.Entities
{
    public class AppSettingsEntity : BaseEntity
    {
        public string AppCodeName { get; set; }
        public string KeyVault { get; set; }
        public Dictionary<string, Setting> Settings { get; set; }
        public Dictionary<string, MasterDataSetting> MasterData {get; set;}

        public class Setting
        {
            public string Category { get; set; }
            public string DisplayName { get; set; }
            public string Description { get; set; }
            [Newtonsoft.Json.JsonConverter(typeof(StringEnumConverter))]
            public Enums.SettingDataType DataType { get; set; }
            public string[] DataTypeOptions { get; set; }
            public bool IsKeyVault { get; set; }
            public bool IsMultiple { get; set; }
            public bool IsPublic { get; set; }
            [JsonConverter(typeof(ObjectJsonConverter))]
            public object DefaultValue { get; set; }
        }

        public class MasterDataSetting
        {
            public string Category { get; set; }
            public string DisplayName { get; set; }
            public string Description { get; set; }
        }
    }
}