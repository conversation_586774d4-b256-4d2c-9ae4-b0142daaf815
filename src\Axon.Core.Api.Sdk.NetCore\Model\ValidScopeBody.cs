/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// ValidScopeBody
    /// </summary>
    [DataContract(Name = "ValidScopeBody")]
    public partial class ValidScopeBody : IValidatableObject
    {

        /// <summary>
        /// Gets or Sets ScopeType
        /// </summary>
        [DataMember(Name = "scopeType", EmitDefaultValue = false)]
        public ScopeType? ScopeType { get; set; }
        /// <summary>
        /// Initializes a new instance of the <see cref="ValidScopeBody" /> class.
        /// </summary>
        /// <param name="scope">scope.</param>
        /// <param name="scopeType">scopeType.</param>
        public ValidScopeBody(string scope = default(string), ScopeType? scopeType = default(ScopeType?))
        {
            this.Scope = scope;
            this.ScopeType = scopeType;
        }

        /// <summary>
        /// Gets or Sets Scope
        /// </summary>
        [DataMember(Name = "scope", EmitDefaultValue = true)]
        public string Scope { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class ValidScopeBody {\n");
            sb.Append("  Scope: ").Append(Scope).Append("\n");
            sb.Append("  ScopeType: ").Append(ScopeType).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
