﻿using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Role;
using Axon.Core.Api.Services.Role;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.Role.ListQuery
{
    [UsedImplicitly]
    internal class GetFilteredRoleListQueryHandler : IRequestHandler<GetFilteredRoleListQueryRequest, CommandResponse<ApiPagedListResult<RoleModel>>>
    {
        private readonly IRoleRepository roleRepository;
        private readonly IRoleModelService roleModelService;

        public GetFilteredRoleListQueryHandler(IRoleRepository roleRepository, IRoleModelService roleModelService)
        {
            this.roleRepository = roleRepository;
            this.roleModelService = roleModelService;
        }

        public async Task<CommandResponse<ApiPagedListResult<RoleModel>>> Handle(GetFilteredRoleListQueryRequest request, CancellationToken cancellationToken)
        {
            var roleModelList = await roleModelService.GetRoleModelsAsync(request.OrganisationCodeName, request.AppCodeName, request.ListParams);
            var totalItemsCount = await roleRepository.GetTotalItemsCountAsync(request.OrganisationCodeName, request.AppCodeName, request.ListParams);

            return CommandResponse<ApiPagedListResult<RoleModel>>.Data( new ApiPagedListResult<RoleModel>(roleModelList, request.ListParams.Offset, request.ListParams.Limit, totalItemsCount));
        }
    }
}
