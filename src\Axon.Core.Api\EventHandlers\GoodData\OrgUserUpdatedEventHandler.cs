﻿using Axon.Core.Contracts;
using MassTransit;
using Microsoft.Extensions.Logging;
using Phlex.Core.GoodData.Interfaces;
using System.Threading.Tasks;

namespace Axon.Core.Api.EventHandlers.GoodData;

public class OrgUserUpdatedEventHandler(
  IGoodData goodData,
  ILogger<OrgUserUpdatedEventHandler> logger) : IConsumer<OrgUserUpdatedEvent>
{
    public async Task Consume(ConsumeContext<OrgUserUpdatedEvent> context)
    {
        var message = context.Message;

        // Completely delete the user from GoodData
        await goodData.DeleteAppTenantUserAsync(message.UserEmail);

        logger.LogInformation("User {UserEmail} deleted in GoodData.", message.UserEmail);
    }
}
