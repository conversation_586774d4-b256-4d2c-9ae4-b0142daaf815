﻿using System.Collections.Generic;
using System.Text.Json.Serialization;
using Axon.Core.Domain.Converters;
using Axon.Core.Domain.Entities.Base;

namespace Axon.Core.Domain.Entities
{
    public class AppOrganisationSettingsEntity : BaseEntity
    {
        public string AppCodeName { get; set; }
        public string OrganisationCodeName { get; set; }
        [<PERSON><PERSON><PERSON>onverter(typeof(DictionaryStringObjectJsonConverter))]
        public Dictionary<string, object> FeatureManagement { get; set; }
        [<PERSON><PERSON><PERSON>onverter(typeof(DictionaryStringObjectJsonConverter))]
        public Dictionary<string, object> Settings { get; set; }
        public Dictionary<string, string> Secrets { get; set; }
        public Dictionary<string, IReadOnlyCollection<AppOrganisationSelectedMasterData>> MasterData { get; set; }

        public class AppOrganisationSelectedMasterData
        {
            public string Id { get; set; }
            public string Name { get; set; }
        }
    }
}