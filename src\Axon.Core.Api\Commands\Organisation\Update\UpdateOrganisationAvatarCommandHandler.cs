﻿using AutoMapper;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;
using Axon.Core.Domain.Services.AzureBlobStorage;
using Axon.Core.Api.Validators.FileUploads;
using Axon.Core.Api.Services.FileUpload;

namespace Axon.Core.Api.Commands.Organisation.Update
{
    internal class UpdateOrganisationAvatarCommandHandler : BaseEntityImageUpdateCommandHandler<UpdateOrganisationAvatarCommandRequest, OrganisationEntity, OrganisationUpdated>
    {
        private static EntityImageRequirements imageRequirements = new EntityImageRequirements { MaxFileSizeInBytes = 1048576, TargetHeightInPixels = 50, TargetWidthInPixels = 50, ShouldBeSquare = true };

        public UpdateOrganisationAvatarCommandHandler(
            IOrganisationRepository repo,
            IMapper mapper,
            IMessageBus messageBus,
            IOrganisationAvatarAzureBlobStorageManager organisationAvatarAzureBlobStorageManager,
            IUpdateImageValidator updateImageValidator,
            IImageResizer imageResizer,
            ILogger<UpdateOrganisationAvatarCommandHandler> logger) : base(repo, mapper, messageBus, organisationAvatarAzureBlobStorageManager, updateImageValidator, imageResizer, imageRequirements, logger)
        {
        }

        protected override string GetEntityCodeName(OrganisationEntity entity)
        {
            return entity.CodeName;
        }

        protected override void AppendImageUrlToEntity(OrganisationEntity entity, string blobUri)
        {
            if (entity.Theme != null)
            {
                entity.Theme.AvatarUrl = blobUri;
            }
            else
            {
                entity.Theme = new OrganisationEntity.ThemeConfigEntity
                {
                    AvatarUrl = blobUri
                };
            }
        }
    }
}
