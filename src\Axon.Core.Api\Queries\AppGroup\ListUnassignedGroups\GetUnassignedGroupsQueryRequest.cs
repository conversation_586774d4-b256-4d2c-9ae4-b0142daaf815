﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppGroup;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.AppGroup.ListUnassignedGroups
{
    public class GetUnassignedGroupsQueryRequest : IRequest<CommandResponse<ApiListResult<UnassignedGroupModel>>>
    {
        public string OrganisationCodeName { get; }
        public string AppCodeName { get; }

        public GetUnassignedGroupsQueryRequest(string organisationCodeName, string appCodeName)
        {
            OrganisationCodeName = organisationCodeName;
            AppCodeName = appCodeName;
        }
    }
}
