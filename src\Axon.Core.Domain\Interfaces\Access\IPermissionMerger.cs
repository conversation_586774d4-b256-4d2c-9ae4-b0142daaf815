﻿using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Models.Access;
using Axon.Core.Domain.Services.Access;
using System.Collections.Generic;

namespace Axon.Core.Domain.Interfaces.Access
{
    /// <summary>
    /// Merges a set of roles with the RoleDefinition to create the effective permissions expressed by the combinations present
    /// </summary>
    internal interface IPermissionMerger
    {
        /// <summary>
        /// Calculates the effective permissions based on a definition and a set of roles.
        /// If the ownerAccesLevel is provided, then this is considered for AccessLevel restricted permissions. It should not be provided if the organisation being targeted
        /// isn't the users owner.
        /// </summary>
        /// <param name="roleDefinition">The definition for the role</param>
        /// <param name="processedRoles">The roles to merge</param>
        /// <param name="ownerAccessLevel">The users AccessLevel if the permission merge is for the users owner organisation</param>
        /// <returns></returns>
        UserEffectivePermissions Combine(RoleDefinitionEntity roleDefinition, IEnumerable<ProcessedRole> processedRoles, AccessLevel? ownerAccessLevel = null);

        /// <summary>
        /// Combines <see cref="UserEffectivePermissions"/> from direct, and indirect sources together
        /// E.g if a user has global admin level access over an organistion this is indirect access, whereas the permissions the user has against the organisation specifically 
        /// is direct access.
        /// Scoped permissions are only considered from direct sources as these are organisation specific. Any present in the inderect permission set will be ignored.
        /// </summary>
        /// <param name="roleDefinition">The definition for the role</param>
        /// <param name="directPermissions">The effective permissions directly against the organisation, not admin flow etc</param>
        /// <param name="indirectPermissions">The permissions coming from a source other than directly against the organistion, e.g admin overrides etc</param>
        /// <returns>A <see cref="UserEffectivePermissions"/> that is the merged result of direct and indirect permissions </returns>
        UserEffectivePermissions Combine(RoleDefinitionEntity roleDefinition, UserEffectivePermissions directPermissions, UserEffectivePermissions indirectPermissions);
    }
}
