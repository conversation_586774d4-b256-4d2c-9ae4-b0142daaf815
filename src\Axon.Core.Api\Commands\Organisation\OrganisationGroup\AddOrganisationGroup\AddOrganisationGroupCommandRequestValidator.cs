﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.AddOrganisationGroup;

[UsedImplicitly]
public class AddOrganisationGroupCommandRequestValidator : AbstractValidator<AddOrganisationGroupCommandRequest>
{
    public AddOrganisationGroupCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
    }
}