﻿using System.IO;
using System.Text.Json;

namespace Axon.Core.Api.Mappers.AppOrganisationSettings;

public class NumberSettingMapper : ISettingMapper
{
    private const string CANNOT_PARSE_EXCEPTION_MESSAGE = "Cannot convert setting value to number.";

    public object Map(string settingValue)
    {
        if (!float.TryParse(settingValue, out var result))
            throw new InvalidDataException(CANNOT_PARSE_EXCEPTION_MESSAGE);
        return result;
    }

    public object Map(JsonElement settingValue)
    {
        if (settingValue.ValueKind is JsonValueKind.String)
            return GetNumberFromString(settingValue);
        if (!settingValue.TryGetDouble(out var result))
            throw new InvalidDataException(CANNOT_PARSE_EXCEPTION_MESSAGE);
        return result;
    }

    private static double GetNumberFromString(JsonElement settingValue)
    {
        var stringValue = settingValue.ToString()!;
        if (double.TryParse(stringValue, out var result))
            return result;
        throw new InvalidDataException(CANNOT_PARSE_EXCEPTION_MESSAGE);
    }
}