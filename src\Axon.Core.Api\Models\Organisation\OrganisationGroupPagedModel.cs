﻿using System.ComponentModel.DataAnnotations;
using System;
using Axon.Core.Domain.Entities.Base;
using Phlex.Core.Api.Abstractions.Models;
using Newtonsoft.Json;

namespace Axon.Core.Api.Models.Organisation;


public class OrganisationGroupPagedModel
{
    [Required] public string Id { get; set; }
    [Required] public string Name { get; set; }
    [Required] public ApiPagedListResult<OrganisationGroupUserModel> Users { get; set; }
    [Required] public DateTime CreatedAt { get; set; }
    [Required] public DateTime LastUpdatedDate { get; set; }
}