﻿using JetBrains.Annotations;
using MediatR;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.Core.Api.Commands
{
    [UsedImplicitly]
#pragma warning disable S2326 // Unused type parameters should be removed - Type is used implicitly by MediatR to resolve the correct delete handler
    public class DeleteCommandRequest<TModel> : IRequest<Result>
#pragma warning restore S2326 // Unused type parameters should be removed
    {
        public string Id { get; }

        public DeleteCommandRequest(string id)
        {
            Id = id;
        }
    }
}