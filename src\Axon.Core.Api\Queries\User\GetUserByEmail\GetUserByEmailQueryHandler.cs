﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.User;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;
using MediatR;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.User.GetUserByEmail;

[UsedImplicitly]
internal class GetUserByEmailQueryHandler : IRequestHandler<GetUserByEmailQueryRequest, CommandResponse<UserModel>>
{
    private readonly IUserRepository userRepository;

    public GetUserByEmailQueryHandler(IUserRepository userRepository)
    {
        this.userRepository = userRepository;
    }

    public async Task<CommandResponse<UserModel>> Handle(GetUserByEmailQueryRequest request, CancellationToken cancellationToken)
    {
        var user = await userRepository.GetUserByEmailAsync(request.Email);
        if (user == null)
            return CommandResponse<UserModel>.NotFound(nameof(UserEntity), "User does not exist");

        var userModel = new UserModel
        {
            Id = user.Id,
            Email = user.Email,
            UserName = user.Name
        };

        return CommandResponse<UserModel>.Data(userModel);
    }
}