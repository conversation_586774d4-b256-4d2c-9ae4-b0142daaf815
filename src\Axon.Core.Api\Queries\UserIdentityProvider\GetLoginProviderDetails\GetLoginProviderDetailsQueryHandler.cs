﻿using System;
using System.Reflection.Metadata;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.UserIdentityProvider;
using Axon.Core.Domain.Interfaces.Persistence;
using MediatR;
using Microsoft.Extensions.Configuration;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Constants;
using Axon.Core.Domain.Entities;
using Microsoft.Extensions.Logging;
using Axon.Core.Shared.Constants.Auth;
using Axon.Core.Domain.Enums;

namespace Axon.Core.Api.Queries.UserIdentityProvider.GetLoginProviderDetails
{
    internal sealed class GetLoginProviderDetailsQueryHandler : IRequestHandler<GetLoginProviderDetailsQuery, CommandResponse<LoginProviderDetails>>
    {
        private readonly IIdentityProviderRepository identityProviderRepository;
        private readonly IUserRepository userRepository;
        private readonly IConfiguration configuration;
        private readonly ILogger<GetLoginProviderDetailsQueryHandler> logger;

        public GetLoginProviderDetailsQueryHandler(IIdentityProviderRepository identityProviderRepository, IUserRepository userRepository, 
            IConfiguration configuration, ILogger<GetLoginProviderDetailsQueryHandler> logger)
        {
            this.identityProviderRepository = identityProviderRepository;
            this.userRepository = userRepository;
            this.configuration = configuration;
            this.logger = logger;
        }

        public async Task<CommandResponse<LoginProviderDetails>> Handle(GetLoginProviderDetailsQuery request, CancellationToken cancellationToken)
        {
            UserEntity user = null;

            try
            {
                user = await userRepository.GetUserByEmailAsync(request.Email);
            }
            catch (Exception ex)
            {
                logger.LogError(ex,"Unable to retrieve user to get IdentityProvider, Email:{Email}", request.Email);
                return new CommandResponse<LoginProviderDetails> { status = System.Net.HttpStatusCode.BadRequest, data = null };
            }

            if (user == null)
            {
                return new CommandResponse<LoginProviderDetails> { status = System.Net.HttpStatusCode.BadRequest, data = null };
            }

            if(user.Status != UserStatus.Active)
            {
                logger.LogWarning("User: {UserId} is disabled, so returning no login provider", user.Id);
                return new CommandResponse<LoginProviderDetails> { status = System.Net.HttpStatusCode.BadRequest, data = null };
            }

            var provider = await identityProviderRepository.GetItemAsync(user.IdentityProviderId);

            LoginProviderDetails data;
            switch (provider.Type)
            {
                case IdentityProviderType.Gigya:

                    data = new LoginProviderDetails
                    {
                        Authority = configuration.GetSection("Gigya").GetValue<string>("Issuer"),
                        ClientId = configuration.GetSection("Gigya").GetValue<string>("ClientId"),
                        UseCustomRefresh = bool.Parse(configuration.GetSection("Gigya").GetValue<string>("UseCustomRefresh"))
                    };

                    return new CommandResponse<LoginProviderDetails>
                    {
                        data = data
                    };
                case IdentityProviderType.MicrosoftEntra:
                    data = new LoginProviderDetails
                    {
                        Authority = configuration.GetSection("EntraSettings").GetValue<string>("Issuer"),
                        ClientId = configuration.GetSection("AzureAd").GetValue<string>("ClientId"),
                        UseCustomRefresh = bool.Parse(configuration.GetSection("EntraSettings").GetValue<string>("UseCustomRefresh"))

                    };

                    return new CommandResponse<LoginProviderDetails>
                    {
                        data = data
                    };
                default:
                    logger.LogError("Unknown IdentityProvider Type, Id:{id},Type:{type}, Name:{name}", provider.Id, provider.Type, provider.Name);
                    return new CommandResponse<LoginProviderDetails> { status = System.Net.HttpStatusCode.BadRequest, data = null };
            }
        }
    }
}
