using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.MasterData;
using Axon.Core.Api.Validators.MasterData;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;
using MediatR;

namespace Axon.Core.Api.Queries.MasterData.ListQuery;

[UsedImplicitly]
internal class GetFilteredMasterDataListQueryHandler : IRequestHandler<GetFilteredMasterDataListQueryRequest, CommandResponse<IEnumerable<MasterDataModel>>>
{
    private readonly IMasterDataRepository masterDataRepository;
    private readonly IMapper mapper;
    private readonly IMasterDataTypeValidator masterDataTypeValidator;

    public GetFilteredMasterDataListQueryHandler(IMasterDataRepository masterDataRepository, IMapper mapper, IMasterDataTypeValidator masterDataTypeValidator)
    {
        this.masterDataRepository = masterDataRepository;
        this.mapper = mapper;
        this.masterDataTypeValidator = masterDataTypeValidator;
    }

    public async Task<CommandResponse<IEnumerable<MasterDataModel>>> Handle(GetFilteredMasterDataListQueryRequest request, CancellationToken cancellationToken)
    {
        (var isValid, var errorMessage) = await masterDataTypeValidator.Validate(request.DataType);

        if (!isValid)
        {
            return CommandResponse<IEnumerable<MasterDataModel>>.BadRequest(nameof(IEnumerable<MasterDataModel>), errorMessage);
        }

        var masterDataList = await masterDataRepository.GetMasterDataAsync(request.DataType);
        var masterDataModelList = mapper.Map<IEnumerable<MasterDataModel>>(masterDataList);

        return CommandResponse<IEnumerable<MasterDataModel>>.Data(masterDataModelList);
    }
}