﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppUser;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;
using MediatR;
using Microsoft.Extensions.Logging;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.AppUser.ListQuery
{
    [UsedImplicitly]
    internal class GetFilteredAppUsersQueryHandler : IRequestHandler<GetFilteredAppUsersQueryRequest, CommandResponse<ApiPagedListResult<AppUserModel>>>
    {
        private readonly ILogger<GetFilteredAppUsersQueryHandler> logger;
        private readonly IAccessRepository accessRepository;
        private readonly IUserRequestContext userRequestContext;
        private readonly IUserRepository userRepository;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IRoleRepository roleRepository;
        private readonly IGroupRepository groupRepository;
        private readonly IMapper mapper;
        private readonly IAppRepository appRepository;

        public GetFilteredAppUsersQueryHandler(ILogger<GetFilteredAppUsersQueryHandler> logger, IAccessRepository accessRepository, IMapper mapper, IUserRepository userRepository, IUserRequestContext userRequestContext,
            IOrganisationRepository organisationRepository, IAppRepository appRepository, IRoleRepository roleRepository, IGroupRepository groupRepository)
        {
            this.accessRepository = accessRepository;
            this.mapper = mapper;
            this.userRepository = userRepository;
            this.userRequestContext = userRequestContext;
            this.organisationRepository = organisationRepository;
            this.appRepository = appRepository;
            this.logger = logger;
            this.roleRepository = roleRepository;
            this.groupRepository = groupRepository;
        }

        public async Task<CommandResponse<ApiPagedListResult<AppUserModel>>> Handle(GetFilteredAppUsersQueryRequest request, CancellationToken cancellationToken)
        {
            var userOid = userRequestContext.GetClaimsData().UserOid;
            var user = await userRepository.GetByIdentityProviderObjectIdAsync(userOid);
            if (user == null)
            {
                logger.LogWarning("User {userOid} does not exist.", userOid);
                return CommandResponse<ApiPagedListResult<AppUserModel>>.NotFound(nameof(UserEntity), "User does not exist");
            }

            OrganisationEntity org = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);
            if (org == null)
            {
                logger.LogWarning("Organisation {organisationCodeName} does not exist.", request.OrganisationCodeName);
                return CommandResponse<ApiPagedListResult<AppUserModel>>.NotFound(nameof(OrganisationEntity), request.OrganisationCodeName);
            }

            if (!request.AppCodeName.Equals(AppNameConstants.AxonCoreCodeName, System.StringComparison.OrdinalIgnoreCase))
            {
                var app = await appRepository.GetItemByAppCodeNameAsync(request.AppCodeName);
                if (app == null)
                {
                    logger.LogWarning("Application {appCodeName} does not exist.", request.AppCodeName);
                    return CommandResponse<ApiPagedListResult<AppUserModel>>.NotFound(nameof(AppEntity), request.AppCodeName);
                }
            }

            (var totalUsers, var userPage) = await accessRepository.GetAppUserPage(request.OrganisationCodeName, request.AppCodeName, request.ListParams);

            if (totalUsers == 0)
            {
                return CommandResponse<ApiPagedListResult<AppUserModel>>.Data(new ApiPagedListResult<AppUserModel>(new List<AppUserModel>(), request.ListParams.Offset, request.ListParams.Limit, totalUsers));
            }


            List<string> groupIds = userPage.SelectMany(userAccess => userAccess.Value.Select(ua => ua.GroupId)).OfType<string>().Distinct().ToList();
            List<string> roleIds = userPage.SelectMany(userAccess => userAccess.Value.Select(ua => ua.RoleId)).OfType<string>().Distinct().ToList();

            IReadOnlyCollection<RoleEntity> roles = await roleRepository.GetRolesAsync(roleIds);
            if (roles == null || roleIds.Count > 0 && !roles.Any())
            {
                logger.LogWarning("Roles {roles} not found.", string.Join(',', roleIds));
            }

            IReadOnlyCollection<GroupEntity> groups = await groupRepository.GetGroupsAsync(groupIds);
            if (groups == null || groupIds.Count > 0 && !groups.Any())
            {
                logger.LogWarning("Groups {groups} not found.", string.Join(',', groupIds));
            }

            var appUserModels = userPage.Select(appUser =>
            {
                var accessEntities = appUser.Value.ToList();
                var userEntity = accessEntities.FirstOrDefault();
                var appUserGroups = new List<GroupEntity>();

                if (groups is { Count: > 0 })
                {
                    appUserGroups =
                        accessEntities.SelectMany(appUser => groups.Where(group => group.Id == appUser.GroupId && appUser.User.Id == userEntity?.User.Id)).ToList();
                }

                var appUserRoles = new List<RoleEntity>();
                if (roles is { Count: > 0 })
                {
                    appUserRoles = accessEntities.SelectMany(appUser => roles.Where(role => role.Id == appUser.RoleId && appUser.User.Id == userEntity?.User.Id))
                        .ToList();
                }


                var hasDirectRole = accessEntities.Any(item =>
                    item.AccessType == AccessType.UserAccess && item.User.Id == userEntity?.User.Id && item.GroupId == null && item.RoleId != null);

                return new AppUserModel()
                {
                    AppCodeName = userEntity?.AppCodeName,
                    OrganisationCodeName = userEntity?.OrganisationCodeName,
                    UserId = userEntity?.User.Id,
                    UserName = userEntity?.User.Name,
                    Email = userEntity?.User.Email,
                    Groups = appUserGroups.Select(group => new AppUserGroupModel()
                    {
                        Id = group.Id,
                        GroupName = group.Name
                    }).ToArray(),
                    Roles = appUserRoles.Select(role => new AppUserRoleModel()
                    {
                        Id = role.Id,
                        RoleName = role.RoleName
                    }).ToArray(),
                    HasDirectRole = hasDirectRole
                };
            });


            if (!appUserModels.Any())
            {
                return CommandResponse<ApiPagedListResult<AppUserModel>>.Data(new ApiPagedListResult<AppUserModel>(new List<AppUserModel>(), request.ListParams.Offset, request.ListParams.Limit, totalUsers));
            }

            return CommandResponse<ApiPagedListResult<AppUserModel>>.Data(new ApiPagedListResult<AppUserModel>(appUserModels, request.ListParams.Offset, request.ListParams.Limit, totalUsers));
        }
    }
}
