﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.RoleDefinition.RoleDefinitionListQuery;

[UsedImplicitly]
public class GetRoleDefinitionQueryRequestValidator : AbstractValidator<GetRoleDefinitionQueryRequest>
{
    public GetRoleDefinitionQueryRequestValidator()
    {
        RuleFor(x => x.AppCode)
            .MustBeAValidCodeName();
    }
}