﻿namespace Axon.Core.Domain.Constants;

public static class AuditEventDescriptions
{
    public static string UserCreated(string userEmail) => $"User \"{userEmail}\" has successfully been created";
    public static string UserUpdated(string userEmail) => $"User \"{userEmail}\" has successfully been updated";
    public static string UserLinkedToOrganisation(string userEmail, string organisationCodeName) => $"User \"{userEmail}\" has successfully been linked to organisation \"{organisationCodeName}\"";
    public static string UserUnlinkedFromOrganisation(string userEmail, string organisationCodeName) => $"User \"{userEmail}\" has successfully been unlinked from organisation \"{organisationCodeName}\"";
    public static string GroupCreated(string groupName) => $"User has successfully created group \"{groupName}\"";
    public static string GroupRenamed(string oldGroupName, string newGroupName) => $"User has successfully renamed group \"{oldGroupName}\" to \"{newGroupName}\"";
    public static string GroupDeleted(string groupName) => $"User has successfully deleted group \"{groupName}\"";
    public static string UserAssignedToGroup(string userEmail, string groupName) => $"User \"{userEmail}\" has successfully been assigned to group \"{groupName}\"";
    public static string UserUnassignedFromGroup(string userEmail, string groupName) => $"User \"{userEmail}\" has successfully been unassigned from group \"{groupName}\"";

    public const string OrganisationCreated = "Organisation has successfully been created";
    public const string OrganisationUpdated = "Organisation has successfully been updated";
    public const string RoleCreated = "Role has successfully been created";
    public const string RoleUpdated = "Role has successfully been updated";
    public const string RoleDeleted = "Role has successfully been deleted";
    public const string RoleDefinitionUpdated = "Role definition has successfully been updated";

    public const string AppGroupCreated = "AppGroup has successfully been created";
    public const string AppGroupUpdated = "AppGroup has successfully been updated";
    public const string AppGroupDeleted = "AppGroup has successfully been deleted";

    public const string AccessCreated = "Access has successfully been created";
    public const string AccessUpdated = "Access has successfully been updated";
    public const string AccessDeleted = "Access has successfully been deleted";
}
