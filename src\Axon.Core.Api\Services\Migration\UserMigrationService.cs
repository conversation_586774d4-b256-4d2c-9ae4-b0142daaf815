﻿using Axon.Core.Domain.Entities;
using System.Globalization;
using System.Net;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Microsoft.Extensions.Logging;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Persistence;
using System;
using System.Collections.Generic;
using Axon.Core.Domain.Constants;

namespace Axon.Core.Api.Services.Migration
{
    public class UserMigrationService : IUserMigrationService
    {
        private readonly ILogger<UserMigrationService> logger;
        private readonly IUserRoleMigrationService userRoleMigrationService;
        private readonly IUserRequestContext userRequestContext;
        private readonly IUserRepository userRepository;
        private readonly IUserOwnerOrganisationMigrationService userOwnerOrganisationMigrationService;
        private readonly IUserOrganisationAccessMigrationCleanupService userOrganisationAccessMigrationCleanupService;

        public UserMigrationService(ILogger<UserMigrationService> logger, 
            IUserRoleMigrationService userRoleMigrationService,
            IUserRequestContext userRequestContext, 
            IUserRepository userRepository,
            IUserOwnerOrganisationMigrationService userOwnerOrganisationMigrationService, 
            IUserOrganisationAccessMigrationCleanupService userOrganisationAccessMigrationCleanupService)
        {
            this.logger = logger;
            this.userRoleMigrationService = userRoleMigrationService;
            this.userRequestContext = userRequestContext;
            this.userRepository = userRepository;
            this.userOwnerOrganisationMigrationService = userOwnerOrganisationMigrationService;
            this.userOrganisationAccessMigrationCleanupService = userOrganisationAccessMigrationCleanupService;
        }
        public async Task<CommandResponse<UserEntity>> MigrateUser(UserEntity user)
        {
            #region Migration

            var migrationVersion = 0.0;
            if (user.MigrationVersion != null && !double.TryParse(user.MigrationVersion, CultureInfo.InvariantCulture, out migrationVersion))
            {
                logger.Log(LogLevel.Debug, "Unable to parse and determine the user migration version value, migration has not been completed for user, userid:{userId}, user email:{email}.", user.Id, user.Email);

                var errorList = BuildErrorsDictionary("Role migration skipped", "Unable to determine the users migration version value");

                return new CommandResponse<UserEntity>() { status = HttpStatusCode.InternalServerError, errors = errorList };
            }

            //begin migration if needed
            if (user.MigrationVersion != null && migrationVersion >= MigrationConstants.LatestMigrationVersion) return new CommandResponse<UserEntity>() { status = HttpStatusCode.OK, data = user };


            #region Organsiation Access

            var organisationAccessCleanupResponse = await OrganisationAccessCleanup();

            if (organisationAccessCleanupResponse.Item1) return organisationAccessCleanupResponse.Item2;

            #endregion Organsiation Access


            #region Role Migration

            var roleMigrationResponse = await RoleMigration();

            if (roleMigrationResponse.Item1) return roleMigrationResponse.Item2;

            #endregion Role Migration


            #region Owner Organisation

            logger.Log(LogLevel.Debug, "User owner organisation step started.");

            await userOwnerOrganisationMigrationService.Migrate();

            logger.Log(LogLevel.Debug, "User owner organisation step complete.");

            #endregion Owner Organisation


            user = await userRepository.GetByIdentityProviderObjectIdAsync(userRequestContext.GetClaimsData().UserOid);
            await UpdateUserMigrateVersion(user, MigrationConstants.LatestMigrationVersion);

            #endregion Migration

            return new CommandResponse<UserEntity>{ status = HttpStatusCode.OK, data = user };
        }


        /// <summary>
        /// Migrates the old security for a user to the new UserAccess items
        /// </summary>
        /// <returns></returns>
        private async Task<Tuple<bool, CommandResponse<UserEntity>>> RoleMigration()
        {
            logger.Log(LogLevel.Debug, "User role migration step started.");

            var errorList = BuildErrorsDictionary("Role migration failed for user", "Role migration failed for user");

            if (!await userRoleMigrationService.Migrate())
            {
                return new Tuple<bool, CommandResponse<UserEntity>>(true, new CommandResponse<UserEntity>() { status = HttpStatusCode.InternalServerError, errors = errorList });
            }
            logger.Log(LogLevel.Debug, "User role migration step complete.");
            return new Tuple<bool, CommandResponse<UserEntity>>(false, null);
        }

        /// <summary>
        /// Method that will make sure the user has a role that matches the accessed roles on the OrgansiationAccess items they have and will remove any they don't
        /// </summary>
        /// <returns></returns>
        private async Task<Tuple<bool, CommandResponse<UserEntity>>> OrganisationAccessCleanup()
        {
            logger.Log(LogLevel.Debug, "User organisation access migration cleanup started.");

            var errorList = BuildErrorsDictionary("Organisation Access migration clean up failed for user", "Organisation Access migration clean up failed for user");

            if (!await userOrganisationAccessMigrationCleanupService.Migrate())
            {
                return new Tuple<bool, CommandResponse<UserEntity>>(true, new CommandResponse<UserEntity>() { status = HttpStatusCode.InternalServerError, errors = errorList });
            }

            logger.Log(LogLevel.Debug, "User organisation access migration cleanup complete.");
            return new Tuple<bool, CommandResponse<UserEntity>>(false, null);
        }

        /// <summary>
        /// Updates the current users Migration version with what is passed in
        /// </summary>
        /// <param name="user"></param>
        /// <param name="migrationVersion"></param>
        /// <returns></returns>
        private async Task<CommandResponse> UpdateUserMigrateVersion(UserEntity user, double migrationVersion)

        {
            logger.Log(LogLevel.Debug, "Starting to update users migrate version.");
            user.MigrationVersion = migrationVersion.ToString(CultureInfo.InvariantCulture);
            await userRepository.UpdateItemAsync(user.Id, user);
            logger.Log(LogLevel.Debug, "users last accessed  migrate version.");

            return new CommandResponse() { status = HttpStatusCode.OK };
        }

        /// <summary>
        /// Builds the generic error dictionary from error and description
        /// </summary>
        /// <param name="error">The text for the error</param>
        /// <param name="description">The description text of the error</param>
        /// <returns></returns>
        private static Dictionary<string, string[]> BuildErrorsDictionary(string error, string description)
        {
            return new Dictionary<string, string[]>
                { { error, [description] } };
        }
    }
}
