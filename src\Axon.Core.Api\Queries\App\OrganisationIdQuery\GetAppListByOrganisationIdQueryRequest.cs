﻿using System.Collections.Generic;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.App;
using Axon.Core.Shared.Api;
using MediatR;

namespace Axon.Core.Api.Queries.App.OrganisationIdQuery
{
    public class GetAppListByOrganisationIdQueryRequest : IRequest<CommandResponse<IEnumerable<AppOrganisationModel>>>
    {
        public string Id { get; }
        public ListParams ListParams { get; }


        public GetAppListByOrganisationIdQueryRequest(string id, ListParams listParams)
        {
            this.Id = id;
            this.ListParams = listParams;
        }
    }
}
