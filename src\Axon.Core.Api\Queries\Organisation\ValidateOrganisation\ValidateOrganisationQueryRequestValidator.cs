﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.Organisation.ValidateOrganisation;

[UsedImplicitly]
public class ValidateOrganisationQueryRequestValidator : AbstractValidator<ValidateOrganisationQueryRequest>
{
    public ValidateOrganisationQueryRequestValidator()
    {
        RuleFor(x => x.CodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.Id)
            .MustBeAValidGuid();
        RuleFor(x => x.Name)
            .MustBeAValidDisplayName();
    }
}