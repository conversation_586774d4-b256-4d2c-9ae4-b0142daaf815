﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Axon.Core.Domain.Entities;

namespace Axon.Core.Domain.Interfaces.Persistence;

public interface IUserRepository : IRepository<UserEntity>
{
    Task<UserEntity> GetByIdentityProviderObjectIdAsync(string objectId);
    Task<UserEntity> GetByIdentityProviderObjectIdAsync(string objectId, string identityProviderId);
    Task<IList<UserEntity>> GetUsersAsync(string[] emails);
    Task<UserEntity> GetUserByEmailAsync(string email);
    Task UpdateLastAccessed(string userId, DateTime lastAccessed);
}