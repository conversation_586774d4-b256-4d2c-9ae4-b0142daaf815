﻿using MediatR;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser.UpdateUserInOrganisation;

public class UpdateUserInOrganisationCommandRequest(string organisationCodeName, string userId, UpdateUserInOrganisationCommandRequestBody UpdateUserInOrganisationCommandBody) : IRequest<CommandResponse>
{
    public string OrganisationCodeName { get; } = organisationCodeName;
    public string UserId { get; } = userId;
    public UpdateUserInOrganisationCommandRequestBody UpdateUserInOrganisationCommandBody { get; } = UpdateUserInOrganisationCommandBody;
}