﻿using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models;
using Axon.Core.Domain.Interfaces.Persistence;
using MediatR;

namespace Axon.Core.Api.Queries.Organisation.ValidateOrganisation
{
    internal class ValidateOrganisationQueryHandler : IRequestHandler<ValidateOrganisationQueryRequest, CommandResponse<ValidationResultModel>>
    {
        private readonly IOrganisationRepository repository;
        public ValidateOrganisationQueryHandler(IOrganisationRepository repository)
        {
            this.repository = repository;
        }

        public async Task<CommandResponse<ValidationResultModel>> Handle(ValidateOrganisationQueryRequest request, CancellationToken cancellationToken)
        {
            var entityByName = (await repository.GetItemsByDisplayNameAsync(request.Name)).AsEnumerable();
            var entityByCodeName = (await repository.GetItemsByCodeNameAsync(request.CodeName)).AsEnumerable();

            var errors = new Dictionary<string, string[]>();

            if (entityByName.Any(x => x.Id != request.Id) )
                errors.Add(nameof(request.Name), new[] { $"Organisation `{request.Name}` already exists" });

            if (entityByCodeName.Any(x => x.Id != request.Id))
                errors.Add(nameof(request.CodeName), new[] { $"Organisation with sub path `{request.CodeName.ToLowerInvariant()}` already exists" });

            var isValid = errors.Count == 0;
            return CommandResponse<ValidationResultModel>.Data(new ValidationResultModel(isValid), HttpStatusCode.OK, errors);
        }
    }
}