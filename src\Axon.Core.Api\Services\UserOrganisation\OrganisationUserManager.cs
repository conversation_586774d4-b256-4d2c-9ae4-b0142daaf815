﻿using Axon.Core.Domain.Entities;
using Axon.Core.Shared.Audit;
using System.Threading.Tasks;
using System;
using System.Linq;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services.Access;
using Axon.Core.Api.Services.Authorisation;
using Microsoft.Extensions.Logging;
using Axon.Core.Api.Services.AppGroup;
using Axon.Core.Domain.Extensions;
using Axon.Core.Shared.Audit.Services;
using CommunityToolkit.Diagnostics;

namespace Axon.Core.Api.Services.UserOrganisation;

public interface IOrganisationUserManager
{
    Task AssignUser(UserEntity user, string[] groups, Guid correlationId, ClientDetails clientDetails, OrganisationEntity organisationEntity);
    Task AssignAdminUser(UserEntity user, Guid correlationId, ClientDetails clientDetails, OrganisationEntity organisationEntity);
    Task UnlinkUserFromUnspecifiedChildOrganisations(UserEntity user, Guid correlationId, ClientDetails clientDetails, OrganisationEntity organisationEntity, string[] childOrganisationCodeNames);
    Task<(UserEntity entity, bool isNew, bool isUpdated)> CreateOrUpdateUser(string userEmail, string userName, UserStatus userStatus, string organisationId, string organisationCodeName, string identityProviderId, Guid correlationId, ClientDetails clientDetails, string userId = null);
    Task UnlinkUserFromOrganisation(Guid correlationId, ClientDetails clientDetails, string organisationAccessContainerId, string userId, string userEmail);
    Task AssignUserToGroup(Guid correlationId, ClientDetails clientDetails, string organisationId, string organisationCodeName, UserEntity user, GroupEntity group);
    Task UnassignUserFromGroup(Guid correlationId, ClientDetails clientDetails, string organisationCodeName, string userId, string userEmail, GroupEntity group);
}

internal class OrganisationUserManager : IOrganisationUserManager
{
    private readonly IAuditService<TenantAuditExtensions> auditService;
    private readonly IAccessService accessService;
    private readonly IUserRepository userRepository;
    private readonly IGroupRepository groupRepository;
    private readonly IOrganisationRepository organisationRepository;
    private readonly IAccessRepository accessRepository;
    private readonly IRoleRepository roleRepository;
    private readonly IAccessGate accessGate;
    private readonly IAppGroupMemberSynchroniser appGroupMemberSynchroniser;
    private readonly IAccessLevelProvider accessLevelProvider;
    private readonly ILogger<OrganisationUserManager> logger;

    public OrganisationUserManager(IAuditService<TenantAuditExtensions> auditService,
        IUserRepository userRepository,
        IGroupRepository groupRepository,
        IOrganisationRepository organisationRepository,
        IAccessRepository accessRepository,
        IRoleRepository roleRepository,
        IAccessGate accessGate,
        IAppGroupMemberSynchroniser appGroupMemberSynchroniser,
        IAccessLevelProvider accessLevelProvider,
        ILogger<OrganisationUserManager> logger, IAccessService accessService)
    {
        Guard.IsNotNull(auditService);
        this.auditService = auditService;
        Guard.IsNotNull(userRepository);
        this.userRepository = userRepository;
        Guard.IsNotNull(groupRepository);
        this.groupRepository = groupRepository;
        Guard.IsNotNull(organisationRepository);
        this.organisationRepository = organisationRepository;
        Guard.IsNotNull(accessRepository);
        this.accessRepository = accessRepository;
        Guard.IsNotNull(accessGate);
        this.accessGate = accessGate;
        Guard.IsNotNull(appGroupMemberSynchroniser);
        this.appGroupMemberSynchroniser = appGroupMemberSynchroniser;
        this.accessLevelProvider = accessLevelProvider;
        this.logger = logger;
        this.accessService = accessService;
        this.roleRepository = roleRepository;
    }

    public async Task AssignUser(UserEntity user, string[] groups, Guid correlationId, ClientDetails clientDetails, OrganisationEntity organisationEntity)
    {
        await CreateOrUpdateOrganisationAccessContainer(correlationId, clientDetails, organisationEntity.Id, organisationEntity.CodeName, user);

        var groupAccessContainerList = await accessRepository.GetAccessItemsForUserAsync(user.Id, AccessType.GroupAccess);

        if (groups?.Length == 0 && groupAccessContainerList.Count == 0)
            return;

        var groupOrganisationAccessContainers = groupAccessContainerList.Where(x => x.OrganisationId.Equals(organisationEntity.Id, StringComparison.OrdinalIgnoreCase)).ToList();
        var currentGroupIds = groupOrganisationAccessContainers.Select(x => x.GroupId);

        var groupIdsToAdd = groups == null ? [] : groups.Where(x => !currentGroupIds.Contains(x)).ToList();
        foreach (var groupId in groupIdsToAdd)
        {
            var group = await groupRepository.GetItemAsync(groupId);
            await CreateGroupAccessContainer(correlationId, clientDetails, organisationEntity.Id, organisationEntity.CodeName, user, group);
            await appGroupMemberSynchroniser.SynchroniseNewGroupMemberWithAppGroups(groupId, user, organisationEntity.Id, organisationEntity.CodeName, correlationId, clientDetails);

            group.LastUpdatedDate = DateTime.UtcNow;
            await groupRepository.UpdateItemAsync(group.Id, group);
        }

        var groupIdsToUpdate = currentGroupIds.Where((groups ?? []).Contains);
        var groupAccessContainersToUpdate = groupOrganisationAccessContainers.Where(x => groupIdsToUpdate.Contains(x.GroupId));

        foreach (var accessEntity in groupAccessContainersToUpdate)
        {
            await UpdateGroupAccessContainer(user, accessEntity.Id);
        }

        var groupAccessContainersToRemove = groups == null || groups.Length == 0 ? groupOrganisationAccessContainers : groupOrganisationAccessContainers.Where(x => !groups.Contains(x.GroupId)).ToList();
        var groupAccessContainersDictionaryToRemove = groupAccessContainersToRemove.ToDictionary(x => x.Id, x => x.GroupId);
        foreach (var groupAccessContainerId in groupAccessContainersDictionaryToRemove)
        {
            var group = await groupRepository.GetItemAsync(groupAccessContainerId.Value);
            await RemoveGroupAccessContainer(correlationId, clientDetails, groupAccessContainerId.Key, group, organisationEntity.CodeName, user.Email);

            group.LastUpdatedDate = DateTime.UtcNow;
            await groupRepository.UpdateItemAsync(group.Id, group);
        }
    }

    public async Task AssignAdminUser(UserEntity user, Guid correlationId, ClientDetails clientDetails, OrganisationEntity organisationEntity)
    {
        var adminRole = (await roleRepository.GetDefaultRolesByName(["Administrator"], AppNameConstants.AxonCoreCodeName))?
            .SingleOrDefault(x => x.RoleType == RoleType.System && x.OrganisationCodeName == null);
        var effectivePermissions = await accessService.GetEffectivePermissions(user.Email, AppNameConstants.AxonCoreCodeName, organisationEntity.CodeName);

        if (adminRole is not null &&
            effectivePermissions.EffectivePermissions.Roles.All(x => !x.Name.Equals("System.Administrator", StringComparison.InvariantCultureIgnoreCase)))
        {
            await AssignUser(user, null, correlationId, clientDetails, organisationEntity);

            var linkToOrgTenantAuditExtensions =
                new TenantAuditExtensions(AuditEventCategories.User, AuditEventDescriptions.AccessCreated, correlationId, clientDetails, organisationEntity.CodeName);
            var accessEntity = new AccessEntity();
            await auditService.LogAsync(AuditEventTypes.UserAssigned, linkToOrgTenantAuditExtensions, accessEntity,
                async () =>
                {
                    accessEntity.User = new AccessUser
                    {
                        Id = user.Id,
                        Email = user.Email,
                        Name = user.Name
                    };

                    accessEntity.AccessType = AccessType.UserAccess;
                    accessEntity.CreatedAt = DateTime.UtcNow;
                    accessEntity.OrganisationId = organisationEntity.Id;
                    accessEntity.OrganisationCodeName = organisationEntity.CodeName;
                    accessEntity.RoleId = adminRole.Id;
                    accessEntity.AppCodeName = AppNameConstants.AxonCoreCodeName;
                    accessEntity.AppId = AppNameConstants.AxonCoreCodeName;

                    await accessRepository.AddItemAsync(accessEntity);
                });
        }
    }

    public async Task UnlinkUserFromUnspecifiedChildOrganisations(UserEntity user, Guid correlationId, ClientDetails clientDetails, OrganisationEntity organisationEntity, string[] childOrganisationCodeNames)
    {
        var childOrganisations = await organisationRepository.GetAllChildOrganisationsByParentId(organisationEntity.Id);
        var childOrganisationIds = childOrganisations.Select(x => x.Id).ToList();

        var usersOrganisationAccessEntities = await accessRepository.GetOrganisationsForUserAsync(user.Id);
        var usersChildOrganisationAccessEntities = usersOrganisationAccessEntities.Where(x => childOrganisationIds.Contains(x.OrganisationId));

        var accessEntitiesToRemove = usersChildOrganisationAccessEntities
            .Where(accessEntity => !childOrganisationCodeNames.Contains(accessEntity.OrganisationCodeName));

        foreach (var organisationAccessEntity in accessEntitiesToRemove)
        {
            await UnlinkUserFromOrganisation(correlationId, clientDetails, organisationAccessEntity.Id, user.Id, user.Email);
        }
    }

    public async Task UnlinkUserFromOrganisation(Guid correlationId, ClientDetails clientDetails, string organisationAccessContainerId, string userId, string userEmail)
    {
        var organisationAccessEntity = await accessRepository.GetItemAsync(organisationAccessContainerId);
        var unlinkFromOrgTenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.User, AuditEventDescriptions.UserUnlinkedFromOrganisation(userEmail, organisationAccessEntity.OrganisationCodeName), correlationId, clientDetails, organisationAccessEntity.OrganisationCodeName);

        await auditService.LogAsync(AuditEventTypes.UserUnassigned, unlinkFromOrgTenantAuditExtensions, organisationAccessEntity,
            async () =>
            {
                await accessRepository.DeleteItemAsync(organisationAccessContainerId);
            });

        var userGroupAccessContainers = await accessRepository.GetAccessItemsForUserAsync(userId, AccessType.GroupAccess);
        var userOrganisationGroupAccessContainers = userGroupAccessContainers.Where(x => x.OrganisationId.Equals(organisationAccessEntity.OrganisationId, StringComparison.InvariantCultureIgnoreCase));

        foreach (var userOrganisationGroupAccessContainer in userOrganisationGroupAccessContainers)
        {
            var group = await groupRepository.GetItemAsync(userOrganisationGroupAccessContainer.GroupId);
            await RemoveGroupAccessContainer(correlationId, clientDetails, userOrganisationGroupAccessContainer.Id, group, organisationAccessEntity.OrganisationCodeName, userEmail);

            group.LastUpdatedDate = DateTime.UtcNow;
            await groupRepository.UpdateItemAsync(group.Id, group);
        }

        var userUserAccessContainers = await accessRepository.GetUserAccessForUserAsync(userId);
        var userOrganisationUserAccessContainers = userUserAccessContainers.Where(x => x.OrganisationId.Equals(organisationAccessEntity.OrganisationId, StringComparison.InvariantCultureIgnoreCase));

        foreach (var userOrganisationUserAccessContainer in userOrganisationUserAccessContainers)
        {
            await RemoveUserAccessContainer(correlationId, clientDetails, userOrganisationUserAccessContainer.Id, organisationAccessEntity.OrganisationCodeName, userEmail);
        }
    }

    public async Task<(UserEntity entity, bool isNew, bool isUpdated)> CreateOrUpdateUser(string userEmail, string userName, UserStatus userStatus, string organisationId, string organisationCodeName,string identityProviderId, Guid correlationId, ClientDetails clientDetails, string userId = null)
    {
        var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.User, AuditEventDescriptions.UserCreated(userEmail), correlationId, clientDetails, organisationCodeName);

        var userEntity = string.IsNullOrEmpty(userId) ? null : await userRepository.GetItemAsync(userId);

        if (userEntity == null)
        {
            userEntity = new UserEntity();
            await auditService.LogAsync(AuditEventTypes.UserCreated, tenantAuditExtensions, userEntity,
                async () =>
                {
                    userEntity.Email = userEmail;
                    userEntity.Name = userName;
                    userEntity.Status = userStatus;
                    userEntity.OwnerOrganisationId = organisationId;
                    userEntity.IdentityProviderId = identityProviderId;
                    userEntity.MigrationVersion = "1.4";
                    userEntity.CreatedAt = DateTime.UtcNow;
                    userEntity.Status = UserStatus.Active;
                    if (!string.IsNullOrEmpty(userId)) userEntity.Id = userId;

                    await userRepository.AddItemAsync(userEntity);
                });
            return (userEntity, true, false);
        }

        var updateUser = !userEntity.Email.Equals(userEmail, StringComparison.InvariantCultureIgnoreCase)
                         || !userEntity.Name.Equals(userName, StringComparison.InvariantCultureIgnoreCase)
                         || userEntity.Status != userStatus;
        if (updateUser)
        {
            await CanUpdateUserDetailsAsync(userEntity);

            var userUpdateTenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.User, AuditEventDescriptions.UserUpdated(userEmail), correlationId, clientDetails, organisationCodeName);

            await auditService.LogAsync(AuditEventTypes.UserUpdated, userUpdateTenantAuditExtensions, userEntity,
                async () =>
                {
                    if (!userEntity.Email.Equals(userEmail, StringComparison.InvariantCultureIgnoreCase)) userEntity.Email = userEmail;
                    if (!userEntity.Name.Equals(userName, StringComparison.InvariantCultureIgnoreCase)) userEntity.Name = userName;
                    userEntity.Status = userStatus;

                    await userRepository.UpdateItemAsync(userEntity.Id, userEntity);
                });
        }
        return (userEntity, false, updateUser);
    }

    public async Task AssignUserToGroup(Guid correlationId, ClientDetails clientDetails, string organisationId, string organisationCodeName, UserEntity user, GroupEntity group)
    {
        var accessEntity = (await accessRepository.GetAccessItemsForUserAsync(user.Id, AccessType.GroupAccess))
            .FirstOrDefault(x => string.Equals(x.GroupId, group.Id, StringComparison.OrdinalIgnoreCase));
        if (accessEntity != null)
            return;

        await CreateGroupAccessContainer(correlationId, clientDetails, organisationId, organisationCodeName, user, group);
        await appGroupMemberSynchroniser.SynchroniseNewGroupMemberWithAppGroups(group.Id, user, organisationId, organisationCodeName, correlationId, clientDetails);
    }

    public async Task UnassignUserFromGroup(Guid correlationId, ClientDetails clientDetails, string organisationCodeName, string userId, string userEmail, GroupEntity group)
    {
        //Note that currently a user can have multiple GroupAccess containers for a single group
        var userGroupAccessContainers = (await accessRepository.GetAccessItemsForUserAsync(userId, AccessType.GroupAccess))
            .Where(x => string.Equals(x.GroupId, group.Id, StringComparison.OrdinalIgnoreCase));

        foreach (var userGroupAccessContainer in userGroupAccessContainers)
        {
            await RemoveGroupAccessContainer(correlationId, clientDetails, userGroupAccessContainer.Id, group, organisationCodeName, userEmail);
        }
    }

    private async Task CreateGroupAccessContainer(Guid correlationId, ClientDetails clientDetails, string organisationId, string organisationCodeName, UserEntity user, GroupEntity group)
    {
        var assignToGroupTenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.User, AuditEventDescriptions.UserAssignedToGroup(user.Email, group.Name), correlationId, clientDetails, organisationCodeName);

        var accessEntity = new AccessEntity();

        await auditService.LogAsync(AuditEventTypes.UserAssigned, assignToGroupTenantAuditExtensions, accessEntity,
            async () =>
            {
                accessEntity.User = new AccessUser
                {
                    Id = user.Id,
                    Email = user.Email,
                    Name = user.Name
                };

                accessEntity.AccessType = AccessType.GroupAccess;
                accessEntity.CreatedAt = DateTime.UtcNow;
                accessEntity.OrganisationId = organisationId;
                accessEntity.OrganisationCodeName = organisationCodeName;
                accessEntity.GroupId = group.Id;

                await accessRepository.AddItemAsync(accessEntity);
            });
    }

    private async Task UpdateGroupAccessContainer(UserEntity user, string groupAccessContainerId)
    {
        var accessEntity = await accessRepository.GetItemAsync(groupAccessContainerId);

        var updateGroup = !accessEntity.User.Email.Equals(user.Email, StringComparison.InvariantCultureIgnoreCase) || !accessEntity.User.Name.Equals(user.Name, StringComparison.InvariantCultureIgnoreCase);
        if (updateGroup)
        {
            await CanUpdateUserDetailsAsync(user);

            if (!accessEntity.User.Email.Equals(user.Email, StringComparison.InvariantCultureIgnoreCase)) accessEntity.User.Email = user.Email;
            if (!accessEntity.User.Name.Equals(user.Name, StringComparison.InvariantCultureIgnoreCase)) accessEntity.User.Name = user.Name;

            await accessRepository.UpdateItemAsync(accessEntity.Id, accessEntity);
        }
    }

    private async Task RemoveGroupAccessContainer(Guid correlationId, ClientDetails clientDetails, string groupAccessContainerId, GroupEntity group, string organisationCodeName, string userEmail)
    {
        var linkToOrgTenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.User, AuditEventDescriptions.UserUnassignedFromGroup(userEmail, group.Name), correlationId, clientDetails, organisationCodeName);

        var accessEntity = await accessRepository.GetItemAsync(groupAccessContainerId);
        await auditService.LogAsync(AuditEventTypes.UserUnassigned, linkToOrgTenantAuditExtensions, accessEntity,
            async () =>
            {
                await accessRepository.DeleteItemAsync(groupAccessContainerId);
            });
    }

    private async Task RemoveUserAccessContainer(Guid correlationId, ClientDetails clientDetails, string userAccessContainerId, string organisationCodeName, string userEmail)
    {
        var unlinkFromOrgTenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.User, AuditEventDescriptions.UserUnlinkedFromOrganisation(userEmail, organisationCodeName), correlationId, clientDetails, organisationCodeName);

        var accessEntity = await accessRepository.GetItemAsync(userAccessContainerId);
        await auditService.LogAsync(AuditEventTypes.UserUnassigned, unlinkFromOrgTenantAuditExtensions, accessEntity,
            async () =>
            {
                await accessRepository.DeleteItemAsync(userAccessContainerId);
            });
    }

    private async Task CreateOrUpdateOrganisationAccessContainer(Guid correlationId, ClientDetails clientDetails, string organisationId, string organisationCodeName, UserEntity user)
    {
        var linkToOrgTenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.User, AuditEventDescriptions.UserLinkedToOrganisation(user.Email, organisationCodeName), correlationId, clientDetails, organisationCodeName);

        var organisationAccessContainer = await accessRepository.GetOrganisationsForUserAsync(user.Id);
        var accessEntityList = organisationAccessContainer.Where(x => x.OrganisationId.Equals(organisationId, StringComparison.OrdinalIgnoreCase)).ToList();

        if (accessEntityList.Count == 0)
        {
            var accessEntity = new AccessEntity();
            await auditService.LogAsync(AuditEventTypes.UserAssigned, linkToOrgTenantAuditExtensions, accessEntity,
                async () =>
                {
                    accessEntity.User = new AccessUser
                    {
                        Id = user.Id,
                        Email = user.Email,
                        Name = user.Name
                    };

                    accessEntity.AccessType = AccessType.OrganisationAccess;
                    accessEntity.CreatedAt = DateTime.UtcNow;
                    accessEntity.OrganisationId = organisationId;
                    accessEntity.OrganisationCodeName = organisationCodeName;

                    await accessRepository.AddItemAsync(accessEntity);
                });
            return;
        }

        var update = accessEntityList.FirstOrDefault()?.User.Email.Equals(user.Email, StringComparison.InvariantCultureIgnoreCase) == false
                     || accessEntityList.FirstOrDefault()?.User.Name.Equals(user.Name, StringComparison.InvariantCultureIgnoreCase) == false;
        if (update)
        {
            await CanUpdateUserDetailsAsync(user);

            foreach (var accessEntity in accessEntityList)
            {
                if (!accessEntity.User.Email.Equals(user.Email, StringComparison.InvariantCultureIgnoreCase)) accessEntity.User.Email = user.Email;
                if (!accessEntity.User.Name.Equals(user.Name, StringComparison.InvariantCultureIgnoreCase)) accessEntity.User.Name = user.Name;

                await accessRepository.UpdateItemAsync(accessEntity.Id, accessEntity);
            }
        }
    }

    private async Task CanUpdateUserDetailsAsync(UserEntity user)
    {
        bool isAllowed;
        if (!string.IsNullOrWhiteSpace(user.OwnerOrganisationId))
            isAllowed = await accessGate.IsAuthorisedForOrganisationById(AppNameConstants.AxonCoreCodeName, user.OwnerOrganisationId, nameof(CorePermissions.EditOrganisation));
        else
            // Users should have an Owner Organisation, but we need to handle old users who don't have that set
            isAllowed = await accessLevelProvider.ProvideAsync() == AccessLevel.Global;

        if (!isAllowed)
        {
            logger.LogWarning("User is not authorized to update user details for user {userId} ({userEmail}) under organisation {organisationId}.", user.Id, user.Email, user.OwnerOrganisationId);
            throw new UnauthorizedAccessException("User is not authorized to update user details.");
        }
    }
}