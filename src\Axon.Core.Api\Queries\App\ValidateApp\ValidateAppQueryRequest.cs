﻿using System.ComponentModel.DataAnnotations;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Axon.Core.Api.Queries.App.ValidateApp
{
    public class ValidateAppQueryRequest : IRequest<CommandResponse<ValidationResultModel>>
    {
        [FromQuery]
        [Required]
        public string Name { get; }

        [FromQuery]
        [Required]
        public string CodeName { get; }

        public string Id { get; }

        public ValidateAppQueryRequest(string id, string name, string codeName)
        {
            Id = id;
            Name = name;
            CodeName = codeName;
        }
    }
}
