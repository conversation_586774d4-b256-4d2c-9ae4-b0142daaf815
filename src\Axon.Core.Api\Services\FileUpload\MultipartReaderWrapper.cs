﻿using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.WebUtilities;

namespace Axon.Core.Api.Services.FileUpload
{
    public class MultipartReaderWrapper : IMultipartReaderWrapper
    {
        public async Task<MultipartSection> ReadMultipartSectionAsync(string boundary, Stream stream, CancellationToken cancellationToken)
        {
            var reader = new MultipartReader(boundary, stream);
            var section = await reader.ReadNextSectionAsync(cancellationToken);

            return section;
        }
    }
}
