﻿using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Models.Access;
using Axon.Core.Shared.Api;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Domain.Interfaces.Access
{
    public interface IAccessService
    {
        Task<IReadOnlyCollection<UserOrganisationAccess>> GetOrganisationAccessForUser(string userId);

        Task<IReadOnlyCollection<UserAccess>> GetUserAccessForUser(string userId);

        /// <summary>
        /// Returns the effective permissions for a user, for a specific application and organisation
        /// </summary>
        /// <param name="emailAddress">The users email address</param>
        /// <param name="appCodeName">The applications code name</param>
        /// <param name="orgCodeName">The organisations code name</param>
        /// <returns>an <see cref="UserEffectivePermissionsForAppAndOrg"/> with the users effective permissions for the application / organisation combination</returns>
        Task<UserEffectivePermissionsForAppAndOrg> GetEffectivePermissions(string emailAddress, string appCodeName, string targetOrgCodeName);

        /// <summary>
        /// Returns the effective permissions for a user, for a specific application across all organisations they have access on
        /// </summary>
        /// <param name="emailAddress">The users email address</param>
        /// <param name="appCodeName">The applications code name</param>
        /// <returns>A set of <see cref="UserEffectivePermissionsForAppAndOrg"/> with the users effective permissions for the application for every organisation they have access to</returns>
        Task<IReadOnlyCollection<UserEffectivePermissionsForAppAndOrg>> GetUsersDirectEffectivePermissionsForApp(string emailAddress, string appCodeName);

        Task<IReadOnlyCollection<(OrganisationEntity Organistaion, UserEffectivePermissionsForAppAndOrg EffectivePermissions)>> GetUsersAccessibleOrganisationsAndEffectivePermissions(string emailAddress,
                                                                                                                                                                                       string appCodeName,
                                                                                                                                                                                       ListParams listParams = null);

        /// <summary>
        /// Returns the effective permissions for a user, across all applications and organisations they have access on
        /// </summary>
        /// <param name="emailAddress">The users email address</param>
        /// <returns>A set of <see cref="UserEffectivePermissionsForAppAndOrg"/> with the users effective permissions for all the applications and organisations they have access to</returns>
        Task<IReadOnlyCollection<UserEffectivePermissionsForAppAndOrg>> GetUsersDirectEffectivePermissionsForAllAppsAndOrgs(string emailAddress);

        /// <summary>
        /// Adds A Organisation Access item for the user
        /// </summary>
        /// <param name="orgCode"></param>
        /// <param name="email"></param>
        /// <param name="ipaddress"></param>
        /// <returns></returns>
        Task<string> AddOrganisationAccessForUser(string orgCode, string email, string ipaddress);

        /// <summary>
        /// Adds A User Access item for the user
        /// </summary>
        /// <param name="orgCode">The Organisation Code</param>
        /// <param name="email">The users email</param>
        /// <param name="ipaddress">The users ipaddress</param>
        /// <returns></returns>
        Task<string> AddAppOrganisationAccessForUser(string orgCode, string appCode, string email, string roleId, string ipaddress);
    }
}
