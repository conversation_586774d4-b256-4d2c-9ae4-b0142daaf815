﻿using System.Net.Http.Json;
using Axon.Core.Api.Commands;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace Axon.Core.Api.Queries.Audit
{
    public class GetAuditsUrlQueryRequest : IRequest<CommandResponse>
    {
        public string OrgCodeName { get; }
        public string AppCodeName { get; }
        public string Host { get; }

        public GetAuditsUrlQueryRequest(string orgCodeName, string appCodeName,string host)
        {
            OrgCodeName = orgCodeName;
            AppCodeName = appCodeName;
            Host = host;
        }
    }
}
