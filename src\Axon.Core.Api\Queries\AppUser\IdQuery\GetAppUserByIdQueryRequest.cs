﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppUser;
using MediatR;

namespace Axon.Core.Api.Queries.AppUser.IdQuery
{
    public class GetAppUserByIdQueryRequest: IRequest<CommandResponse<SpecificAppUserModel>> 
    {
        public GetAppUserByIdQueryRequest(string emailAddress, string appCodeName, string orgCodeName)
        {
            EmailAddress = emailAddress;
            AppCodeName = appCodeName;
            OrgCodeName = orgCodeName;
        }

        public string EmailAddress { get; }
        public string AppCodeName { get; }
        public string OrgCodeName { get; }
    }
}
