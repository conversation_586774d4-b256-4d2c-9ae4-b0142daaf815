﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Services.Migration;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Infrastructure.Auth;
using Axon.Core.Shared.Services.Users;
using CommunityToolkit.Diagnostics;
using Microsoft.Extensions.Logging;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace Axon.Core.Api.Services.Authorisation
{
    public class UserOnboardingService : IUserOnboardingService
    {
        private readonly IUserRepository userRepo;
        private readonly ILogger<UserOnboardingService> logger;
        private readonly IUserMigrationService userMigrationService;

        public UserOnboardingService(IUserRepository userRepo,
                                    IUserMigrationService userMigrationService,
                                    ILogger<UserOnboardingService> logger)
        {
            Guard.IsNotNull(userRepo);
            this.userRepo = userRepo;
            Guard.IsNotNull(logger);
            this.logger = logger;
            Guard.IsNotNull(userMigrationService);
            this.userMigrationService = userMigrationService;
        }

        public async Task<CommandResponse> Onboard(IEnumerable<Claim> claims, string email)
        {
            var claimsList = claims.ToList();
            var userOid = claimsList.ObjectId();
            UserEntity user = null;
            try
            {
                user = await userRepo.GetUserByEmailAsync(email);
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error,ex, "Error unable to retrieve user, email:{email}, user oid:{userOid}", email, userOid);
                throw new UnauthorizedAccessException("LOGIN_FAILED_FOR_USER");
            }

            //users are now created through the front end so we don't want to create and should throw an unauthorized
            if (user == null)
            {
                logger.Log(LogLevel.Error, "Error user not found, user:{email}, user oid:{userOid}", email, userOid);
                throw new UnauthorizedAccessException("LOGIN_FAILED_FOR_USER");
            }


            if(user.MigrationVersion != null && user.MigrationVersion.Equals("1.4") && user.IdentityProviderObjectId == userOid)
            {
                return CommandResponse.Success();
            }
            else if (UserIsNewUser(user, email)) //new user check || or older user that needs to be migrated to use sub for objectid
            {
                return await CompleteNewUser(userOid, user);
            }
            else if(UserIsOlderUserForMigration(user, claimsList, email))
            {
                return await MigrateExistingUser(userOid, user);
            }

            logger.Log(LogLevel.Error, "User Is in an unknown state, user:{email}, user oid:{userOid} is neither up to date, new or in a sensible state for migration", email, userOid);
            return CommandResponse.Failed(nameof(UserEntity), "User is in an unknown state");

        }

        /// <summary>
        /// Completes a new 
        /// </summary>
        /// <param name="userOid"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        private async Task<CommandResponse> CompleteNewUser(string userOid, UserEntity user)
        {
            logger.Log(LogLevel.Debug, "New user completion starting for user, userid:{userid} in CompleteNewUser", userOid);
            user.IdentityProviderObjectId = userOid;
            await userRepo.UpdateItemAsync(user.Id, user);
            logger.Log(LogLevel.Debug, "New user completion finished for user, userid:{userid} in CompleteNewUser", userOid);
            return CommandResponse.Success();
        }

        private async Task<CommandResponse> MigrateExistingUser(string userOid, UserEntity user)
        {
            user.IdentityProviderObjectId = userOid;
            await userRepo.UpdateItemAsync(user.Id, user);

            logger.Log(LogLevel.Debug, "User migration starting for user, userid:{userid} in MigrateExistingUser", userOid);
            return await userMigrationService.MigrateUser(user);
        }

        private static bool UserIsNewUser(UserEntity user, string email)
        {
            var migrationVersionIsNew = user.MigrationVersion != null && user.MigrationVersion.Equals("1.4");
            var oIdIsNullOrEmpty = string.IsNullOrEmpty(user.IdentityProviderObjectId);
            var emailMatches = user.Email.Equals(email, StringComparison.InvariantCultureIgnoreCase);
            return migrationVersionIsNew &&
                   oIdIsNullOrEmpty &&
                   emailMatches;
        }

        public static bool UserIsOlderUserForMigration(UserEntity user, List<Claim> claims,string email)
        {
            var migrationVersionParsed = double.TryParse(user.MigrationVersion, out var testMigrationVersion);
            var migrationVersionIsNullOrOld = (string.IsNullOrEmpty(user.MigrationVersion) || migrationVersionParsed && testMigrationVersion < 1.4);
            var emailMatches = user.Email.Equals(email, StringComparison.InvariantCultureIgnoreCase);
            var objectIdDoesntMatch = user.IdentityProviderObjectId != claims.ObjectId();
            return migrationVersionIsNullOrOld &&
                   emailMatches &&
                   objectIdDoesntMatch;
        }

    }
}