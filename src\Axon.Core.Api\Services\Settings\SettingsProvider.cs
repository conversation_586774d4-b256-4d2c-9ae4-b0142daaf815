﻿using System;
using System.Collections.Generic;
using System.Text.Json;
using Axon.Core.Api.Mappers.AppOrganisationSettings;
using Axon.Core.Api.Models.AppSetting;
using Axon.Core.Domain.Entities;
using Azure.Security.KeyVault.Secrets;
using CommunityToolkit.Diagnostics;
using JetBrains.Annotations;
using Microsoft.Extensions.Logging;

namespace Axon.Core.Api.Services.Settings
{
    public interface ISettingsProvider
    {
        public SettingsValueModel GetSetting(KeyValuePair<string, AppSettingsEntity.Setting> setting, AppOrganisationSettingsEntity appOrganisationSettings, SecretClient secretClient, string orgName);
    }
    public class SettingsProvider : ISettingsProvider
    {
        private readonly IAppOrganisationSettingsMapperFactory appOrganisationSettingsMapperFactory;
        private readonly ILogger<SettingsProvider> logger;

        public SettingsProvider(IAppOrganisationSettingsMapperFactory appOrganisationSettingsMapperFactory, ILogger<SettingsProvider> logger)
        {
            this.appOrganisationSettingsMapperFactory = appOrganisationSettingsMapperFactory;
            this.logger = logger;
        }

        public SettingsValueModel GetSetting(KeyValuePair<string, AppSettingsEntity.Setting> setting, AppOrganisationSettingsEntity appOrganisationSettings, SecretClient secretClient, string orgName)
        {
            var (settingName, value) = setting;

            var settingMapper = appOrganisationSettingsMapperFactory.Create(value.DataType);

            var settingValue = value.IsKeyVault ?
                GetSecretValue(orgName, settingName, secretClient, settingMapper) :
                GetSettingValue(setting, appOrganisationSettings, settingMapper);

            return new SettingsValueModel(settingName, value.Category, value.DisplayName, value.Description, value.DataType,
                settingValue, value.DataTypeOptions, value.IsKeyVault, value.IsMultiple, value.IsPublic);
        }

        private object GetSecretValue(string orgName, string settingName, SecretClient secretClient, ISettingMapper settingMapper)
        {
            Guard.IsNotNull(secretClient);

            try
            {
                var settingValue = secretClient.GetSecret($"{orgName}-{settingName}");
                return settingMapper.Map(settingValue.Value.Value);
            }
            catch (Azure.Identity.AuthenticationFailedException)
            {
                throw;
            }
            catch (Exception)
            {
                logger.LogInformation("Cannot find secret {secretName} in {secretClient}, returning null.", settingName, secretClient.VaultUri);
                return null;
            }
        }

        private static object GetSettingValue(KeyValuePair<string, AppSettingsEntity.Setting> setting, [CanBeNull] AppOrganisationSettingsEntity appOrganisationSettings, ISettingMapper settingMapper)
        {
            var (settingName, value) = setting;

            object settingValue;
            if (appOrganisationSettings is null)
                settingValue = value.DefaultValue;
            else
            {
                var appHasValueConfigured = appOrganisationSettings.Settings.TryGetValue(settingName, out settingValue);

                if (!appHasValueConfigured)
                    settingValue = value.DefaultValue;
            }

            return setting.Value.IsMultiple ?
                settingMapper.Map(GetArrayJson(settingValue)) :
                settingMapper.Map(JsonSerializer.SerializeToElement(settingValue));
        }

        private static string GetArrayJson(object settingValue)
        {
            return settingValue == null ? string.Empty : settingValue.ToString();
        }
    }
}