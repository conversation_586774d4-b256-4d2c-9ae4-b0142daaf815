﻿using Axon.Core.Domain.Enums;
using System;

namespace Axon.Core.Api.Mappers.AppOrganisationSettings
{
    public interface IAppOrganisationSettingsMapperFactory
    {
        ISettingMapper Create(SettingDataType settingDataType);
    }

    internal class AppOrganisationSettingsMapperFactory : IAppOrganisationSettingsMapperFactory
    {
        private readonly Func<SettingDataType, ISettingMapper> participantFactory;

        public AppOrganisationSettingsMapperFactory(Func<SettingDataType, ISettingMapper> participantFactory)
        {
            this.participantFactory = participantFactory;
        }

        public ISettingMapper Create(SettingDataType settingDataType)
        {
            return participantFactory(settingDataType);
        }
    }
}