﻿using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using MassTransit;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.EventHandlers;

public class UpsertScopeCommandHandler : IConsumer<UpsertScopeCommand>
{
    private readonly IMessageBus messageBus;
    private readonly IScopeResourceRepository scopeResourceRepository;

    public UpsertScopeCommandHandler(IMessageBus messageBus, IScopeResourceRepository scopeResourceRepository)
    {
        this.messageBus = messageBus;
        this.scopeResourceRepository = scopeResourceRepository;
    }

    public async Task Consume(ConsumeContext<UpsertScopeCommand> context)
    {
        var message = context.Message;

        var scopeResources = await scopeResourceRepository.GetScopeResourcesAsync(message.AppCodeName, message.OrganisationCodeName, message.Scope);

        var scope = scopeResources.FirstOrDefault(x =>
            x.ResourceId == message.ResourceId);

        if (scope is null)
        {
            scope = new ScopeResourceEntity
            {
                AppCodeName = message.AppCodeName,
                OrganisationCodeName = message.OrganisationCodeName,
                Scope = message.Scope,
                ResourceName = message.ResourceName,
                ResourceId = message.ResourceId
            };

            await scopeResourceRepository.AddItemAsync(scope);
            return;
        }

        scope.ChangeResourceName(message.ResourceName);
        await scopeResourceRepository.UpdateItemAsync(scope.Id, scope);

        var scopeResourceNameChangedEvent = new ScopeResourceNameChangedEvent(message.AppCodeName, message.OrganisationCodeName, message.Permission, message.ResourceId, message.ResourceName);
        await messageBus.PublishAsync(scopeResourceNameChangedEvent);
    }
}