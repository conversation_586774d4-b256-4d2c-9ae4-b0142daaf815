﻿using MediatR;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser.DeleteOrganisationUser;

public class DeleteOrganisationUserCommandRequest : IRequest<CommandResponse>
{
    public string OrganisationCodeName { get; }
    public string UserId { get; }

    public DeleteOrganisationUserCommandRequest(string organisationCodeName, string userId)
    {
        OrganisationCodeName = organisationCodeName;
        UserId = userId;
    }
}