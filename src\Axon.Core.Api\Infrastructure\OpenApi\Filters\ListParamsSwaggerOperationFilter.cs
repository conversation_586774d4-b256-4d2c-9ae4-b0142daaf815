﻿using System;
using System.Collections.Generic;
using System.Linq;
using Axon.Core.Api.Controllers;
using Axon.Core.Api.Filters;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Shared.Api;
using JetBrains.Annotations;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Axon.Core.Api.Infrastructure.OpenApi.Filters
{
    /// <summary>
    /// A custom MVC ACtion Filter that adds the necessary metadata to the SwaggerDoc for the
    /// automatically handled <see cref="ListParams"/> query string parameters.<para/>
    ///
    /// NOTE: That for the SDK's to support these parameters, they must also appear on the Controller Action
    /// method signature as seen in the code example below, the values from these method arguments are correctly populated in the controller actions calls but
    /// <strong><em>ARE NOT</em></strong> used, the values are
    /// not passed in to the request, Use the values from <see cref="ApiControllerBase.ListParams"/> instead, which is populated from <see cref="ListParamsActionFilterAttribute"/>.
    ///
    /// <code>
    /// public async Task&lt;IActionResult&gt; GetAppListAsync(int? limit = 20, int? offset = 0, [CanBeNull] string orderBy="", [CanBeNull] string filter="")
    /// </code>
    /// </summary>
    [UsedImplicitly]
    public class ListParamsSwaggerOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            operation.ExternalDocs = new OpenApiExternalDocs
            {
                Description = "Dynamic Linq Expressions reference", 
                Url = new Uri("https://dynamic-linq.net/expression-language")
            };

            operation.Parameters ??= new List<OpenApiParameter>();

            if (!IsListAction(context)) return;
            
            RemoveAutoGeneratedDocumentation(operation, new[] { "limit", "offset", "orderby", "filter" });
            AddEnhancedParameterDocumentation(operation);
        }

        private static bool IsListAction(OperationFilterContext context) => 
            context.ApiDescription.ActionDescriptor is ControllerActionDescriptor descriptor && descriptor.ActionName.Contains("List");

        private static void AddEnhancedParameterDocumentation(OpenApiOperation operation)
        {
            operation.Parameters.Add(new OpenApiParameter()
            {
                Name = "limit",
                In = ParameterLocation.Query,
                Description = $"Max records to return, Range: 1 - {ListParams.MaxLimit}.",
                Required = false
            });
            operation.Parameters.Add(new OpenApiParameter()
            {
                Name = "offset",
                In = ParameterLocation.Query,
                Description = $"Records to skip, Range: 0 - {ListParams.MaxOffset}.",
                Required = false
            });
            operation.Parameters.Add(new OpenApiParameter()
            {
                Name = "orderby",
                In = ParameterLocation.Query,
                Description = "Ordering of the results",
                Required = false,
                Examples = new Dictionary<string, OpenApiExample>()
                {
                    {
                        "undefined-order", new OpenApiExample()
                        {
                            Description = "Undefined ordering",
                            Value = new OpenApiString($""),
                            Summary = "Undefined"
                        }
                    },
                    {
                        "ascending", new OpenApiExample()
                        {
                            Description = "Sort by a single field in ascending order",
                            Value = new OpenApiString($"{nameof(OrganisationModel.DisplayName).ToLowerInvariant()} asc"),
                            Summary = "Ascending"
                        }
                    },
                    {
                        "descending", new OpenApiExample()
                        {
                            Description = "Sort by a single field in descending order",
                            Value = new OpenApiString($"{nameof(OrganisationModel.DisplayName).ToLowerInvariant()} desc"),
                            Summary = "Descending"
                        }
                    },
                    {
                        "multiple", new OpenApiExample()
                        {
                            Description = "Sort by multiple fields in multiple orders",
                            Value = new OpenApiString(
                                $"{nameof(OrganisationModel.DisplayName).ToLowerInvariant()} asc, {nameof(OrganisationModel.Icon).ToLowerInvariant()} desc, {nameof(OrganisationModel.IsEnabled).ToLowerInvariant()} asc"),
                            Summary = "Multiple"
                        }
                    },
                }
            });
            operation.Parameters.Add(new OpenApiParameter()
            {
                Name = "filter",
                In = ParameterLocation.Query,
                Description = "Filter the results",
                Required = false,
                Examples = new Dictionary<string, OpenApiExample>()
                {
                    {
                        "no-filter", new OpenApiExample()
                        {
                            Description = "No filter",
                            Value = new OpenApiString($""),
                            Summary = "No filtering"
                        }
                    },
                    {
                        "equality", new OpenApiExample()
                        {
                            Description = "Equality expression",
                            Value = new OpenApiString($"{nameof(OrganisationModel.DisplayName).ToLowerInvariant()} eq \"Test\""),
                            Summary = "(==) Equals"
                        }
                    },
                    {
                        "not-equality", new OpenApiExample()
                        {
                            Description = "Not equals expression",
                            Value = new OpenApiString($"{nameof(OrganisationModel.DisplayName).ToLowerInvariant()} neq \"Test\""),
                            Summary = "(!=) Not equals"
                        }
                    },
                    {
                        "greaterthan", new OpenApiExample()
                        {
                            Description = "Greater than expression",
                            Value = new OpenApiString($"{nameof(OrganisationModel.DisplayName).ToLowerInvariant()} gt \"Test\""),
                            Summary = "(>) Greater than"
                        }
                    },
                    {
                        "lessthan", new OpenApiExample()
                        {
                            Description = "Less than expression",
                            Value = new OpenApiString($"{nameof(OrganisationModel.DisplayName).ToLowerInvariant()} lt \"Test\""),
                            Summary = "(<) Less than"
                        }
                    },
                    {
                        "greaterthanorequals", new OpenApiExample()
                        {
                            Description = "Greater than or equals expression",
                            Value = new OpenApiString($"{nameof(OrganisationModel.DisplayName).ToLowerInvariant()} ge \"Test\""),
                            Summary = "(>=) Greater than or equals"
                        }
                    },
                    {
                        "lessthanorequals", new OpenApiExample()
                        {
                            Description = "Less than expression",
                            Value = new OpenApiString($"{nameof(OrganisationModel.DisplayName).ToLowerInvariant()} le \"Test\""),
                            Summary = "(<=)Less than or equals"
                        }
                    },
                    {
                        "contains", new OpenApiExample()
                        {
                            Description = "Advanced string filtering",
                            Value = new OpenApiString($"{nameof(OrganisationModel.DisplayName).ToLowerInvariant()}.StartsWith(\"1\")"),
                            Summary = "Starts with"
                        }
                    },
                }
            });
        }

        private static void RemoveAutoGeneratedDocumentation(OpenApiOperation operation, string[] paramNames)
        {
            var list = new List<OpenApiParameter>(operation.Parameters);
            list.RemoveAll(p => paramNames.Any(n => n.ToLowerInvariant().Equals(p.Name.ToLowerInvariant())));
            operation.Parameters = list;
        }
    }
}