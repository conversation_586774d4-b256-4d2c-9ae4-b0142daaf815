﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Commands.AppMasterData.UpdateAppMasterData;
using Axon.Core.Api.Commands.AppOrganisationSettings;
using Axon.Core.Api.Commands.AppOrganisationSettings.UpdateAppOrganisationSettings;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Models.AppSetting;
using Axon.Core.Api.Queries.AppOrganisationSettings;
using Axon.Core.Api.Services.Authorisation;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Axon.Core.Api.Controllers
{
    [ApiController]
    [Produces("application/json", "application/xml")]
    [Route("v{version:apiVersion}/Organisation/{orgCodeName}/app/{appCodeName}")]
    [ProducesResponseType(401, Type = typeof(CommandResponse))]
    [ProducesResponseType(403, Type = typeof(CommandResponse))]
    [ProducesResponseType(500, Type = typeof(CommandResponse))]
    [Authorize]
    public class AppOrganisationSettingsController : ApiControllerBase
    {
        private readonly IValidator<GetOrganisationAppSettingsQueryRequest> getOrganisationAppSettingsQueryRequestValidator;
        private readonly IValidator<GetOrganisationAppPublicSettingsQueryRequest> getOrganisationAppPublicSettingsQueryRequestValidator;
        private readonly IValidator<GetOrganisationAppMasterDataQueryRequest> getOrganisationAppMasterDataQueryRequestValidator;

        public AppOrganisationSettingsController(IMediator mediator, IValidator<GetOrganisationAppSettingsQueryRequest> getOrganisationAppSettingsQueryRequestValidator, IValidator<GetOrganisationAppPublicSettingsQueryRequest> getOrganisationAppPublicSettingsQueryRequestValidator, IValidator<GetOrganisationAppMasterDataQueryRequest> getOrganisationAppMasterDataQueryRequestValidator) : base(mediator)
        {
            this.getOrganisationAppSettingsQueryRequestValidator = getOrganisationAppSettingsQueryRequestValidator;
            this.getOrganisationAppPublicSettingsQueryRequestValidator = getOrganisationAppPublicSettingsQueryRequestValidator;
            this.getOrganisationAppMasterDataQueryRequestValidator = getOrganisationAppMasterDataQueryRequestValidator;
        }

        [HttpGet("settings", Name = "GetOrganisationAppSettings")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<SettingsValueModel[]>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse<SettingsValueModel[]>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [HasOrganisationPermissions(permissionsKey: nameof(CorePermissions.EditOrganisation))]
        public async Task<IActionResult> GetOrganisationAppSettings(string orgCodeName, string appCodeName)
        {
            var request = new GetOrganisationAppSettingsQueryRequest(orgCodeName, appCodeName);
            var validationResult = await getOrganisationAppSettingsQueryRequestValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpPut("settings/{settingName}", Name = "UpdateOrganisationAppSettings")]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
        [HasOrganisationPermissions(permissionsKey: nameof(CorePermissions.EditOrganisation))]
        public async Task<IActionResult> UpdateOrganisationAppSettings(string orgCodeName, string appCodeName, string settingName, [FromBody] AppOrganisationSettingsBody command)
        {
            return await Send(new UpdateAppOrganisationSettingsCommandRequest(orgCodeName, appCodeName, settingName, command.Value));
        }

        [HttpGet("publicSettings", Name = "GetOrganisationAppPublicSettings")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<SettingsValueModel[]>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse<SettingsValueModel[]>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [HasOrganisationPermissions(permissionsKey: nameof(CorePermissions.ViewOrganisation))]
        public async Task<IActionResult> GetOrganisationAppPublicSettings(string orgCodeName, string appCodeName)
        {
            var request = new GetOrganisationAppPublicSettingsQueryRequest(orgCodeName, appCodeName);
            var validationResult = await getOrganisationAppPublicSettingsQueryRequestValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpGet("masterData", Name = "GetOrganisationAppMasterData")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<MasterDataValueModel>>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse<IEnumerable<MasterDataValueModel>>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [HasOrganisationPermissions(permissionsKey: nameof(CorePermissions.EditOrganisation))]
        public async Task<IActionResult> GetOrganisationAppMasterData(string orgCodeName, string appCodeName)
        {
            var request = new GetOrganisationAppMasterDataQueryRequest(orgCodeName, appCodeName);
            var validationResult = await getOrganisationAppMasterDataQueryRequestValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpPut("masterdata/{dataType}", Name = "UpdateOrganisationAppMasterData")]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
        [HasOrganisationPermissions(permissionsKey: nameof(CorePermissions.EditOrganisation))]
        public async Task<IActionResult> UpdateOrganisationAppMasterData(string orgCodeName, string appCodeName, string dataType, [FromBody] AppOrganisationMasterDataBody command)
        {
            return await Send(new UpdateAppOrganisationMasterDataCommandRequest(orgCodeName, appCodeName, dataType, command.Selected));
        }
    }
}