﻿using Axon.Core.Api.Models.Role;
using MediatR;
using System.Collections.Generic;

namespace Axon.Core.Api.Commands.AppUser.Create
{
    public class UpdateAppUserCommand : IRequest<CommandResponse>
    {
        public UpdateAppUserCommand(string emailAddress, string appCodeName, string orgCodeName, string roleId, IReadOnlyCollection<ScopeResourcesBody> scopes)
        {
            EmailAddress = emailAddress;
            AppCodeName = appCodeName;
            OrgCodeName = orgCodeName;
            RoleId = roleId;
            Scopes = scopes;
        }

        public string EmailAddress { get; }
        public string AppCodeName { get; }
        public string OrgCodeName { get; }
        public string RoleId { get; }
        public IReadOnlyCollection<ScopeResourcesBody> Scopes { get; }
    }
}
