{{- $fullName := include "axon-core-api.fullname" . -}}
{{- $fullNameOvr :=  printf "%s-api" $fullName -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "axon-core-api.fullname" . }}-api
  labels:
    {{- include "axon-core-api.labels" . | nindent 4 }}
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    checkov.io/skip1: CKV_K8S_21=The namespace is being defined as helm argument
spec:
  tls:
    {{- range .Values.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .tlsSecretName }}
    {{- end }}
  rules:
    {{- range .Values.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path | default "/api/core/(.*)" }}
            pathType: {{ .pathType }}
            backend:
              service:
                name: {{ .svcName | default $fullNameOvr }}
                port:
                   {{- if .portNumber }}
                  number: {{ .portNumber }}
                  {{- else }}
                  name: {{ .portName | default "http"}}
                  {{- end }}
          {{- end }}
    {{- end }}