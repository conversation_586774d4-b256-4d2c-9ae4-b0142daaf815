﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.App.OrganisationIdQuery;

[UsedImplicitly]
public class GetAppListByOrganisationIdQueryRequestValidator : AbstractValidator<GetAppListByOrganisationIdQueryRequest>
{
    public GetAppListByOrganisationIdQueryRequestValidator()
    {
        RuleFor(x => x.Id)
            .MustBeAValidGuid();
    }
}