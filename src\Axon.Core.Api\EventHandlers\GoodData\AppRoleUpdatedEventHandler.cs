﻿using Axon.Core.Contracts;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services.GoodData;
using MassTransit;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;
using System.Linq;
using System.Threading.Tasks;

namespace Axon.Core.Api.EventHandlers.GoodData;

public class AppRoleUpdatedEventHandler(
    IMessageBus messageBus,
    IAppGroupRepository appGroupRepository,
    IAccessRepository accessRepository,
    IGoodDataService goodDataService,
    ILogger<AppRoleUpdatedEventHandler> logger)
    : IConsumer<AppRoleUpdatedEvent>
{
    public async Task Consume(ConsumeContext<AppRoleUpdatedEvent> context)
    {
        var message = context.Message;

        if (!await goodDataService.IsGoodDataReportingEnabled(message.AppId))
        {
            return;
        }

        if (message.Action is EventActionType.Created or EventActionType.Updated)
        {
            var accessEntities = await accessRepository.GetAccessItemsForRoleAsync(message.OrgCodeName, message.AppCodeName, message.RoleId);

            if (!accessEntities.Any())
            {
                logger.LogWarning("Access items for role {RoleId} do not exist.", message.RoleId);
            }

            foreach (var entity in accessEntities)
            {
                var appUserUpdatedEventMessage = new AppUserUpdatedEvent()
                {
                    Action = EventActionType.Updated,
                    AppCodeName = entity.AppCodeName,
                    AppId = entity.AppId,
                    OrgCodeName = entity.OrganisationCodeName,
                    OrgId = entity.OrganisationId,
                    UserId = entity.User.Id
                };
                await messageBus.PublishAsync(appUserUpdatedEventMessage);
            }

            var appGroupEntities = await appGroupRepository.GetAppGroupsByRoleIdAsync(message.OrgCodeName, message.AppCodeName, message.RoleId);

            if (!appGroupEntities.Any())
            {
                logger.LogWarning("App groups for role {RoleId} do not exist.", message.RoleId);
            }

            foreach (var entity in appGroupEntities)
            {
                var appGroupUpdatedEventMessage = new AppGroupUpdatedEvent()
                {
                    Action = EventActionType.Updated,
                    AppId = message.AppId,
                    AppCodeName = entity.AppCodeName,
                    OrgId = message.OrgId,
                    OrgCodeName = entity.OrganisationCodeName,
                    GroupId = entity.GroupId,
                };
                await messageBus.PublishAsync(appGroupUpdatedEventMessage);
            }
        }
    }
}