﻿using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;
using MediatR;

namespace Axon.Core.Api.Queries.AppGroup.GetById
{
    [UsedImplicitly]
    internal class GetAppGroupByIdQueryHandler : IRequestHandler<GetAppGroupByIdQueryRequest, CommandResponse<AppGroupModel>>
    {
        private readonly IAppGroupRepository appGroupRepository;
        private readonly IAccessRepository accessRepository;
        private readonly IMapper mapper;

        public GetAppGroupByIdQueryHandler(IAppGroupRepository appGroupRepository, IAccessRepository accessRepository, IMapper mapper)
        {
            this.appGroupRepository = appGroupRepository;
            this.accessRepository = accessRepository;
            this.mapper = mapper;
        }
        public async Task<CommandResponse<AppGroupModel>> Handle(GetAppGroupByIdQueryRequest request, CancellationToken cancellationToken)
        {
            var appGroupEntity = await appGroupRepository.GetItemAsync(request.Id);

            if (appGroupEntity == null)
            {
                return CommandResponse<AppGroupModel>.Failed(new Dictionary<string, string[]> { { nameof(request.Id), new[] { $"App Group with Id `{request.Id}` does not exist" } } }, HttpStatusCode.NotFound);
            }

            if (appGroupEntity.OrganisationCodeName != request.OrganisationCodeName || appGroupEntity.AppCodeName != request.AppCodeName)
            {
                return CommandResponse<AppGroupModel>.Failed(new Dictionary<string, string[]> { { nameof(request), new[] { $"App Group with Id `{request.Id}` does not belong to App `{request.AppCodeName}` within Organisation `{request.OrganisationCodeName}`" } } });
            }

            var appGroupModel = mapper.Map<AppGroupModel>(appGroupEntity);
            appGroupModel.MembersCount = await accessRepository.GetGroupMembersCountAsync(request.OrganisationCodeName, request.AppCodeName, appGroupEntity.GroupId);

            return CommandResponse<AppGroupModel>.Data(appGroupModel);
        }
    }
}
