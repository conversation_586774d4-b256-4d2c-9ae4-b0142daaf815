﻿using Axon.Core.Contracts;
using Axon.Core.Domain.Services.GoodData;
using MassTransit;
using Microsoft.Extensions.Logging;
using Phlex.Core.GoodData.Interfaces;
using System.Threading.Tasks;

namespace Axon.Core.Api.EventHandlers.GoodData;

public class OrgUpdatedEventHandler(
    IGoodData goodData,
    IGoodDataService goodDataService,
    ILogger<OrgUpdatedEventHandler> logger)
    : IConsumer<OrgUpdatedEvent>
{
    public async Task Consume(ConsumeContext<OrgUpdatedEvent> context)
    {
        var message = context.Message;

        var appCodeNames = await goodDataService.GetAppCodeNames(message.AppIds);
        if (appCodeNames.Length == 0)
        {
            logger.LogInformation("No GoodData Applications found for this organisation {OrgCodeName}.", message.OrgCodeName);
            return;
        }

        if (message.Action is EventActionType.Created or EventActionType.Updated)
        {
            foreach (var appCodeName in appCodeNames)
            {
                await goodData.CreateTenantWorkspaceAsync(appCodeName, message.OrgCodeName);
                logger.LogInformation("Tenant {Tenant} was successfully created in {Application} application in GoodData.", message.OrgCodeName, appCodeName);
            }
        }
        else if (message.Action is EventActionType.Deleted)
        {
            await goodData.DeleteTenantWorkspaceAsync(appCodeNames, message.OrgCodeName);
            logger.LogInformation("Tenant {Tenant} was successfully removed in {Applications} application in GoodData.", message.OrgCodeName, string.Join(", ", appCodeNames));
        }
    }
}
