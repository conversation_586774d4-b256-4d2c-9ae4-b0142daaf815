﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using MediatR;

namespace Axon.Core.Api.Queries.Organisation.CodeNameQuery
{
    public class GetOrganisationByCodeNameRequest : IRequest<CommandResponse<OrganisationModel>>
    {
        public string CodeName { get; }

        public GetOrganisationByCodeNameRequest(string codeName)
        {
            CodeName = codeName;
        }
    }
}
