﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models;
using MediatR;

namespace Axon.Core.Api.Queries.Organisation.ValidateOrganisation
{
    public class ValidateOrganisationQueryRequest : IRequest<CommandResponse<ValidationResultModel>>
    {
        public string Name { get; }
        public string CodeName { get; }
        public string Id { get; }

        public ValidateOrganisationQueryRequest(string id, string name, string codeName)
        {
            Name = name;
            CodeName = codeName;
            Id = id;
        }
    }
}