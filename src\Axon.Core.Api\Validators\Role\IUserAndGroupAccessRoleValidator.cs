﻿using Axon.Core.Api.Models.Role;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Api.Validators.Role
{
    public interface IUserAndGroupAccessRoleValidator
    {
        /// <summary>
        /// Validates that the provided role is suitable for assigning against a user / group for access purposes.
        /// If the role has enabled UserGroupScopes, then the provided scopes will be validated against this.
        /// Provided scopes will also have their resources checked against those available for the organisation.
        /// </summary>
        /// <param name="roleId">The role id that is wanting to be assigned to a user</param>
        /// <param name="appCodeName">The app the role is being granted against</param>
        /// <param name="orgCodeName">The organisation the role is being granted against</param>
        /// <param name="accessScopes">The scopes that are being set at the user/group level </param>
        /// <returns>True if the role is suitable to be used, false if it is not (along with a suitable message)</returns>
        Task<(bool valid, string message)> Validate(string roleId, string appCodeName, string orgCodeName, IReadOnlyCollection<ScopeResourcesBody> accessScopes);
    }
}