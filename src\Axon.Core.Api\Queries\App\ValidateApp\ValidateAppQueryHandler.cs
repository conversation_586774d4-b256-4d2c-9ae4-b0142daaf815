﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models;
using Axon.Core.Domain.Interfaces.Persistence;
using MediatR;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.App.ValidateApp
{
    internal class ValidateAppQueryHandler : IRequestHandler<ValidateAppQueryRequest, CommandResponse<ValidationResultModel>>
    {
        private readonly IAppRepository repository;
        public ValidateAppQueryHandler(IAppRepository repository)
        {
            this.repository = repository;
        }

        public async Task<CommandResponse<ValidationResultModel>> Handle(ValidateAppQueryRequest request, CancellationToken cancellationToken)
        {
            var entityByName = (await repository.GetItemsByDisplayNameAsync(request.Name)).AsEnumerable();
            var entityByCodeName = (await repository.GetItemsByAppCodeNameAsync(request.CodeName)).AsEnumerable();

            var errors = new Dictionary<string, string[]>();

            if (entityByName.Any(x => x.Id != request.Id))
                errors.Add(nameof(request.Name), new[] { $"Application `{request.Name}` already exists" });

            if (entityByCodeName.Any(x => x.Id != request.Id))
                errors.Add(nameof(request.CodeName), new[] { $"App with application CodeName `{request.CodeName}` already exists." });

            var isValid = errors.Count == 0;
            return CommandResponse<ValidationResultModel>.Data(
                new ValidationResultModel(isValid), 
                HttpStatusCode.OK, 
                errors);
        }
    }
}
