﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using Axon.Core.Infrastructure.Auth;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Api.Constants;

namespace Axon.Core.Api.Attributes;

/// <summary>
///     Base class for performing organisation scoped permission checks
/// </summary>
/// When app code is present, the permission check will still be made against axon-core unless useTargetApplicationPermissions is set to true.
/// E.g if permission is "ViewOrganisation", org code is "phlexglobal" and app code is "data-explorer" then a check is made to determine if
/// "data-explorer" is present and enabled on the organisation "phlexglobal", and then the permission check is made to check the user has ViewOrganisation permission against
/// the organisation "phlexglobal".
/// If however the useTargetApplicationPermissions flag is set to true, then the permission check is made to check the user has ViewOrganisation permission against
/// the organisation "phlexglobal" and application data-explorer
/// </remarks>
public abstract class HasOrganisationPermissionBaseAttribute : HasPermissionsAttribute
{
    private readonly string appCodeNamePathKey;
    private readonly bool useTargetApplicationPermissions;

    protected HasOrganisationPermissionBaseAttribute(string permissionsKey, string appCodeNameUrlIdentifier = "appCodeName", bool useTargetApplicationPermissions = false) : base(permissionsKey)
    {
        this.appCodeNamePathKey = appCodeNameUrlIdentifier;
        this.useTargetApplicationPermissions = useTargetApplicationPermissions;
    }

    public new async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        await Authorise(context);
    }

    public new void OnAuthorization(AuthorizationFilterContext context)
    {
        Authorise(context).Wait();
    }

    protected override async Task Authorise(AuthorizationFilterContext context)
    {
        (var _, var accessGate, var authenticationSchemeProvider, var logger) = GetStandardServices<HasOrganisationPermissionBaseAttribute>(context);
        (var validatedClaims, var errorResponse) = TryGetStandardClaims(context, logger);

        if (validatedClaims == null)
        {
            context.Result = errorResponse;
            return;
        }

        var orgIdentifier = GetOrgIdentifier(context);
        if (string.IsNullOrEmpty(orgIdentifier))
        {
            logger.LogDebug("Auth failed: cannot find the organisation id in the route");
            context.Result = new UnauthorizedResult();
            return;
        }

        var appCodeName = context.RouteData.Values[appCodeNamePathKey] as string;

        if (appCodeName == null && useTargetApplicationPermissions) 
        {
            logger.LogDebug("Auth failed: cannot determine target application by path");
            context.Result = new UnauthorizedResult();
            return;
        }

        if (!await PerformPermissionCheck(accessGate, logger, useTargetApplicationPermissions ? appCodeName : ClaimsExtensions.AxonCoreAppName, orgIdentifier, Permission))
        {
            context.Result = new ForbidResult(authenticationSchemeProvider.AllSchemes());
        }
    }

    protected abstract string GetOrgIdentifier(AuthorizationFilterContext context);

    protected abstract Task<bool> PerformPermissionCheck(IAccessGate accessGate, ILogger logger, string appCodeName, string orgIdentifier, string permission);
}
