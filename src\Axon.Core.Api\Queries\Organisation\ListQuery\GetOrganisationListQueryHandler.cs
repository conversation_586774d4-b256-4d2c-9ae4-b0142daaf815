﻿using System;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using JetBrains.Annotations;
using MediatR;
using Axon.Core.Api.Models.Organisation;
using System.Collections.Generic;
using System.Threading;
using Axon.Core.Api.Services.Organisation;

namespace Axon.Core.Api.Queries.Organisation.ListQuery
{
    [UsedImplicitly]
    internal class GetOrganisationListQueryHandler : IRequestHandler<GetOrganisationQueryRequest, CommandResponse<IEnumerable<OrganisationModel>>>
    {
        private readonly IOrganisationRequestService listService;

        public GetOrganisationListQueryHandler(IOrganisationRequestService listService)
        {
            this.listService = listService;
        }

        public async Task<CommandResponse<IEnumerable<OrganisationModel>>> Handle(GetOrganisationQueryRequest request, CancellationToken cancellationToken)
        {
            request.ListParams.Filter = String.IsNullOrEmpty(request.ListParams.Filter) ? "IsDeleted eq false and IsEnabled eq true" : request.ListParams.Filter + " and IsDeleted eq false and IsEnabled eq true";


            return await listService.GetFilteredOrganisations(request.ListParams);
        }
    }
}