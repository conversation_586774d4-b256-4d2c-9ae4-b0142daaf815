﻿using Axon.Core.Api.Models.Role;
using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;
using System.Linq;

namespace Axon.Core.Api.Commands.Role.Create
{
    [UsedImplicitly]
    public class CreateRoleBodyValidator : BaseCommandValidator<CreateRoleBody>
    {
        public CreateRoleBodyValidator()
        {
            RuleFor(o => o.OrganisationCodeName)
                .MustBeAValidCodeName()
                .NotEmpty();
            RuleFor(o => o.AppCodeName)
                .MustBeAValidCodeName()
                .NotEmpty();
            RuleFor(o => o.RoleBody).NotNull();
            When(o => o.RoleBody != null, () =>
            {
                RuleFor(o => o.RoleBody.RoleName)
                    .MustBeAValidRoleName();
            });
            When(o => o.RoleBody != null && o.RoleBody.UserGroupScopes != null, () =>
            {
                RuleFor(o => o.RoleBody.UserGroupScopes)
               .Must(x => x.Length < 2).WithMessage("No more than one UserGroupScope can be selected");
            });
           
        }
    }
}
