apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "axon-core-api.fullname" . }}-api
  labels:
    {{- include "axon-core-api.labels" . | nindent 4 }}
  annotations:
    checkov.io/skip1: CKV_K8S_21=The namespace is being defined as helm argument
    checkov.io/skip2: CKV_K8S_14=The tag is being defined as helm argument
    checkov.io/skip3: CKV_K8S_43=We cannot use digest since these are regenerated on every image
    checkov.io/skip4: CKV_K8S_8=There is no liveness probe for this app
    checkov.io/skip5: CKV_K8S_9=There is no readiness probe for this app
    checkov.io/skip6: CKV_K8S_35=There are no secrets defined as env vars

spec:
  replicas: {{ .Values.replicas }}
  selector:
    matchLabels:
      {{- include "axon-core-api.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.api.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "axon-core-api.selectorLabels" . | nindent 8 }}
        azure.workload.identity/use: 'true'
    spec:
      {{- with .Values.api.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      automountServiceAccountToken: false
      securityContext:
        {{- toYaml .Values.api.podSecurityContext | nindent 8 }}
      serviceAccountName: {{ include "axon-core-api.fullname" . }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.api.securityContext | nindent 12 }}
          image: "{{ .Values.api.repository }}:{{ .Values.api.tag }}"
          imagePullPolicy: {{ .Values.api.pullPolicy }}
          livenessProbe:
            httpGet:
              path: /health/liveness
              port: http
              scheme: HTTP
            initialDelaySeconds: 60
            failureThreshold: 5
            periodSeconds: 30
            timeoutSeconds: 3
          readinessProbe:
            httpGet:
              path: /health/readiness
              port: http
              scheme: HTTP
            initialDelaySeconds: 60
            failureThreshold: 5
            periodSeconds: 30
            timeoutSeconds: 3
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          env:
            - name: NEW_RELIC_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  name: newreliclicensekey
                  key: KEY
            - name: NEW_RELIC_APP_NAME
              value: {{ .Values.newrelic_api_app_name }}
            - name: ASPNETCORE_ENVIRONMENT
              value: {{ .Values.aspNetCoreEnvironment }}
            - name: ASPNETCORE_URLS
              value: "http://+:8080"
            - name: AzureAd__ClientId
              value: {{ .Values.clientId }}
            - name: KeyVaultName
              value: {{ .Values.keyVaultName }}
            - name: EntraSettings__Issuer
              value: {{.Values.azureIssuer}}
            - name: EntraSettings__UseCustomRefresh
              value: "{{.Values.azureUseCustomRefresh}}"
            - name: AzureBlobStorage__StorageConnectionString
              value: {{ .Values.BlobStorageConnectionString }}
            - name: MessageBus__AzureServiceBus__Namespace
              value: {{ .Values.NamespaceName }}
            - name: cors__origins__0__uri
              value: {{ .Values.corsOriginUrl0 }}
            - name: cors__origins__1__uri
              value: {{ .Values.corsOriginUrl1 }}
            - name: AzureIdentity__ManageIdentityClientId
              value: {{ .Values.managedIdentityClientId }}
            - name: Gigya__ClientId
              value: {{ .Values.gigyaClientId}}
            - name: Gigya__Issuer
              value: {{ .Values.gigyaIssuer}}
            - name: Gigya__UseCustomRefresh
              value: "{{.Values.gigyaUseCustomRefresh}}"
            - name: AZURE_FEDERATED_TOKEN_FILE
              value: "/var/run/secrets/azure/tokens/azure-identity-token"
            - name: ConnectionStrings__Axon-Core-ApiDb__EndpointUrl
              value:  {{ .Values.cosmosdbUrl }}
            - name: ConnectionStrings__Axon-Core-ApiDb__DatabaseName
              value:  {{ .Values.cosmosdbName }}
            - name: AzureBlobStorage__Containers__AppAvatars__ContainerName
              value: {{ .Values.AzureBlobStorageContainersAppAvatarsContainerName }}
            - name: AzureBlobStorage__Containers__AppAvatars__FolderPrefix
              value: {{ .Values.AzureBlobStorageContainersAppAvatarsFolderPrefix }}
            - name: AzureBlobStorage__Containers__OrganisationAvatars__ContainerName
              value: {{ .Values.AzureBlobStorageContainersOrganisationAvatarsContainerName }}
            - name: AzureBlobStorage__Containers__OrganisationAvatars__FolderPrefix
              value: {{ .Values.AzureBlobStorageContainersOrganisationAvatarsFolderPrefix }}
            - name: AzureBlobStorage__Containers__Themes__ContainerName
              value: {{ .Values.AzureBlobStorageContainersThemesContainerName }}
            - name: AzureBlobStorage__Containers__Themes__FolderPrefix
              value: {{ .Values.AzureBlobStorageContainersThemesFolderPrefix }}
            - name: AxonCoreShared__DataProtection__StorageUri
              value: {{ .Values.DataProtectionBlobStorageUri }}
            - name: AxonCoreShared__DataProtection__KeyVaultKey
              value: {{ .Values.DataProtectionKeyVaultKey }}
            - name: GoodData__BaseUri
              value: {{ .Values.GoodDataBaseUri }}
            - name: GoodData__Environment
              value: {{ .Values.GoodDataEnvironment }}
          resources:
            {{- toYaml .Values.api.resources | nindent 12 }}
      {{- with .Values.api.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.api.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.api.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
