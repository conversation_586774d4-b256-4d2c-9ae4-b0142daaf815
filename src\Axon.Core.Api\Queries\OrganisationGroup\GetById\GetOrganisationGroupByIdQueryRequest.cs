﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Shared.Api;
using MediatR;

namespace Axon.Core.Api.Queries.OrganisationGroup.GetById;

public class GetOrganisationGroupByIdQueryRequest : IRequest<CommandResponse<OrganisationGroupPagedModel>>
{
    public string OrganisationCodeName { get; }
    public string OrganisationGroupId { get; }
    public ListParams ListParams { get; }

    public GetOrganisationGroupByIdQueryRequest(string organisationCodeName, string organisationGroupId, ListParams listParams)
    {
        OrganisationCodeName = organisationCodeName;
        OrganisationGroupId = organisationGroupId;
        ListParams = listParams;
    }
}
