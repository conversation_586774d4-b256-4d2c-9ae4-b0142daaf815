﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;
using System.Collections.Generic;
using Axon.Core.Shared.Api;

namespace Axon.Core.Api.Queries.AppAccess.OrganisationUsersQuery
{
    public class GetOrganisationUsersQueryRequest : IRequest<CommandResponse<ApiPagedListResult<OrganisationUserModel>>>
    {
        public string OrgCodeName { get; }
        public ListParams ListParams { get; }
        public string Embed { get; }

        public GetOrganisationUsersQueryRequest(string orgCodeName, ListParams listParams, string embed)
        {
            OrgCodeName = orgCodeName;
            ListParams = listParams;
            Embed = embed;
        }
    }
}
