﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.AppOrganisationSettings.UpdateAppOrganisationSettings;

[UsedImplicitly]
public class UpdateAppOrganisationSettingsCommandRequestValidator : AbstractValidator<UpdateAppOrganisationSettingsCommandRequest>
{
    public UpdateAppOrganisationSettingsCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.SettingName)
            .MustBeAValidSettingName();
        RuleFor(x => x.Value)
            .NotEmpty().WithMessage("Value cannot be empty.");
    }
}