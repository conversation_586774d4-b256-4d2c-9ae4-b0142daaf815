﻿using Axon.Core.Domain.Extensions;
using Axon.Core.Domain.Interfaces.Access;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using CommunityToolkit.Diagnostics;

namespace Axon.Core.Api.Attributes
{
    /// <inheritdoc cref="HasOrganisationPermissionBaseAttribute"/>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
    public class HasOrganisationByIdPermissionsAttribute : HasOrganisationPermissionBaseAttribute
    {
        public readonly string OrgIdPathKey;

        public HasOrganisationByIdPermissionsAttribute(string permissionsKey,
                                                       string appCodeNameUrlIdentifier = "appCodeName",
                                                       string orgIdUrlIdentifier = "id") : base(permissionsKey, appCodeNameUrlIdentifier)
        {
            Guard.IsNotNullOrEmpty(orgIdUrlIdentifier);

            OrgIdPathKey = orgIdUrlIdentifier;
        }

        protected override string GetOrgIdentifier(AuthorizationFilterContext context)
        {
            return context.RouteData.Values[OrgIdPathKey] as string;
        }

        protected override async Task<bool> PerformPermissionCheck(IAccessGate accessGate, ILogger logger, string appCodeName, string orgIdentifier, string permission)
        {
            if (!await accessGate.IsAuthorisedForOrganisationById(appCodeName, orgIdentifier, permission))
            {
                logger.LogDebug("Auth failed: For user, permission (`{Permission}`) against org: `{OrgIdentifier}` and app: `{AppCodeName}`",  permission, orgIdentifier, appCodeName);
                return false;
            }

            return true;
        }
    }
}
