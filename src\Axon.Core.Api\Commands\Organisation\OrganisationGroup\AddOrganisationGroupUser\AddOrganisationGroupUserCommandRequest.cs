﻿using MediatR;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.AddOrganisationGroupUser;

public class AddOrganisationGroupUserCommandRequest : IRequest<CommandResponse>
{
    public string OrganisationCodeName { get; }
    public string GroupId { get; }
    public AddOrganisationGroupUserCommandRequestBody AddOrganisationGroupUserBody { get; }

    public AddOrganisationGroupUserCommandRequest(string organisationCodeName, string groupId, AddOrganisationGroupUserCommandRequestBody addOrganisationGroupUserBody)
    {
        OrganisationCodeName = organisationCodeName;
        GroupId = groupId;
        AddOrganisationGroupUserBody = addOrganisationGroupUserBody;
    }
}
