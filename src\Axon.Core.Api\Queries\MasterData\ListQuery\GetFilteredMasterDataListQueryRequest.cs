using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.MasterData;
using MediatR;
using System.Collections.Generic;

namespace Axon.Core.Api.Queries.MasterData.ListQuery;

public class GetFilteredMasterDataListQueryRequest : IRequest<CommandResponse<IEnumerable<MasterDataModel>>>
{
    public string DataType { get; }

    public GetFilteredMasterDataListQueryRequest(string dataType)
    {
        DataType = dataType;
    }
}