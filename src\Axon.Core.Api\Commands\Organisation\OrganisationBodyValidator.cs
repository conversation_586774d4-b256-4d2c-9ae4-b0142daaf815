using Axon.Core.Api.Models.Organisation;
using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation
{
    [UsedImplicitly]
    public class OrganisationBodyValidator : BaseCommandValidator<OrganisationBody>
    {
        public OrganisationBodyValidator()
        {
            RuleFor(x => x.CodeName)
                .NotEmpty().WithMessage("{PropertyName} cannot be empty")
                .MustBeAValidCodeName();
            RuleFor(x => x.DisplayName)
                .MustBeAValidDisplayName();
            RuleFor(x => x.Description)
                .MustBeAValidDescription();
            RuleFor(x => x.Icon)
                .MustBeAValidUri();
            RuleFor(x => x.ParentOrganisationId)
                .MustBeAValidGuid();

            RuleForEach(model => model.Apps)
                .SetValidator(new SetupAppBodyValidator());

            RuleFor(x => x.Theme.DefaultColours.Highlights)
                .MustBeAValidColour().When(x => x.Theme != null);
            RuleFor(x => x.Theme.DefaultColours.Buttons)
                .MustBeAValidColour().When(x => x.Theme != null);
            RuleFor(x => x.Theme.DefaultColours.Hover)
                .MustBeAValidColour().When(x => x.Theme != null);
            RuleFor(x => x.Theme.DarkColours.Highlights)
                .MustBeAValidColour().When(x => x.Theme != null);
            RuleFor(x => x.Theme.DarkColours.Buttons)
                .MustBeAValidColour().When(x => x.Theme != null);
            RuleFor(x => x.Theme.DarkColours.Hover)
                .MustBeAValidColour().When(x => x.Theme != null);
        }

        public class SetupAppBodyValidator : AbstractValidator<SetupAppBody>
        {
            public SetupAppBodyValidator()
            {
                RuleFor(x => x.AppId)
                    .MustBeAValidGuid();
            }
        }
    }
}
