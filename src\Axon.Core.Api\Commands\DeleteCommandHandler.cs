using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Domain.Entities.Base;
using Axon.Core.Domain.Exceptions;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using MediatR;
using Phlex.Core.FunctionalExtensions.Results;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands
{
    internal class DeleteCommandHandler<TEntity, TModel, TMessage> : IRequestHandler<DeleteCommandRequest<TModel>, Result> 
        where TEntity : BaseEntity
        where TMessage : class
    {
        protected IMessageBus MessageBus { get; }
        protected readonly IRepository<TEntity> Repo;
        protected readonly IMapper Mapper;

        public DeleteCommandHandler(IRepository<TEntity> repo, IMapper mapper, IMessageBus messageBus)
        {
            Guard.IsNotNull(messageBus);
            MessageBus = messageBus;
            Guard.IsNotNull(repo);
            Repo = repo;
            Guard.IsNotNull(mapper);
            Mapper = mapper;
        }

        public virtual async Task<Result> Handle(DeleteCommandRequest<TModel> request, CancellationToken cancellationToken)
        {
            var entity = await Repo.GetItemAsync(request.Id);
            if (entity == null)
            {
                throw new EntityNotFoundException(nameof(TEntity), request.Id);
            }

            await Repo.DeleteItemAsync(request.Id);

            await SendMessage(entity);
            
            return Result.Success();
        }
        protected virtual async Task SendMessage(TEntity org)
        {

            var msg = Mapper.Map<TMessage>(org);

            await MessageBus.PublishAsync(msg);
        }
    }

}