/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// SettingsValueModel
    /// </summary>
    [DataContract(Name = "SettingsValueModel")]
    public partial class SettingsValueModel : IValidatableObject
    {

        /// <summary>
        /// Gets or Sets DataType
        /// </summary>
        [DataMember(Name = "dataType", IsRequired = true, EmitDefaultValue = true)]
        public SettingDataType DataType { get; set; }
        /// <summary>
        /// Initializes a new instance of the <see cref="SettingsValueModel" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected SettingsValueModel() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="SettingsValueModel" /> class.
        /// </summary>
        /// <param name="settingName">settingName (required).</param>
        /// <param name="category">category (required).</param>
        /// <param name="displayName">displayName (required).</param>
        /// <param name="description">description (required).</param>
        /// <param name="dataType">dataType (required).</param>
        /// <param name="value">value (required).</param>
        /// <param name="dataTypeOptions">dataTypeOptions.</param>
        /// <param name="isKeyVault">isKeyVault (required).</param>
        /// <param name="isMultiple">isMultiple (required).</param>
        /// <param name="isPublic">isPublic (required).</param>
        public SettingsValueModel(string settingName = default(string), string category = default(string), string displayName = default(string), string description = default(string), SettingDataType dataType = default(SettingDataType), Object value = default(Object), List<string> dataTypeOptions = default(List<string>), bool isKeyVault = default(bool), bool isMultiple = default(bool), bool isPublic = default(bool))
        {
            // to ensure "settingName" is required (not null)
            if (settingName == null)
            {
                throw new ArgumentNullException("settingName is a required property for SettingsValueModel and cannot be null");
            }
            this.SettingName = settingName;
            // to ensure "category" is required (not null)
            if (category == null)
            {
                throw new ArgumentNullException("category is a required property for SettingsValueModel and cannot be null");
            }
            this.Category = category;
            // to ensure "displayName" is required (not null)
            if (displayName == null)
            {
                throw new ArgumentNullException("displayName is a required property for SettingsValueModel and cannot be null");
            }
            this.DisplayName = displayName;
            // to ensure "description" is required (not null)
            if (description == null)
            {
                throw new ArgumentNullException("description is a required property for SettingsValueModel and cannot be null");
            }
            this.Description = description;
            this.DataType = dataType;
            // to ensure "value" is required (not null)
            if (value == null)
            {
                throw new ArgumentNullException("value is a required property for SettingsValueModel and cannot be null");
            }
            this.Value = value;
            this.IsKeyVault = isKeyVault;
            this.IsMultiple = isMultiple;
            this.IsPublic = isPublic;
            this.DataTypeOptions = dataTypeOptions;
        }

        /// <summary>
        /// Gets or Sets SettingName
        /// </summary>
        [DataMember(Name = "settingName", IsRequired = true, EmitDefaultValue = true)]
        public string SettingName { get; set; }

        /// <summary>
        /// Gets or Sets Category
        /// </summary>
        [DataMember(Name = "category", IsRequired = true, EmitDefaultValue = true)]
        public string Category { get; set; }

        /// <summary>
        /// Gets or Sets DisplayName
        /// </summary>
        [DataMember(Name = "displayName", IsRequired = true, EmitDefaultValue = true)]
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or Sets Description
        /// </summary>
        [DataMember(Name = "description", IsRequired = true, EmitDefaultValue = true)]
        public string Description { get; set; }

        /// <summary>
        /// Gets or Sets Value
        /// </summary>
        [DataMember(Name = "value", IsRequired = true, EmitDefaultValue = true)]
        public Object Value { get; set; }

        /// <summary>
        /// Gets or Sets DataTypeOptions
        /// </summary>
        [DataMember(Name = "dataTypeOptions", EmitDefaultValue = true)]
        public List<string> DataTypeOptions { get; set; }

        /// <summary>
        /// Gets or Sets IsKeyVault
        /// </summary>
        [DataMember(Name = "isKeyVault", IsRequired = true, EmitDefaultValue = true)]
        public bool IsKeyVault { get; set; }

        /// <summary>
        /// Gets or Sets IsMultiple
        /// </summary>
        [DataMember(Name = "isMultiple", IsRequired = true, EmitDefaultValue = true)]
        public bool IsMultiple { get; set; }

        /// <summary>
        /// Gets or Sets IsPublic
        /// </summary>
        [DataMember(Name = "isPublic", IsRequired = true, EmitDefaultValue = true)]
        public bool IsPublic { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class SettingsValueModel {\n");
            sb.Append("  SettingName: ").Append(SettingName).Append("\n");
            sb.Append("  Category: ").Append(Category).Append("\n");
            sb.Append("  DisplayName: ").Append(DisplayName).Append("\n");
            sb.Append("  Description: ").Append(Description).Append("\n");
            sb.Append("  DataType: ").Append(DataType).Append("\n");
            sb.Append("  Value: ").Append(Value).Append("\n");
            sb.Append("  DataTypeOptions: ").Append(DataTypeOptions).Append("\n");
            sb.Append("  IsKeyVault: ").Append(IsKeyVault).Append("\n");
            sb.Append("  IsMultiple: ").Append(IsMultiple).Append("\n");
            sb.Append("  IsPublic: ").Append(IsPublic).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            // SettingName (string) minLength
            if (this.SettingName != null && this.SettingName.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for SettingName, length must be greater than 1.", new [] { "SettingName" });
            }

            // Category (string) minLength
            if (this.Category != null && this.Category.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for Category, length must be greater than 1.", new [] { "Category" });
            }

            // DisplayName (string) minLength
            if (this.DisplayName != null && this.DisplayName.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for DisplayName, length must be greater than 1.", new [] { "DisplayName" });
            }

            // Description (string) minLength
            if (this.Description != null && this.Description.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for Description, length must be greater than 1.", new [] { "Description" });
            }

            yield break;
        }
    }

}
