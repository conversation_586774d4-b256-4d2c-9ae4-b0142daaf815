﻿using System;
using System.Threading.Tasks;
using Axon.Core.Contracts;
using MassTransit;

namespace Axon.Core.Api.EventHandlers;

public class ScopeDeletedEventHandler : IConsumer<ScopeDeletedEvent>
{

    public async Task Consume(ConsumeContext<ScopeDeletedEvent> context)
    {
        //Future piece of work will decide what needs to happen to permissions using scopes etc when they are deleted
        throw new NotImplementedException();
    }
}