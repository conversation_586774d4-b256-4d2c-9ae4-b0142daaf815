using System;
using System.Linq;
using AutoMapper;
using Axon.Core.Api.Commands.App;
using Axon.Core.Api.Commands.App.UpdateAppConfig;
using Axon.Core.Api.Commands.Organisation;
using Axon.Core.Api.Commands.UserPreference;
using Axon.Core.Api.Models.App;
using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Api.Models.AppUser;
using Axon.Core.Api.Models.MasterData;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Api.Models.OrganisationAccess;
using Axon.Core.Api.Models.Role;
using Axon.Core.Api.Models.ScopeResource;
using Axon.Core.Api.Models.UserIdentityProvider;
using Axon.Core.Api.Models.UserPreference;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Shared.Authorisation;
using JetBrains.Annotations;
using static Axon.Core.Domain.Entities.OrganisationEntity;

namespace Axon.Core.Api.Mappers;

[UsedImplicitly]
public class CoreAutoMapperProfile : Profile
{
    public CoreAutoMapperProfile()
    {
        // Core maps for handlers
        CreateMap<AppEntity, AppModel>();
        CreateMap<AppBody, AppEntity>()
            .ForMember(dest => dest.Url, opt => opt.MapFrom((src, dest) => dest.Url ?? $"apps/{src.AppCodeName}"));
        CreateMap<UpdateAppConfigBody, AppEntity>();

        CreateMap<OrganisationEntity, OrganisationModel>()
            .ForMember(m => m.ShowAudits, c => c.Ignore());
        CreateMap<OrganisationBody, OrganisationEntity>();
        CreateMap<UpdateOrganisationBody, OrganisationEntity>()
            .ForMember(dest => dest.NormalizedDisplayName, opt => opt.MapFrom(src => src.DisplayName.ToLower()));

        CreateMap<UserPreferenceEntity, UserPreferencesModel>();
        CreateMap<UserPreferenceBody, UserPreferenceEntity>();

        CreateMap<UserEntity, ListItemModel>();

        CreateMap<RoleEntity, RoleModel>()
            .ForMember(m => m.Permissions, opt => opt.MapFrom(dest => dest.Permissions.ToList()));
        CreateMap<RoleEntity.Permission, RoleModel.Permission>()
            .ForMember(m => m.IsInherited, c => c.Ignore());
        CreateMap<RoleEntity.ScopeResources, RoleModel.ScopeResources>()
                        .AfterMap((source, destination) => destination.Resources = source.Resources == null ? null : destination.Resources); //AutoMapper by default sets null collections to empty collections on mapping. Override this here.
        CreateMap<RoleEntity.Resource, RoleModel.Resource>();
        CreateMap<RoleEntity.UserGroupScope, RoleModel.UserGroupScope>();

        CreateMap<RoleDefinitionEntity.ValidScope, ValidScopeBody>();

        CreateMap<RoleDefinitionEntity.Permission, AppRoleDefinitionPermission>()
            .ForMember(m => m.ValidScopes, opt => opt.MapFrom(dest => dest.ValidScopes.ToList()));

        CreateMap<CreateRoleBody, RoleEntity>()
            .BeforeMap((_, d) =>
            {
                d.RoleType = RoleType.Custom;
                d.InheritRoleId = null;
            })
            .ForMember(x => x.RoleName, opt => opt.MapFrom(dest => dest.RoleBody.RoleName))
            .ForMember(x => x.IsEnabled, opt => opt.MapFrom(dest => dest.RoleBody.IsEnabled))
            .ForMember(x => x.Permissions, opt => opt.MapFrom(dest => dest.RoleBody.Permissions))
            .ForMember(x => x.UserGroupScopes, opt => opt.MapFrom(dest => dest.RoleBody.UserGroupScopes));

        CreateMap<PermissionBody, RoleEntity.Permission>();
        CreateMap<ScopeResourcesBody, RoleEntity.ScopeResources>()
            .AfterMap((source, destination) => destination.Resources = source.Resources == null ? null : destination.Resources); //AutoMapper by default sets null collections to empty collections on mapping. Override this here.
        CreateMap<ResourceBody, RoleEntity.Resource>();
        CreateMap<UserGroupScopeBody, RoleEntity.UserGroupScope>();

        CreateMap<CreateAppGroupRequest, AppGroupEntity>()
            .BeforeMap((_, d) =>
            {
                d.CreatedAt = DateTime.UtcNow;
                d.LastUpdatedDate = DateTime.UtcNow;
            })
            .ForMember(x => x.GroupId, opt => opt.MapFrom(dest => dest.CreateAppGroupBody.GroupId))
            .ForMember(x => x.GroupName, opt => opt.MapFrom(dest => dest.CreateAppGroupBody.GroupName))
            .ForMember(x => x.RoleId, opt => opt.MapFrom(dest => dest.CreateAppGroupBody.RoleId))
            .ForMember(x => x.Role, opt => opt.MapFrom(dest => dest.CreateAppGroupBody.RoleName))
            .ForMember(x => x.Scopes, opt => opt.MapFrom(dest => dest.CreateAppGroupBody.Scopes));
        CreateMap<AppGroupEntity, AppGroupModel>()
            .ForMember(m => m.MembersCount, c => c.Ignore());

        CreateMap<GroupEntity, UnassignedGroupModel>()
            .ForMember(x => x.GroupName, opt => opt.MapFrom(dest => dest.Name));

        // Message mappers
        CreateMap<AppEntity, AppCreated>();
        CreateMap<AppEntity, AppUpdated>();
        CreateMap<AppEntity, AppDeleted>();

        CreateMap<OrganisationEntity, OrganisationCreated>();
        CreateMap<OrganisationEntity, OrganisationUpdated>();
        CreateMap<OrganisationEntity, OrganisationDeleted>();

        CreateMap<OrganisationEntity, OrgUpdatedEvent>()
            .ForMember(m => m.OrgCodeName, c => c.MapFrom(src => src.CodeName))
            .ForMember(m => m.OrgId, c => c.MapFrom(src => src.Id))
            .ForMember(m => m.AppIds, c => c.MapFrom(src => src.Apps.Select(x => x.AppId)));

        CreateMap<OrganisationBody, OrganisationEntity>()
            .ForMember(m => m.Apps, c => c.Ignore());
        CreateMap<SetupThemeBody, ThemeConfigEntity>()
            .ForMember(m => m.AvatarUrl, c => c.Ignore())
            .ForMember(m => m.HeaderUrl, c => c.Ignore())
            .ForMember(m => m.StylesheetUrl, c => c.Ignore());
        CreateMap<OrgColour, OrgColourEntity>();

        CreateMap<UserPreferenceEntity, UserPreferenceCreated>();
        CreateMap<UserPreferenceEntity, UserPreferenceUpdated>();

        CreateMap<IdentityProviderEntity, UserIdentityProviderModel>();


        CreateMap<AppEntity, AppOrganisationModel>();
        CreateMap<AppOrganisationBody, AppEntity>();

        CreateMap<AppConfigEntity, AppConfigModel>().ReverseMap();
        CreateMap<AppThemeConfigEntity, AppThemeConfigModel>().ReverseMap();
        CreateMap<ThemeConfigEntity, ThemeConfigModel>().ReverseMap();
        CreateMap<OrgColourEntity, OrgColourModel>().ReverseMap();

        CreateMap<AccessEntity, AppUserGroupModel>();
        CreateMap<GroupEntity, AppUserGroupModel>()
            .ForMember(m => m.GroupName, opt => opt.MapFrom(src => src.Name));

        CreateMap<ScopeResourceEntity, ScopeResourceModel>();

        CreateMap<MasterDataEntity, MasterDataModel>();

        CreateMap<RoleEntity, AppRoleUpdatedEvent>()
            .ForMember(m => m.RoleId, opt => opt.MapFrom(src => src.Id))
            .ForMember(m => m.OrgCodeName, opt => opt.MapFrom(src => src.OrganisationCodeName));

        CreateMap<AppGroupEntity, AppGroupUpdatedEvent>()
            .ForMember(m => m.OrgCodeName, opt => opt.MapFrom(src => src.OrganisationCodeName));

        CreateMap<AccessEntity, AppUserUpdatedEvent>()
            .ForMember(m => m.OrgCodeName, opt => opt.MapFrom(src => src.OrganisationCodeName))
            .ForMember(m => m.OrgId, opt => opt.MapFrom(src => src.OrganisationId))
            .ForMember(m => m.UserId, opt => opt.MapFrom(src => src.User.Id))
            .ForMember(m => m.OrgId, opt => opt.MapFrom(src => src.OrganisationId));
    }
}