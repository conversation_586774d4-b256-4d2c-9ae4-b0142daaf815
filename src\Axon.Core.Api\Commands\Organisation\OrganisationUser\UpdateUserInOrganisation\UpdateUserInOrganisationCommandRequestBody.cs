﻿using JetBrains.Annotations;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser.UpdateUserInOrganisation;

public class UpdateUserInOrganisationCommandRequestBody(string name, string email, string status, string[] groups = null, GroupOrganisation[] childOrganisations = null)
{
    [Required]
    public string Name { get; } = name;
    [Required]
    public string Email { get; } = email;
    [Required]
    public string Status { get; } = status;

    [CanBeNull]
    public string[] Groups { get; } = groups;
    [CanBeNull]
    public GroupOrganisation[] ChildOrganisations { get; } = childOrganisations;
}