﻿using Axon.Core.Api.Commands;
using MediatR;
using System.Collections.Generic;
using Axon.Core.Api.Models.Role;

namespace Axon.Core.Api.Queries.Role.ListQuery
{
    public class GetAllRoleListQueryRequest : IRequest<CommandResponse<IEnumerable<RoleModel>>>
    {
        public string OrganisationCodeName { get; }
        public string AppCodeName { get; }

        public GetAllRoleListQueryRequest(string organisationCodeName, string appCodeName)
        {
            OrganisationCodeName = organisationCodeName;
            AppCodeName = appCodeName;
        }
    }
}
