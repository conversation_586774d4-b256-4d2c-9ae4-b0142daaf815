using System;
using System.Linq.Expressions;
using FluentValidation;

namespace Axon.Core.Api.Commands
{
    public abstract class BaseCommandValidator<TSource> : AbstractValidator<TSource>
    {
        protected void RuleForWellFormedUrls(Expression<Func<TSource, string>> urlProvider)
        {
            RuleFor(urlProvider)
                .NotEmpty()
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out _))
                .WithMessage("Malformed url");
        }
    }
}