﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Axon.Core.Api.Commands.App
{
    public class AppProperties
    {
        public string Id { get; set; }
        public string DisplayName { get; set; }
        public string Description { get; set; }
        public string Icon { get; set; }
        public string Url { get; set; }
        public string AppCodeName { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsEnabled { get; set; }
        public bool IsSystemApp { get; set; }
        public string Status { get; set; }
        public bool IsDefault { get; }

    }
    public class AppOrganisationBody
    {
        public string Id { get; set; }
        public string DisplayName { get; set; }
        public string Description { get; set; }
        public string Icon { get; set; }
        public string Url { get; set; }
        public string AppCodeName { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsEnabled { get; set; }
        public bool IsSystemApp { get; set; }
        public string Status { get; set; }
        public bool IsDefault { get; }

        public AppOrganisationBody(AppProperties appProps)
        {
            DisplayName = appProps.DisplayName;
            Description = appProps.Description;
            Icon = appProps.Icon;
            Url = appProps.Url;
            AppCodeName = appProps.AppCodeName;
            IsDeleted = appProps.IsDeleted;
            IsEnabled = appProps.IsEnabled;
            IsSystemApp = appProps.IsSystemApp;
            Status = appProps.Status;
            IsDefault = appProps.IsDefault;
        }
    }
}
