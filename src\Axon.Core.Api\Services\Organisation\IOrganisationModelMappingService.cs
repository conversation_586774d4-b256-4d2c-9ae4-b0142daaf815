﻿using Axon.Core.Api.Models.Organisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Models.Access;
using System.Collections.Generic;

namespace Axon.Core.Api.Services.Organisation
{
    /// <summary>
    /// Mapping between an <see cref="OrganisationEntity"/> and a <see cref="OrganisationModel"/>
    /// requires permissions knowledge, to set the CanManage, ViewAudits and CanAccess flags.
    /// This interface wraps this behaviour up so it is usable by anything that returns an OrganisationModel.
    /// This does use IMapper underneath fundamentally, but extends on it by setting the extra flag properties.
    /// </summary>
    public interface IOrganisationModelMappingService
    {
        /// <summary>
        /// Maps an organistion for a user
        /// </summary>
        /// <param name="entity">The organisation to map</param>
        /// <param name="orgAxonPermissions">The users permission for axon-core against that organistion</param>
        /// <param name="appPermissions">The users permissions for all apps against the organistion</param>
        /// <returns>A mapped <see cref="OrganisationModel"/></returns>
        OrganisationModel MapForUser(OrganisationEntity entity,
                                     UserEffectivePermissionsForAppAndOrg orgAxonPermissions,
                                     IReadOnlyCollection<UserEffectivePermissionsForAppAndOrg> appPermissions);

        /// <summary>
        /// Maps an organiston for an app
        /// </summary>
        /// <param name="entity">The organisation to map</param>
        /// <returns>A mapped <see cref="OrganisationModel"/></returns>
        OrganisationModel MapForApp(OrganisationEntity entity);
    }
}
