﻿using Axon.Core.Domain.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Domain.Interfaces.Persistence
{
    public interface IAppGroupRepository : IRepository<AppGroupEntity>
    {
        Task<int> GetTotalItemsCountAsync(string orgCodeName, string appCodeName, object listParams);
        Task<IList<AppGroupEntity>> GetAppGroupsAsync(string orgCodeName, string appCodeName, object listParams);
        Task<IList<AppGroupEntity>> GetAppGroupsAsync(string orgCodeName, string appCodeName);
        Task<IList<AppGroupEntity>> GetAppGroupsByRoleIdAsync(string orgCodeName, string appCodeName, string roleId);
        Task<IList<AppGroupEntity>> GetAppGroupsByGroupIdAsync(string groupId);
        Task<bool> AppGroupExistsAsync(string orgCodeName, string appCodeName, string groupId);

        /// <summary>
        /// Finds which of the provided resources are in use
        /// </summary>
        /// <param name="orgCodeName">Organisation code</param>
        /// <param name="appCodeName">Application Code</param>
        /// <param name="scope">Scope to find resources for</param>
        /// <param name="resources">Resources to find</param>
        /// <returns>The set of provided resources that are in use for the provided organisation / application</returns>
        Task<IReadOnlyCollection<RoleEntity.Resource>> GetInUseResources(string orgCodeName, string appCodeName, string scope, IReadOnlyCollection<RoleEntity.Resource> resources);
    }
}
