﻿using Axon.Core.Api.Commands;
using CommunityToolkit.Diagnostics;
using MediatR;
using Microsoft.Extensions.Logging;
using Phlex.Core.GoodData.Interfaces;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.GoodData;

internal class GetWorkspaceWithDashboardsQueryHandler : IRequestHandler<GetWorkspaceWithDashboardsQueryRequest, CommandResponse>
{
    private readonly IGoodData goodDataService;
    private readonly ILogger<GetWorkspaceWithDashboardsQueryHandler> logger;

    public GetWorkspaceWithDashboardsQueryHandler(IGoodData goodDataService, ILogger<GetWorkspaceWithDashboardsQueryHandler> logger)
    {
        Guard.IsNotNull(goodDataService);
        this.goodDataService = goodDataService;

        Guard.IsNotNull(logger);
        this.logger = logger;
    }

    public async Task<CommandResponse> Handle(GetWorkspaceWithDashboardsQueryRequest request, CancellationToken cancellationToken)
    {
        try
        {
            var workspaceDetails = await goodDataService.GetWorkspaceIdsAndDashboardsAsync(request.AppCodeName, request.OrgCodeName ?? string.Empty);

            if (workspaceDetails == null)
            {
                return CommandResponse.NotFound(nameof(workspaceDetails), $"Workspace details not found for application '{request.AppCodeName}' and organisation '{request.OrgCodeName}'");
            }

            return CommandResponse.Data(workspaceDetails);
        }
        catch (System.Exception ex)
        {
            logger.LogError(ex, "Unable to retrieve workspace details.");
            return CommandResponse.Failed($"{nameof(request.AppCodeName)}/{nameof(request.OrgCodeName)}", $"Unable to retrieve workspace details for this application `{request.AppCodeName}` and this organisation `{request.OrgCodeName}`.");
        }
    }
}
