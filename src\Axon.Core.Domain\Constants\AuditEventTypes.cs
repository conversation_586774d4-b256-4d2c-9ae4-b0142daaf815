﻿namespace Axon.Core.Domain.Constants
{
    public static class AuditEventTypes
    {
        public const string UserLogout = "User:Logout";
        public const string UserAutoLogout = "User:AutoLogout";
        public const string UserLogin = "User:Login";
        public const string UserPrincipalLogin = "UserPrinciple:Login";
        public const string UserCreated = "User:Created";
        public const string UserUpdated = "User:Updated";
        public const string UserAssigned = "User:Assigned";
        public const string UserUnassigned = "User:Unassigned";

        public const string OrganisationCreated = "Organisation:CreateOrganisation";
        public const string OrganisationUpdated = "Organisation:UpdateOrganisation";

        public const string RoleCreated = "Role:CreateRole";
        public const string RoleUpdated = "Role:UpdateRole";
        public const string RoleDeleted = "Role:DeleteRole";

        public const string RoleDefinitionUpdated = "RoleDefinition:UpdateRoleDefinition";

        public const string GroupCreated = "Group:Created";
        public const string GroupUpdated = "Group:Updated";
        public const string GroupDeleted = "Group:Deleted";

        public const string AppGroupCreated = "AppGroup:CreateAppGroup";
        public const string AppGroupUpdated = "AppGroup:UpdateAppGroup";
        public const string AppGroupDeleted = "AppGroup:DeleteAppGroup";

        public const string AccessCreated = "Access:CreateAccess";
        public const string AccessUpdated = "Access:UpdateAccess";
        public const string AccessDeleted = "Access:DeleteAccess";
    }
}
