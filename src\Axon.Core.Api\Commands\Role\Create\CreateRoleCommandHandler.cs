﻿using AutoMapper;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Phlex.Core.MessageBus;
using System.Threading.Tasks;
using System.Threading;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.Core.Domain.Constants;
using Axon.Core.Api.Models.Role;
using Axon.Core.Api.Validators.Role;
using Axon.Core.Infrastructure.Cosmos.Repository;
using System;
using Axon.Core.Domain.Extensions;


namespace Axon.Core.Api.Commands.Role.Create
{
    internal class CreateRoleCommandHandler : BaseCreateCommandHandler<CreateRoleBody, RoleEntity, AppRoleUpdatedEvent>
    {
        private readonly IRoleBodyValidator rolePermissionBodyValidator;
        private readonly IClientDetailsProvider clientDetailsProvider;
        private readonly ICorrelationIdProvider correlationIdProvider;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAppRepository appRepository;
        private readonly IAuditService<TenantAuditExtensions> auditService;

        public CreateRoleCommandHandler(
            IRoleBodyValidator rolePermissionBodyValidator,
            IRoleRepository repo, 
            IMapper mapper, 
            IMessageBus messageBus,
            IClientDetailsProvider clientDetailsProvider,
            ICorrelationIdProvider correlationIdProvider,
            IOrganisationRepository organisationRepository,
            IAppRepository appRepository,
            IAuditService<TenantAuditExtensions> auditService) 
            : base(repo, mapper, messageBus)
        {
            this.rolePermissionBodyValidator = rolePermissionBodyValidator;
            this.clientDetailsProvider = clientDetailsProvider;
            this.correlationIdProvider = correlationIdProvider;
            this.auditService = auditService;
            this.organisationRepository = organisationRepository;
            this.appRepository = appRepository;
        }

        public override async Task<CommandResponse> Handle(CreateCommandRequest<CreateRoleBody> request, CancellationToken cancellationToken)
        {
            var roleRepository = (IRoleRepository)Repo;

            var existing = await roleRepository.RoleNameExistsAsync(request.Model.OrganisationCodeName, request.Model.AppCodeName, request.Model.RoleBody.RoleName);
            if (existing)
            {
                return CommandResponse.Failed(nameof(request.Model.RoleBody.RoleName), $"Role `{request.Model.RoleBody.RoleName}` already exists");
            }

            var rolePermissionsValidationResult = await rolePermissionBodyValidator.IsValid(request.Model.OrganisationCodeName, request.Model.AppCodeName, request.Model.RoleBody);
            if (!rolePermissionsValidationResult.isValid)
            {
                return CommandResponse.Failed(nameof(request.Model.RoleBody.Permissions), rolePermissionsValidationResult.errorMessage);
            }

            var entity = new RoleEntity();
            var correlationId = correlationIdProvider.Provide();
            var clientDetails = clientDetailsProvider.Provide();

            var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Role, AuditEventDescriptions.RoleCreated, correlationId, clientDetails, request.Model.OrganisationCodeName);

            await auditService.LogAsync(AuditEventTypes.RoleCreated, tenantAuditExtensions, entity,
                async () =>
                {
                    Mapper.Map(request.Model, entity);

                    await Repo.AddItemAsync(entity);
                });

            var mappedEvent = Mapper.Map<AppRoleUpdatedEvent>(entity);
            var org = await organisationRepository.GetItemByCodeNameAsync(request.Model.OrganisationCodeName);
            if (org == default(OrganisationEntity))
            {
                return CommandResponse.Failed(nameof(request.Model.OrganisationCodeName), $"Organisation `{request.Model.OrganisationCodeName}` does not exist");
            }
            var appId = await appRepository.GetAppIdByAppCode(request.Model.AppCodeName);
            if (appId == default(string))
            {
                return CommandResponse.NotFound(nameof(AppEntity), request.Model.AppCodeName);
            }

            mappedEvent.OrgId = org.Id;
            mappedEvent.AppId = appId;
            mappedEvent.Action = EventActionType.Created;

            await MessageBus.PublishAsync(mappedEvent, cancellationToken: cancellationToken);

            return CommandResponse.Created(nameof(RoleEntity), entity.Id);
        }
    }
}
