api:
  repository: phlexglobal.azurecr.io/axon-core-api
  pullPolicy: Always
  tag: latest

  resources:
    limits:
      cpu: 600m
      memory: 1Gi
    requests:
      cpu: 200m
      memory: 100Mi

  podAnnotations: {}
  imagePullSecrets: {}

  podSecurityContext:
    seccompProfile:
      type: RuntimeDefault
    runAsUser: 65534
    runAsGroup: 65534
    runAsNonRoot: true

  securityContext:
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true
    capabilities:
      drop:
        - NET_RAW
        - ALL
    runAsNonRoot: true
    runAsUser: 65534
    runAsGroup: 65534

  nodeSelector:
    kubernetes.io/os: linux

  affinity: {}
  tolerations: {}

grpc:
  repository: phlexglobal.azurecr.io/axon-core-grpc
  pullPolicy: Always
  tag: latest

  resources:
    limits:
      cpu: 600m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 400Mi

  podAnnotations: {}
  imagePullSecrets: {}

  podSecurityContext:
    seccompProfile:
      type: RuntimeDefault
    runAsUser: 65534
    runAsGroup: 65534
    runAsNonRoot: true

  securityContext:
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true
    capabilities:
      drop:
        - NET_RAW
        - ALL
    runAsNonRoot: true
    runAsUser: 65534
    runAsGroup: 65534

  nodeSelector:
    kubernetes.io/os: linux

  affinity: {}
  tolerations: {}

nameOverride: "axon-core-api"
fullnameOverride: "axon-core-api"

replicas: 1

ingress:
  tls:
    - tlsSecretName: tls-app-dev-smartphlex-com
      hosts:
        - app-dev.smartphlex.com
  hosts:
    - host: app-dev.smartphlex.com
      paths:
        - path: /api/core/(.*)
          pathType: ImplementationSpecific

scaledObject:
  # -- Minimum number of replicas that the scaled object will create
  minReplicas: 1
  # -- Maximum number of replicas that the scaled object will create
  maxReplicas: 5
  # -- This is the polling interval to check each trigger on for the Scaled object type. By default its 30 seconds.
  pollingInterval: 30
  # -- The period to wait after the last trigger reported active before scaling the deployment back to 0. By default it’s 5 minutes (300 seconds).
  cooldownPeriod: 300
  # -- The CPU percentage value to scale up on
  utilisation: 70

gigyaClientId: cUDSdI53tU5LgmVJH2AkCH-8
gigyaIssuer: https://tst.aaas.cencora.com/oidc/op/v1.0/4_Pv18t6XTOc51PxyYytQzHA/authorize
gigyaUseCustomRefresh: true


azureIssuer: https://login.microsoftonline.com/common/v2.0
azureUseCustomRefresh: false


keyVaultName: axon-dev-kv-eun-core
aspNetCoreEnvironment: Production

cosmosdbName: Axon-Core-ApiDb

newreliclicensekey: "#{newreliclicensekey}#"

managedIdentityClientId: b3133a36-766f-4ff4-bf56-6216a8241770
azureWorkload:
  clientId: b3133a36-766f-4ff4-bf56-6216a8241770
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AzureBlobStorageContainersAppAvatarsContainerName: axon
AzureBlobStorageContainersOrganisationAvatarsContainerName: axon
AzureBlobStorageContainersThemesContainerName: axon
