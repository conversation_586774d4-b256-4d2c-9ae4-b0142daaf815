﻿using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Contracts;
using Axon.Core.Domain.Interfaces.Persistence;
using MassTransit;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.EventHandlers;

public class DeleteScopeCommandHandler : IConsumer<DeleteScopeCommand>
{
    private readonly IMessageBus messageBus;
    private readonly IScopeResourceRepository scopeResourceRepository;

    public DeleteScopeCommandHandler(IMessageBus messageBus, IScopeResourceRepository scopeResourceRepository)
    {
        this.messageBus = messageBus;
        this.scopeResourceRepository = scopeResourceRepository;
    }

    public async Task Consume(ConsumeContext<DeleteScopeCommand> context)
    {
        var message = context.Message;

        var scopeResources = await scopeResourceRepository.GetScopeResourcesAsync(message.AppCodeName, message.OrganisationCodeName, message.Scope);

        var scope = scopeResources.FirstOrDefault(x =>
            x.ResourceId == message.ResourceId);

        if (scope is null)
            return;

        await scopeResourceRepository.DeleteItemAsync(scope.Id);

        var scopeDeletedEvent = new ScopeDeletedEvent(message.AppCodeName, message.OrganisationCodeName, message.ResourceId, message.ResourceName);
        await messageBus.PublishAsync(scopeDeletedEvent);
    }
}