﻿using Axon.Core.Api.Models.Role;
using System.Collections.Generic;

namespace Axon.Core.Api.Models.AppGroup
{
    public class UpdateAppGroupBody
    {
        public string RoleId { get; set; }
        public string RoleName { get; set; }
        public IReadOnlyCollection<ScopeResourcesBody> Scopes { get; set; }
        public UpdateAppGroupBody(string roleId, string roleName, IReadOnlyCollection<ScopeResourcesBody> scopes)
        {
            RoleId = roleId;
            RoleName = roleName;
            Scopes = scopes;
        }
    }
}
