﻿using Microsoft.AspNetCore.Mvc;

namespace Axon.Core.Api.Models.AppGroup
{
    public class CreateAppGroupRequest
    {
        [FromRoute, BindProperty(Name = "orgCodeName")] public string OrganisationCodeName { get; set; }
        [FromRoute, BindProperty(Name = "appCodeName")] public string AppCodeName { get; set; }
        [FromBody] public CreateAppGroupBody CreateAppGroupBody { get; set; }
    }
}
