﻿using Axon.Core.Api.Commands;
using Axon.Core.Shared.Audit;
using MediatR;
using Microsoft.AspNetCore.OData.Query;

namespace Axon.Core.Api.Queries.Audit;

public class GetOrganisationAuditsQueryRequest : IRequest<CommandResponse>
{
    public string OrgCodeName { get; }
    public ODataQueryOptions<TenantAudit> OdataOptions { get; }

    public GetOrganisationAuditsQueryRequest(string orgCodeName, ODataQueryOptions<TenantAudit> options)
    {
        OrgCodeName = orgCodeName;
        OdataOptions = options;
    }
}