/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// ValidateCurrentUserPermissionsAccessQueryResponse
    /// </summary>
    [DataContract(Name = "ValidateCurrentUserPermissionsAccessQueryResponse")]
    public partial class ValidateCurrentUserPermissionsAccessQueryResponse : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ValidateCurrentUserPermissionsAccessQueryResponse" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected ValidateCurrentUserPermissionsAccessQueryResponse() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="ValidateCurrentUserPermissionsAccessQueryResponse" /> class.
        /// </summary>
        /// <param name="anyAccess">anyAccess (required).</param>
        /// <param name="allAccess">allAccess (required).</param>
        /// <param name="permissions">permissions (required).</param>
        public ValidateCurrentUserPermissionsAccessQueryResponse(bool anyAccess = default(bool), bool allAccess = default(bool), Dictionary<string, bool> permissions = default(Dictionary<string, bool>))
        {
            this.AnyAccess = anyAccess;
            this.AllAccess = allAccess;
            // to ensure "permissions" is required (not null)
            if (permissions == null)
            {
                throw new ArgumentNullException("permissions is a required property for ValidateCurrentUserPermissionsAccessQueryResponse and cannot be null");
            }
            this.Permissions = permissions;
        }

        /// <summary>
        /// Gets or Sets AnyAccess
        /// </summary>
        [DataMember(Name = "anyAccess", IsRequired = true, EmitDefaultValue = true)]
        public bool AnyAccess { get; set; }

        /// <summary>
        /// Gets or Sets AllAccess
        /// </summary>
        [DataMember(Name = "allAccess", IsRequired = true, EmitDefaultValue = true)]
        public bool AllAccess { get; set; }

        /// <summary>
        /// Gets or Sets Permissions
        /// </summary>
        [DataMember(Name = "permissions", IsRequired = true, EmitDefaultValue = true)]
        public Dictionary<string, bool> Permissions { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class ValidateCurrentUserPermissionsAccessQueryResponse {\n");
            sb.Append("  AnyAccess: ").Append(AnyAccess).Append("\n");
            sb.Append("  AllAccess: ").Append(AllAccess).Append("\n");
            sb.Append("  Permissions: ").Append(Permissions).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
