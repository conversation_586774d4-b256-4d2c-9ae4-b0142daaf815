using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Shared.Api;
using CommunityToolkit.Diagnostics;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.Core.Api.Controllers;

public abstract class ApiControllerBase : ControllerBase
{
    public readonly ListParams ListParams;
    protected readonly IMediator Mediator;

    protected ApiControllerBase(IMediator mediator)
    {
        Guard.IsNotNull(mediator);
        Mediator = mediator;
        ListParams = new ListParams();
    }

    /// <summary>
    ///     Send a request without specified result model.
    /// </summary>
    /// <param name="request">Command or query model</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    protected async Task<IActionResult> Send(IRequest<Result> request, CancellationToken ct = default)
    {
        return CreateResult(await Mediator.Send(request, ct));
    }

    /// <summary>
    ///     Send a request with the specified result model.
    /// </summary>
    /// <param name="request">Command or query model</param>
    /// <param name="ct">Cancellation token</param>
    protected async Task<IActionResult> Send<TResponse>(IRequest<TResponse> request, CancellationToken ct = default)
    {
        return CreateResult(await Mediator.Send(request, ct));
    }

    private IActionResult CreateResult<TResponse>(TResponse response)
    {
        return response switch
        {
            ICommandResponse cr => CommandResponseResult(response, cr),
            Result result => result.IsSuccess
                ? Ok()
                : CreateErrorResult(result),
            _ => throw new SystemException("Unsupported response type.")
        };
    }

    private static IActionResult CommandResponseResult<TResponse>(TResponse response, ICommandResponse commandResponse)
    {
        return new JsonResult(response)
        {
            StatusCode = (int) commandResponse.status
        };
    }

    private static IActionResult CreateErrorResult(Result result)
    {
        return new JsonResult(CommandResponse.Failed(new Dictionary<string, string[]>
        {
            {"Error", new[] {result.Error.Message}}
        }, HttpStatusCode.InternalServerError))
        {
            StatusCode = (int) HttpStatusCode.InternalServerError
        };
    }
}