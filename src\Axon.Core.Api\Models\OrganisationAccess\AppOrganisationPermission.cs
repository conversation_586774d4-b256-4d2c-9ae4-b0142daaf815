﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.OrganisationAccess;

public class AppOrganisationPermission
{
    private AppOrganisationPermission(string permission, bool hasScopes, bool? permitted, IReadOnlyCollection<AppOrganisationPermissionScope> scopes = null)
    {
        Permission = permission;
        Permitted = permitted;
        Scopes = scopes;
        HasScopes = hasScopes;
    }

    [Required]
    public string Permission { get; }
    [Required]
    public bool HasScopes { get; }
    [Required]
    public bool? Permitted { get; }
    [Required]
    public IReadOnlyCollection<AppOrganisationPermissionScope> Scopes { get; }

    public static AppOrganisationPermission CreateGeneralPermission(string permission, bool? permitted)
        => new(permission, false, permitted, Array.Empty<AppOrganisationPermissionScope>());

    public static AppOrganisationPermission CreateScopedPermission(string permission, bool? permitted, IReadOnlyCollection<AppOrganisationPermissionScope> scopes)
        => new(permission, true, permitted, scopes);
}
