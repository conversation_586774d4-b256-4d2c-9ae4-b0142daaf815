﻿using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.User.GetUserByEmail;

[UsedImplicitly]
public class GetUserByEmailQueryRequestValidator : AbstractValidator<GetUserByEmailQueryRequest>
{
    public GetUserByEmailQueryRequestValidator()
    {
        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email cannot be empty.")
            .EmailAddress().WithMessage("Invalid email address format.");
    }
}