﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Validators.AppOrganisationSettings;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using JetBrains.Annotations;
using MediatR;

namespace Axon.Core.Api.Commands.AppMasterData.UpdateAppMasterData;

[UsedImplicitly]
internal class UpdateAppOrganisationMasterDataCommandHandler : IRequestHandler<UpdateAppOrganisationMasterDataCommandRequest, CommandResponse>
{
    private readonly IAppOrganisationSettingsRepository appOrganisationSettingsRepository;
    private readonly IAppSettingsRepository appSettingsRepository;
    private readonly IMasterDataSelectionValidator masterDataSelectionValidator;

    public UpdateAppOrganisationMasterDataCommandHandler(
        IAppSettingsRepository appSettingsRepository,
        IAppOrganisationSettingsRepository appOrganisationSettingsRepository,
        IMasterDataSelectionValidator masterDataSelectionValidator)
    {
        Guard.IsNotNull(appSettingsRepository);
        this.appSettingsRepository = appSettingsRepository;
        Guard.IsNotNull(appOrganisationSettingsRepository);
        this.appOrganisationSettingsRepository = appOrganisationSettingsRepository;
        this.masterDataSelectionValidator = masterDataSelectionValidator;
    }

    public async Task<CommandResponse> Handle(UpdateAppOrganisationMasterDataCommandRequest request, CancellationToken cancellationToken)
    {
        var appSettings = await appSettingsRepository.GetByAppCodeNameAsync(request.AppCodeName);

        if (appSettings == null)
        {
            return CommandResponse.NotFound(nameof(AppSettingsEntity), $"{request.AppCodeName}");
        }

        if (appSettings.MasterData == null || !appSettings.MasterData.TryGetValue(request.MasterDataType, out _))
        {
            return CommandResponse.NotFound("MasterDataType", $"{request.MasterDataType}");
        }

        var convertedSelected = request.Selected.Select(sm => new AppOrganisationSettingsEntity.AppOrganisationSelectedMasterData { Id = sm.Id, Name = sm.Name }).ToList();

        (var isValid, var errorMessage) = await masterDataSelectionValidator.Validate(request.MasterDataType, convertedSelected);

        if (!isValid)
        {
            return CommandResponse.BadRequest(nameof(AppSettingsEntity), errorMessage);
        }

        return await SaveInDatabase(request.OrganisationCodeName, request.AppCodeName, request.MasterDataType, convertedSelected);
    }

    private async Task<CommandResponse> SaveInDatabase(string organisationCodeName, string appCodeName, string masterDataType, IReadOnlyCollection<AppOrganisationSettingsEntity.AppOrganisationSelectedMasterData> selectedMasterData)
    {
        var appOrgSettings = await appOrganisationSettingsRepository.GetAppOrganisationSettingsAsync(organisationCodeName, appCodeName);

        if (appOrgSettings == null)
        {
            var entity = new AppOrganisationSettingsEntity
            {
                AppCodeName = appCodeName,
                OrganisationCodeName = organisationCodeName,
                Settings = new Dictionary<string, object>(),
                MasterData = new Dictionary<string, IReadOnlyCollection<AppOrganisationSettingsEntity.AppOrganisationSelectedMasterData>>
                {
                    { masterDataType, selectedMasterData}
                }
            };

            await appOrganisationSettingsRepository.AddItemAsync(entity);

            return CommandResponse.Created(nameof(AppOrganisationSettingsEntity), entity.Id);
        }

        if(appOrgSettings.MasterData == null)
        {
            appOrgSettings.MasterData = new Dictionary<string, IReadOnlyCollection<AppOrganisationSettingsEntity.AppOrganisationSelectedMasterData>>
                                            {
                                                { masterDataType, selectedMasterData }
                                            };
        }
        else if (appOrgSettings.MasterData.TryGetValue(masterDataType, out var existingMasterData))
        {
            (var isValid, var errorMessage) = await masterDataSelectionValidator.ValidateUpdate(appCodeName, organisationCodeName, masterDataType, existingMasterData, selectedMasterData);

            if (!isValid)
            {
                return CommandResponse.BadRequest(nameof(AppSettingsEntity), errorMessage);
            }

            appOrgSettings.MasterData[masterDataType] = selectedMasterData;
        }
        else
        {
            appOrgSettings.MasterData.Add(masterDataType, selectedMasterData);
        }

        await appOrganisationSettingsRepository.UpdateItemAsync(appOrgSettings.Id, appOrgSettings);

        return CommandResponse.Success();
    }
}