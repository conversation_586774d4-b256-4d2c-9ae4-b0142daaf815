﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Audit;
using Axon.Core.Api.Queries.Audit;
using Axon.Core.Infrastructure.AppSettings;
using Axon.Core.Infrastructure.IoC;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace Axon.Core.Api.Services.Audit
{
    public interface IAuditEndpointService
    {
        Task<CommandResponse<object>> GetOData(GetAuditsQueryRequest request);
        Task<CommandResponse<AuditFilters>> GetFilters(GetAuditFiltersQueryRequest request);
        Task<CommandResponse<string>> GetOdataUrl(GetAuditsUrlQueryRequest request);
        Task<CommandResponse<string>> GetODataFilterUrl(GetAuditFiltersUrlQueryRequest request);
    }

    public class AuditEndpointService : IAuditEndpointService
    {
        private readonly ILogger<AuditEndpointService> logger;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly AuditSettings auditOptions;
        private readonly IAuditEndpointHttpClient auditHttpClient;

        public AuditEndpointService(ILogger<AuditEndpointService> logger, IHttpContextAccessor httpContextAccessor, AuditSettings auditOptions, IAuditEndpointHttpClient auditHttpClient)
        {
            this.logger = logger;
            this.httpContextAccessor = httpContextAccessor;
            this.auditOptions = auditOptions;
            this.auditHttpClient = auditHttpClient;
        }

        public async Task<CommandResponse<string>> GetOdataUrl(GetAuditsUrlQueryRequest request)
        {
            var odataEndpointUrl = GetOdataEndpointWithoutQueryString(request.Host, request.AppCodeName, request.OrgCodeName);
            if (string.IsNullOrEmpty(odataEndpointUrl))
            {
                return CommandResponse<string>.Failed(new Dictionary<string, string[]>() { { "ODataUrl", new[] { "Error: Unable to get the Odata endpoint url" } } });
            }
            return CommandResponse<string>.Data(data: odataEndpointUrl);
        }

        public async Task<CommandResponse<string>> GetODataFilterUrl(GetAuditFiltersUrlQueryRequest request)
        {
            var odataEndpointURL = GetFilterEndpoint(request.Host, request.AppCodeName, request.OrgCodeName);
            if (string.IsNullOrEmpty(odataEndpointURL))
            {
                return CommandResponse<string>.Failed(new Dictionary<string, string[]>() { { "FilterUrl", new[] { "Error: Unable to get the Filter endpoint url" } } });
            }

            return CommandResponse<string>.Data(data: odataEndpointURL);
        }

        public async Task<CommandResponse<object>> GetOData(GetAuditsQueryRequest request)
        {
            var odataEndpointURL = GetOdataEndpoint(request.Host, request.AppCodeName, request.OrgCodeName, request.ODataOptions);
            if (string.IsNullOrEmpty(odataEndpointURL))
            {
                return CommandResponse<object>.Failed(new Dictionary<string, string[]>() { { "ODataUrl", new[] { "Error: Unable to get the Odata endpoint url" } } });
            }

            return await MakeRequest<object>(odataEndpointURL);
        }

        public async Task<CommandResponse<AuditFilters>> GetFilters(GetAuditFiltersQueryRequest request)
        {
            var odataEndpointURL = GetFilterEndpoint(request.Host, request.AppCodeName, request.OrgCodeName);
            if (string.IsNullOrEmpty(odataEndpointURL))
            {
                return CommandResponse<AuditFilters>.Failed(new Dictionary<string, string[]>() { { "FilterUrl", new[] { "Error: Unable to get the Filter endpoint url" } } });
            }

            return await MakeRequest<AuditFilters>(odataEndpointURL);
        }

        private string GetOdataEndpoint(string host, string appCodeName, string orgCodeName, QueryString odataOptions)
        {
            if (appCodeName == "axon-core")
            {
                if (string.IsNullOrEmpty(auditOptions.AxonCoreOdataEndpointUrlTemplate))
                {
                    logger.LogError("AxonCoreOdataEndpointUrlTemplate has not been provided");
                    return null;
                }
                return $"{PopulateUrlTemplate(auditOptions.AxonCoreOdataEndpointUrlTemplate, host, appCodeName, orgCodeName)}{odataOptions.Value}";
            }

            if (auditOptions.TestOdata.UseTestEndpoint)
            {
                if (!string.IsNullOrEmpty(auditOptions.TestOdata.TestEndpointOdataUrl))
                {
                    return $"{auditOptions.TestOdata.TestEndpointOdataUrl}{odataOptions.Value}";
                }
                else
                {
                    logger.LogError("TestEndpointOdataUrl has not been provided");
                    return null;
                }
            }

            if (!string.IsNullOrEmpty(auditOptions.OdataEndpointUrlTemplate))
            {
                return $"{PopulateUrlTemplate(auditOptions.OdataEndpointUrlTemplate, host, appCodeName, orgCodeName)}{odataOptions.Value}";
            }
            else
            {
                logger.LogError("OdataEndpointUrlTemplate url has not been provided");
                return null;
            }
        }

        private string GetOdataEndpointWithoutQueryString(string host, string appCodeName, string orgCodeName)
        {
            if (appCodeName == "axon-core")
            {
                if (string.IsNullOrEmpty(auditOptions.AxonCoreOdataEndpointUrlTemplate))
                {
                    logger.LogError("AxonCoreOdataEndpointUrlTemplate has not been provided");
                    return null;
                }
                return $"{PopulateUrlTemplate(auditOptions.AxonCoreOdataEndpointUrlTemplate, host, appCodeName, orgCodeName)}";
            }

            if (auditOptions.TestOdata.UseTestEndpoint)
            {
                if (!string.IsNullOrEmpty(auditOptions.TestOdata.TestEndpointOdataUrl))
                {
                    return $"{auditOptions.TestOdata.TestEndpointOdataUrl}";
                }
                else
                {
                    logger.LogError("TestEndpointOdataUrl has not been provided");
                    return null;
                }
            }

            if (!string.IsNullOrEmpty(auditOptions.OdataEndpointUrlTemplate))
            {
                return $"{PopulateUrlTemplate(auditOptions.OdataEndpointUrlTemplate, host, appCodeName, orgCodeName)}";
            }
            else
            {
                logger.LogError("OdataEndpointUrlTemplate url has not been provided");
                return null;
            }
        }

        private string GetFilterEndpoint(string host, string appCodeName, string orgCodeName)
        {

            if (auditOptions.TestOdata.UseTestEndpoint)
            {
                if (!string.IsNullOrEmpty(auditOptions.TestOdata.TestEndpointFilterUrl))
                {
                    return auditOptions.TestOdata.TestEndpointFilterUrl;
                }
                else
                {
                    logger.LogError("TestEndpointFilterUrl has not been provided");
                    return null;
                }
            }

            if (!string.IsNullOrEmpty(auditOptions.FilterEndpointUrlTemplate))
            {
                return PopulateUrlTemplate(auditOptions.FilterEndpointUrlTemplate, host, appCodeName, orgCodeName);
            }
            else
            {
                logger.LogError("FilterEndpointUrlTemplate url has not been provided");
                return null;
            }
        }

        private string PopulateUrlTemplate(string template, string host, string appCodeName, string orgCodeName)
        {
            return template.Replace("{env}", host)
                           .Replace("{app}", appCodeName)
                           .Replace("{tenant}", orgCodeName);
        }

        private async Task<CommandResponse<T>> MakeRequest<T>(string url)
        {
            string authToken = httpContextAccessor.HttpContext.Request.Headers["Authorization"];
            if (string.IsNullOrEmpty(authToken))
            {
                logger.LogError("Error Unable to get Authorization from headers to pass through.");
                return CommandResponse<T>.Failed(new Dictionary<string, string[]>() { { "Data", new[] { "Error: Unable to get auth token" } } }, status: HttpStatusCode.InternalServerError);
            }

            var request = new HttpRequestMessage(HttpMethod.Get, url);
            request.Headers.Authorization = AuthenticationHeaderValue.Parse(authToken);

            HttpResponseMessage response = await auditHttpClient.Client.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var data = await response.Content.ReadFromJsonAsync<T>();
                return CommandResponse<T>.Data(data: data);
            }
            else
            {
                logger.LogError("Call did not return success: {StatusCode}, {ReasonPhrase}", response.StatusCode, response.ReasonPhrase);
                return CommandResponse<T>.Failed(new Dictionary<string, string[]>() { { "Data", new[] { "Error: Unable to get data from application" } } }, status: HttpStatusCode.InternalServerError);
            }
        }
    }
}
