﻿using System.Collections.Generic;
using System.Linq;

namespace Axon.Core.Api.Extensions;

public static class DictionaryExtensions
{
    public static IDictionary<string, string[]> Merge(this IDictionary<string, string[]> first, IDictionary<string, string[]> second)
    {
        return first
            .Concat(second)
            .GroupBy(kvp => kvp.Key)
            .ToDictionary(
                g => g.Key,
                g => g.SelectMany(kvp => kvp.Value).ToArray()
            );
    }
}