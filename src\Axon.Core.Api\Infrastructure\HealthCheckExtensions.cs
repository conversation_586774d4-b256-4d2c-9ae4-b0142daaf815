﻿using System.Collections.Generic;
using System.Linq;
using System.Net.Mime;
using System.Text.Json;
using System.Threading.Tasks;
using Axon.Core.Infrastructure.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Routing;
using System.Reflection;

namespace Axon.Core.Api.Infrastructure
{
    public static class HealthCheckExtensions
    {
        private static readonly JsonSerializerOptions JsonOptions = new() {WriteIndented = true};

        /// <summary>
        /// Adds the HealthCheck settings for Startup
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration">The IConfiguration variable from initialisation</param>
        /// <returns></returns>
        public static IServiceCollection AddHealthChecks(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHealthChecks()
                .AddCheck("HealthCheck", () =>
                    HealthCheckResult.Healthy("Health is OK!"), tags: new[] { "ready" });

            return services;
        }

        public static IEndpointRouteBuilder MapHealthChecks(IEndpointRouteBuilder endpoints)
        {
            IDictionary<HealthStatus, int> resultStatusCodes = new Dictionary<HealthStatus, int>
            {
                [HealthStatus.Healthy] = StatusCodes.Status200OK,
                [HealthStatus.Degraded] = StatusCodes.Status200OK,
                [HealthStatus.Unhealthy] = StatusCodes.Status503ServiceUnavailable
            };
            endpoints.MapHealthChecks("/health", new HealthCheckOptions()
            {
                ResponseWriter = WriteHealthInformationResponse,
                AllowCachingResponses = false,
                ResultStatusCodes = resultStatusCodes
            });

            //basic readiness check placeholder
            endpoints.MapHealthChecks("/health/readiness", new HealthCheckOptions()
            {
                Predicate = (check) => check.Tags.Contains("ready"),
                ResponseWriter = WriteReadinessResponse,
                AllowCachingResponses = false,
                ResultStatusCodes = resultStatusCodes
            });

            //basic liveness check placeholder
            endpoints.MapHealthChecks("/health/liveness", new HealthCheckOptions()
            {
                Predicate = (_) => false,
                AllowCachingResponses = false,
                ResultStatusCodes = resultStatusCodes
            });

            //basic claims check placeholder
            endpoints.MapHealthChecks("/health/claims", new HealthCheckOptions()
            {
                ResponseWriter = WriteClaimsCheckResponse,
                AllowCachingResponses = false,
                ResultStatusCodes = resultStatusCodes
            }).WithMetadata(new AuthorizeAttribute());
            return endpoints;
        }

        /// <summary>
        /// Writes the readiness response including all check information and current status.
        /// </summary>
        /// <param name="httpContext">The HTTP context.</param>
        /// <param name="result">The result.</param>
        /// <returns></returns>
        private static Task WriteReadinessResponse(HttpContext httpContext, HealthReport result)
        {
            httpContext.Response.ContentType = MediaTypeNames.Application.Json;
            var json = JsonSerializer.Serialize(
                new
                {
                    status = result.Status.ToString(),
                    results = result.Entries.Select(entry =>
                        new
                        {
                            Name = entry.Key,
                            Details = new
                            {
                                status = entry.Value.Status.ToString(),
                                description = entry.Value.Description,
                                data = entry.Value.Data.Select(d => new
                                {
                                    d.Key,
                                    d.Value
                                })
                            }
                        })
                }, options: JsonOptions);
            return httpContext.Response.WriteAsync(json);
        }

        /// <summary>
        /// Writes the health information response including App Name and Version
        /// </summary>
        /// <param name="httpContext">The HTTP context.</param>
        /// <param name="result">The result.</param>
        /// <returns></returns>
        private static Task WriteClaimsCheckResponse(HttpContext httpContext,
            HealthReport result)
        {
            var claims = httpContext.User.Claims.ToList();
            httpContext.Response.ContentType = MediaTypeNames.Application.Json;
            var json = new
            {
                name = claims.Name(),
                email = claims.Email(),
                oid = claims.ObjectId(),
                tenantId = claims.TenantId(),
                role = claims.Role()
            };
            return httpContext.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(json, options: JsonOptions));
        }

        private static Task WriteHealthInformationResponse(HttpContext httpContext,
            HealthReport result)
        {
            httpContext.Response.ContentType = MediaTypeNames.Application.Json;
            var applicationName = Assembly.GetEntryAssembly().GetName().Name;
            var json = new
            {
                status = result.Status.ToString(),
                app = applicationName,
                version = VersionConfiguration.DisplayVersion
            };
            return httpContext.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(json, options: JsonOptions));
        }
    }
}
