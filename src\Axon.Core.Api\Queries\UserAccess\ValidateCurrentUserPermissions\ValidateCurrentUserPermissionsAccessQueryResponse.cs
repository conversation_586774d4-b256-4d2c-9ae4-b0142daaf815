﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Axon.Core.Api.Queries.UserAccess.ValidateCurrentUserPermissions;

public class ValidateCurrentUserPermissionsAccessQueryResponse
{
    public ValidateCurrentUserPermissionsAccessQueryResponse(Dictionary<string, bool> permissions)
    {
        Permissions = permissions;
    }

    [Required]
    public bool AnyAccess { get; set; }
    [Required]
    public bool AllAccess { get; set; }
    [Required]
    public Dictionary<string, bool> Permissions { get; }
}