﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.AppGroup.GetById;

[UsedImplicitly]
public class GetAppGroupByIdQueryRequestValidator : AbstractValidator<GetAppGroupByIdQueryRequest>
{
    public GetAppGroupByIdQueryRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.Id)
            .MustBeAValidGuid();
    }
}