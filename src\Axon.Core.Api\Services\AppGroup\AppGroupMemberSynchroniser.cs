﻿using Axon.Core.Api.Constants;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Axon.Core.Api.Services.AppGroup
{
    internal sealed class AppGroupMemberSynchroniser : IAppGroupMemberSynchroniser
    {
        private readonly IAccessRepository accessRepository;
        private readonly IAppRepository appRepository;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAppGroupRepository appGroupRepository;
        private readonly IAuditService<TenantAuditExtensions> auditService;

        public AppGroupMemberSynchroniser(IAccessRepository accessRepository, 
                                          IAppRepository appRepository, 
                                          IOrganisationRepository organisationRepository,
                                          IAppGroupRepository appGroupRepository,
                                          IAuditService<TenantAuditExtensions> auditService)
        {
            this.accessRepository = accessRepository;
            this.appRepository = appRepository;
            this.organisationRepository = organisationRepository;
            this.appGroupRepository = appGroupRepository;
            this.auditService = auditService;
        }

        public async Task SynchroniseUpdatedGroupRoleWithUsers(AppGroupEntity appGroupEntity,
                                                               Guid correlationId,
                                                               ClientDetails clientDetails)
        {
            var groupAccessEntities = await accessRepository.GetAccessItemsForGroupAsync(appGroupEntity.OrganisationCodeName, appGroupEntity.AppCodeName, appGroupEntity.GroupId);

            if (!groupAccessEntities.Any())
            {
                return;
            }

            foreach (var groupAccessEntity in groupAccessEntities)
            {
                groupAccessEntity.RoleId = appGroupEntity.RoleId;
                groupAccessEntity.Scopes = appGroupEntity.Scopes;
            }

            var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Access,
                                                                  AuditEventDescriptions.AccessUpdated,
                                                                  correlationId,
                                                                  clientDetails,
                                                                  appGroupEntity.OrganisationCodeName);

            await auditService.LogAsync(AuditEventTypes.AccessUpdated, tenantAuditExtensions, groupAccessEntities,
                async () => await accessRepository.BulkUpdateItemsAsync([.. groupAccessEntities]));
        }

        public async Task SynchroniseNewGroupRoleWithUsers(AppGroupEntity appGroupEntity,
                                                           Guid correlationId,
                                                           ClientDetails clientDetails)
        {
            var appId = AppNameConstants.AxonCoreCodeName;

            if (appGroupEntity.AppCodeName != AppNameConstants.AxonCoreCodeName)
            {
                var app = await appRepository.GetItemByAppCodeNameAsync(appGroupEntity.AppCodeName) ?? throw new InvalidOperationException("App cannot be found");
                appId = app.Id;
            }

            var org = await organisationRepository.GetItemByCodeNameAsync(appGroupEntity.OrganisationCodeName) ?? throw new InvalidOperationException("Organisation cannot be found");
            var members = await accessRepository.GetGroupMembershipAccessDocumentsForGroupAsync(appGroupEntity.GroupId);

            if (!members.Any())
            {
                return;
            }

            var accessEntities = members.Select(u => new AccessEntity
            {
                AccessType = AccessType.GroupAccess,
                OrganisationCodeName = appGroupEntity.OrganisationCodeName,
                OrganisationId = org.Id,
                GroupId = appGroupEntity.GroupId,
                RoleId = appGroupEntity.RoleId,
                AppCodeName = appGroupEntity.AppCodeName,
                AppId = appId,
                Scopes = appGroupEntity.Scopes,
                User = u.User,
                CreatedAt = DateTime.UtcNow,
                Id = $"{Guid.NewGuid()}"
            }).ToList();

            var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Access,
                                                                  AuditEventDescriptions.AccessCreated,
                                                                  correlationId,
                                                                  clientDetails,
                                                                  appGroupEntity.OrganisationCodeName);

            await auditService.LogAsync(AuditEventTypes.AccessCreated, tenantAuditExtensions, accessEntities,
                async () => await accessRepository.BulkUpdateItemsAsync(accessEntities));
        }

        public async Task SynchroniseNewGroupMemberWithAppGroups(string groupId,
                                                                 UserEntity user,
                                                                 string organisationId,
                                                                 string organisationCodeName,
                                                                 Guid correlationId,
                                                                 ClientDetails clientDetails)
        {
            if(await accessRepository.UserHasExistingRecordsForAppGroups(user.Email, groupId))
            {
                throw new InvalidOperationException("Existing OrganisationAccess records for applications are present for this user and group");
            }

            var appGroups = await appGroupRepository.GetAppGroupsByGroupIdAsync(groupId);

            if (!appGroups.Any())
            {
                return;
            }

            var apps = await appRepository.GetItemsByAppCodeNamesAsync(appGroups.Select(x=>x.AppCodeName));

            var accessEntities = appGroups.Select(ag => {

                var appId = ag.AppCodeName.Equals(AppNameConstants.AxonCoreCodeName, StringComparison.OrdinalIgnoreCase) ? AppNameConstants.AxonCoreCodeName :
                                apps.SingleOrDefault(a => a.AppCodeName.Equals(ag.AppCodeName))?.Id;

                if (appId == null)
                {
                    //Shouldn't be possible to have an app group that doesn't match an existing app
                    throw new InvalidOperationException("App cannot be found");
                }

                return new AccessEntity()
                {
                    AccessType = AccessType.GroupAccess,
                    OrganisationCodeName = organisationCodeName,
                    OrganisationId = organisationId,
                    GroupId = groupId,
                    RoleId = ag.RoleId,
                    AppCodeName = ag.AppCodeName,
                    AppId = appId,
                    Scopes = ag.Scopes,
                    User = new AccessUser()
                    {
                        Id = user.Id,
                        Email = user.Email,
                        Name = user.Name
                    },
                    CreatedAt = DateTime.UtcNow,
                    Id = $"{Guid.NewGuid()}"
                };
            }).ToList();

            var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Access,
                                                                  AuditEventDescriptions.AccessCreated,
                                                                  correlationId,
                                                                  clientDetails,
                                                                  organisationCodeName);

            await auditService.LogAsync(AuditEventTypes.AccessCreated, tenantAuditExtensions, accessEntities,
                async () => await accessRepository.BulkUpdateItemsAsync(accessEntities));
        }

    }
}
