﻿using System.Collections.Generic;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Filters;
using Axon.Core.Api.Models.App;
using Axon.Core.Api.Queries;
using JetBrains.Annotations;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands.App;
using Axon.Core.Api.Commands.App.UpdateAppConfig;
using Axon.Core.Api.Models;
using Axon.Core.Api.Queries.App.OrganisationIdQuery;
using Axon.Core.Api.Queries.App.ValidateApp;
using Axon.Core.Api.Services.Authorisation;
using Microsoft.AspNetCore.Authorization;
using Axon.Core.Api.Commands.Organisation.Update;
using Axon.Core.Shared.Api;
using FluentValidation;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Queries.Admin.App.ListQuery;
using Axon.Core.Api.Queries.App.ListQuery;

namespace Axon.Core.Api.Controllers
{
    [ApiController]
    [Produces("application/json", "application/xml")]
    [ProducesResponseType(401, Type = typeof(CommandResponse))]
    [ProducesResponseType(403, Type = typeof(CommandResponse))]
    [ProducesResponseType(500, Type = typeof(CommandResponse))]
    [Route("v{version:apiVersion}/App")]
    [Authorize]
    public class AppController : ApiControllerBase
    {
        private readonly IValidator<ListParams> listParamsValidator;
        private readonly IValidator<IdQueryRequest<AppModel>> idQueryRequestAppModelValidator;
        private readonly IValidator<GetAppListByOrganisationIdQueryRequest> getAppListByOrganisationIdQueryRequestValidator;
        private readonly IValidator<DeleteCommandRequest<AppModel>> deleteCommandRequestAppModelValidator;
        private readonly IValidator<ValidateAppQueryRequest> validateAppQueryRequestValidator;

        public AppController(IMediator mediator, IValidator<ListParams> listParamsValidator, IValidator<IdQueryRequest<AppModel>> idQueryRequestAppModelValidator, IValidator<GetAppListByOrganisationIdQueryRequest> getAppListByOrganisationIdQueryRequestValidator, IValidator<DeleteCommandRequest<AppModel>> deleteCommandRequestAppModelValidator, IValidator<ValidateAppQueryRequest> validateAppQueryRequestValidator) : base(mediator)
        {
            this.listParamsValidator = listParamsValidator;
            this.idQueryRequestAppModelValidator = idQueryRequestAppModelValidator;
            this.getAppListByOrganisationIdQueryRequestValidator = getAppListByOrganisationIdQueryRequestValidator;
            this.deleteCommandRequestAppModelValidator = deleteCommandRequestAppModelValidator;
            this.validateAppQueryRequestValidator = validateAppQueryRequestValidator;
        }

        [HttpGet("admin", Name = "GetAdminAppList")]
        [HasPermissions(nameof(CorePermissions.EditApplication))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<AppModel>>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [ListParamsActionFilter]
        public async Task<IActionResult> GetAdminAppListAsync([FromQuery][CanBeNull] string filter = "",
                                                         [FromQuery][CanBeNull] string orderBy = "",
                                                         [FromQuery] int? offset = 0,
                                                         [FromQuery] int? limit = 20)
        {
            var validationResult = await listParamsValidator.ValidateAsync(ListParams);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }
            return await Send(new GetAdminAppListQueryRequest(ListParams));
        }

        [HttpGet(Name = "GetAppList")]
        [HasPermissions(nameof(CorePermissions.ViewApplication))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<AppModel>>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [ListParamsActionFilter]
        public async Task<IActionResult> GetAppListAsync([FromQuery][CanBeNull] string filter = "",
                                                         [FromQuery][CanBeNull] string orderBy = "",
                                                         [FromQuery] int? offset = 0,
                                                         [FromQuery] int? limit = 20)
        {
            var validationResult = await listParamsValidator.ValidateAsync(ListParams);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }
            return await Send(new GetAppListQueryRequest(ListParams));
        }

        [HttpGet("{id}", Name = "GetApp")]
        [HasPermissions(permissionsKey: nameof(CorePermissions.ViewApplication))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<AppModel>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        public async Task<IActionResult> GetAppByIdAsync(string id)
        {
            var request = new IdQueryRequest<AppModel>(id);
            var validationResult = await idQueryRequestAppModelValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpGet("Organisation/{id}", Name = "GetAppsByOrganisation")]
        [HasOrganisationByIdPermissions(permissionsKey: nameof(CorePermissions.ViewApplication))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<AppOrganisationModel>>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [ListParamsActionFilter]
        public async Task<IActionResult> GetAppsByOrganisationIdAsync(string id,
                                                                     [FromQuery][CanBeNull] string filter = "",
                                                                     [FromQuery][CanBeNull] string orderBy = "",
                                                                     [FromQuery] int? offset = 0,
                                                                     [FromQuery] int? limit = 20)
        {
            var request = new GetAppListByOrganisationIdQueryRequest(id, ListParams);
            var requestValidationResult = await getAppListByOrganisationIdQueryRequestValidator.ValidateAsync(request);
            var validationResult = await listParamsValidator.ValidateAsync(ListParams);
            if (!validationResult.IsValid || !requestValidationResult.IsValid)
            {
                var validationResultErrors = validationResult.ToErrorDictionary();
                var requestValidationResultErrors = requestValidationResult.ToErrorDictionary();
                var errors = validationResultErrors.Merge(requestValidationResultErrors);
                return BadRequest(errors);
            }

            return await Send(request);
        }

        [HttpPost(Name = "CreateApp")]
        [HasPermissions(permissionsKey: nameof(CorePermissions.CreateApplication))]
        [ProducesResponseType(201, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
        public async Task<IActionResult> CreateAppAsync([FromBody] AppBody model) =>
            await Send(new CreateCommandRequest<AppBody>(model));

        [HttpPut("{id}", Name = "UpdateApp")]
        [HasPermissions(permissionsKey: nameof(CorePermissions.EditApplication))]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
        public async Task<IActionResult> UpdateAppAsync(string id, [FromBody] AppBody command) =>
            await Send(new UpdateCommandRequest<AppBody>(id, command));

        [HttpDelete("{id}", Name = "DeleteApp")]
        [HasPermissions(permissionsKey: nameof(CorePermissions.DeleteApplication))]
        [ProducesResponseType(204)]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
        public async Task<IActionResult> DeleteAppAsync(string id)
        {
            var request = new DeleteCommandRequest<AppModel>(id);
            var validationResult = await deleteCommandRequestAppModelValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpPut("{id}/Config", Name = "UpdateAppConfig")]
        [HasPermissions(permissionsKey: nameof(CorePermissions.EditApplication))]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
        public async Task<IActionResult> UpdateAppConfigAsync(string id, [FromBody] UpdateAppConfigBody command) =>
            await Send(new UpdateCommandRequest<UpdateAppConfigBody>(id, command));

        [HttpGet("{id}/validate", Name = "ValidateApplicationById")]
        [HasPermissions(nameof(CorePermissions.EditApplication))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<ValidationResultModel>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        public async Task<IActionResult> ValidateAppByIdAsync([FromRoute(Name = "id")] string id, [FromQuery(Name = "Name")] string displayName, [FromQuery(Name = "CodeName")] string appCodeName)
        {
            var request = new ValidateAppQueryRequest(id, displayName, appCodeName);
            var validationResult = await validateAppQueryRequestValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpGet("validate", Name = "ValidateApplication")]
        [HasPermissions(nameof(CorePermissions.CreateApplication))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<ValidationResultModel>))]
        public async Task<IActionResult> ValidateAppAsync([FromQuery(Name = "Name")] string displayName, [FromQuery(Name = "CodeName")] string appCodeName)
        {
            var request = new ValidateAppQueryRequest(null, displayName, appCodeName);
            var validationResult = await validateAppQueryRequestValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpPut("{id}/avatar", Name = "UpdateAppAvatar")]
        [DisableFormValueModelBinding]
        [HasPermissions(nameof(CorePermissions.EditApplication))]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
        public async Task<IActionResult> UpdateAppAvatarAsync(string id)
        {
            return await Send(new UpdateCommandRequest<UpdateAppAvatarCommandRequest>(id, new UpdateAppAvatarCommandRequest()));
        }
    }
}
