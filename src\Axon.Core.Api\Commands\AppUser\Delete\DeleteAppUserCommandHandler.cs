﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using Axon.Core.Api.Commands.AppUser.Delete;
using MediatR;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Microsoft.Extensions.Logging;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.Core.Api.Constants;
using Axon.Core.Domain.Enums;
using AutoMapper;
using Axon.Core.Contracts;
using System.Runtime.CompilerServices;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.AppUser.Create
{
    internal class DeleteAppUserCommandHandler : IRequestHandler<DeleteAppUserCommand, CommandResponse>
    {
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAppRepository appRepository;
        private readonly ILogger<DeleteAppUserCommandHandler> logger;
        private readonly IAccessRepository accessRepository;
        private readonly IAuditService<TenantAuditExtensions> auditService;
        private readonly IClientDetailsProvider clientDetailsProvider;
        private readonly ICorrelationIdProvider correlationIdProvider;
        private readonly IUserRepository userRepository;
        private readonly IMessageBus messageBus;
        private readonly IMapper mapper;

        public DeleteAppUserCommandHandler(ILogger<DeleteAppUserCommandHandler> logger, 
            IOrganisationRepository organisationRepository,
            IAppRepository appRepository,
            IAccessRepository accessRepository,
            IAuditService<TenantAuditExtensions> auditService,
            IClientDetailsProvider clientDetailsProvider,
            ICorrelationIdProvider correlationIdProvider,
            IUserRepository userRepository,
            IMessageBus messageBus,
            IMapper mapper)
        {
            this.organisationRepository = organisationRepository;
            this.logger = logger;
            this.appRepository = appRepository;
            this.accessRepository = accessRepository;
            this.auditService = auditService;
            this.clientDetailsProvider = clientDetailsProvider;
            this.correlationIdProvider = correlationIdProvider;
            this.userRepository = userRepository;
            this.messageBus = messageBus;
            this.mapper = mapper;
        }

        public async Task<CommandResponse> Handle(DeleteAppUserCommand request, CancellationToken cancellationToken)
        {
            var organisationEntity = await organisationRepository.GetItemByCodeNameAsync(request.OrgCodeName);
            if (organisationEntity == null)
            {
                logger.LogWarning("Organisation {organisationCodeName} does not exist.", request.OrgCodeName);
                return CommandResponse.NotFound(nameof(OrganisationEntity), request.OrgCodeName);
            }

            if (!request.AppCodeName.Equals(AppNameConstants.AxonCoreCodeName, System.StringComparison.OrdinalIgnoreCase))
            {
                var appEntity = await appRepository.GetItemByAppCodeNameAsync(request.AppCodeName);
                if (appEntity == null)
                {
                    logger.LogWarning("App {appCodeName} does not exist.", request.AppCodeName);
                    return CommandResponse.NotFound(nameof(AppEntity), request.AppCodeName);
                }
            }

            var userEntity = await userRepository.GetUserByEmailAsync(request.EmailAddress);
            if (userEntity == null)
            {
                logger.LogWarning("User {emailAddress} does not exist.", request.EmailAddress);
                return CommandResponse.NotFound(nameof(UserEntity), request.EmailAddress);
            }

            //get user access and group access items for user
            var existing = await accessRepository.GetAccessItemsForUserAsync(request.EmailAddress, request.AppCodeName, [request.OrgCodeName], [AccessType.UserAccess]);
            if (!existing.Any())
            {
                logger.LogWarning("Access items for Org: {OrgCodeName}, App: {appCodeName} and User Email Address: {emailAddress}, does not exist.", request.OrgCodeName, request.AppCodeName, request.EmailAddress);
                return CommandResponse.NotFound(nameof(AccessEntity), request.AppCodeName);
            }

            //delete user access items and group access items
            var correlationId = correlationIdProvider.Provide();
            var clientDetails = clientDetailsProvider.Provide();

            var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Access, AuditEventDescriptions.AccessDeleted, correlationId, clientDetails, request.OrgCodeName);

            await auditService.LogAsync(AuditEventTypes.AccessDeleted, tenantAuditExtensions, existing,
                async () =>
                {
                    await accessRepository.BulkDeleteItemsAsync(existing.ToList());
                });
            foreach (var access in existing)
            {
                var mappedEvent = mapper.Map<AppUserUpdatedEvent>(access);
                mappedEvent.Action = EventActionType.Deleted;
                await messageBus.PublishAsync(mappedEvent);
            }
            return CommandResponse.Success();
        }
    }
}
