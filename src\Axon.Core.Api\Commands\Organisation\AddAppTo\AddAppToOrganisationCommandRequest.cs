﻿using MediatR;

namespace Axon.Core.Api.Commands.Organisation.AddAppTo
{
    public class AddAppToOrganisationCommandRequest : IRequest<CommandResponse>
    {
        public string AppId { get; }
        public string OrganisationId { get; }

        public AddAppToOrganisationCommandRequest(string organisationId, string appId)
        {
            OrganisationId = organisationId;
            AppId = appId;
        }
    }
}
