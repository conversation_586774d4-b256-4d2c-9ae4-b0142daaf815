﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.UserIdentityProvider;
using Axon.Core.Api.Queries.UserIdentityProvider;
using Axon.Core.Api.Queries.UserIdentityProvider.GetLoginProviderDetails;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Axon.Core.Api.Controllers
{
    [ApiController]
    [Produces("application/json", "application/xml")]
    [ProducesResponseType(401, Type = typeof(CommandResponse))]
    [ProducesResponseType(403, Type = typeof(CommandResponse))]
    [ProducesResponseType(500, Type = typeof(CommandResponse))]
    [Route("v{version:apiVersion}/User/IdentityProvider")]
    [Authorize]
    public class UserIdentityProviderController : ApiControllerBase
    {
        public UserIdentityProviderController(IMediator mediator) : base(mediator)
        {
        }

        [HttpGet(Name = "GetUserIdentityProvider")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<UserIdentityProviderModel>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        public async Task<IActionResult> GetIdentityProviderDetails()
            => await Send(new GetUserIdentityProviderQueryRequest());

        [HttpGet("login/provider", Name = "GetLoginProviderDetails")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<LoginProviderDetails>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
        [AllowAnonymous]
        public async Task<IActionResult> GetOIDCConfig(string email)
        {
            var request = new GetLoginProviderDetailsQuery(email);
            return await Send(request);
        }
    }
}
