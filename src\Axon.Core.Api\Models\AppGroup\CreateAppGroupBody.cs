﻿using Axon.Core.Api.Models.Role;
using System.Collections.Generic;

namespace Axon.Core.Api.Models.AppGroup
{
    public class CreateAppGroupBody
    {
        public string GroupId { get; set; }
        public string GroupName { get; set; }
        public string RoleId { get; set; }
        public string RoleName { get; set; }
        public IReadOnlyCollection<ScopeResourcesBody> Scopes { get; set; }
        public CreateAppGroupBody(string groupId, string groupName, string roleId, string roleName, IReadOnlyCollection<ScopeResourcesBody> scopes)
        {
            GroupId = groupId;
            GroupName = groupName;
            RoleId = roleId;
            RoleName = roleName;
            Scopes = scopes;
        }
    }
}
