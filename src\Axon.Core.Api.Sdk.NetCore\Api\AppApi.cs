/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Axon.Core.Api.Sdk.NetCore.Client;
using Axon.Core.Api.Sdk.NetCore.Model;

namespace Axon.Core.Api.Sdk.NetCore.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse CreateApp(AppBody? appBody = default(AppBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> CreateAppWithHttpInfo(AppBody? appBody = default(AppBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns></returns>
        void DeleteApp(string id);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> DeleteAppWithHttpInfo(string id);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>AppModelIEnumerableCommandResponse</returns>
        AppModelIEnumerableCommandResponse GetAdminAppList(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of AppModelIEnumerableCommandResponse</returns>
        ApiResponse<AppModelIEnumerableCommandResponse> GetAdminAppListWithHttpInfo(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>AppModelCommandResponse</returns>
        AppModelCommandResponse GetApp(string id);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of AppModelCommandResponse</returns>
        ApiResponse<AppModelCommandResponse> GetAppWithHttpInfo(string id);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>AppModelIEnumerableCommandResponse</returns>
        AppModelIEnumerableCommandResponse GetAppList(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of AppModelIEnumerableCommandResponse</returns>
        ApiResponse<AppModelIEnumerableCommandResponse> GetAppListWithHttpInfo(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>AppOrganisationModelIEnumerableCommandResponse</returns>
        AppOrganisationModelIEnumerableCommandResponse GetAppsByOrganisation(string id, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of AppOrganisationModelIEnumerableCommandResponse</returns>
        ApiResponse<AppOrganisationModelIEnumerableCommandResponse> GetAppsByOrganisationWithHttpInfo(string id, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse UpdateApp(string id, AppBody? appBody = default(AppBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> UpdateAppWithHttpInfo(string id, AppBody? appBody = default(AppBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>CommandResponse</returns>
        CommandResponse UpdateAppAvatar(string id);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> UpdateAppAvatarWithHttpInfo(string id);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateAppConfigBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse UpdateAppConfig(string id, UpdateAppConfigBody? updateAppConfigBody = default(UpdateAppConfigBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateAppConfigBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> UpdateAppConfigWithHttpInfo(string id, UpdateAppConfigBody? updateAppConfigBody = default(UpdateAppConfigBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ValidationResultModelCommandResponse</returns>
        ValidationResultModelCommandResponse ValidateApplication(string? name = default(string?), string? codeName = default(string?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ApiResponse of ValidationResultModelCommandResponse</returns>
        ApiResponse<ValidationResultModelCommandResponse> ValidateApplicationWithHttpInfo(string? name = default(string?), string? codeName = default(string?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ValidationResultModelCommandResponse</returns>
        ValidationResultModelCommandResponse ValidateApplicationById(string id, string? name = default(string?), string? codeName = default(string?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ApiResponse of ValidationResultModelCommandResponse</returns>
        ApiResponse<ValidationResultModelCommandResponse> ValidateApplicationByIdWithHttpInfo(string id, string? name = default(string?), string? codeName = default(string?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> CreateAppAsync(AppBody? appBody = default(AppBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> CreateAppWithHttpInfoAsync(AppBody? appBody = default(AppBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task DeleteAppAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> DeleteAppWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppModelIEnumerableCommandResponse</returns>
        System.Threading.Tasks.Task<AppModelIEnumerableCommandResponse> GetAdminAppListAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppModelIEnumerableCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<AppModelIEnumerableCommandResponse>> GetAdminAppListWithHttpInfoAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppModelCommandResponse</returns>
        System.Threading.Tasks.Task<AppModelCommandResponse> GetAppAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<AppModelCommandResponse>> GetAppWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppModelIEnumerableCommandResponse</returns>
        System.Threading.Tasks.Task<AppModelIEnumerableCommandResponse> GetAppListAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppModelIEnumerableCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<AppModelIEnumerableCommandResponse>> GetAppListWithHttpInfoAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppOrganisationModelIEnumerableCommandResponse</returns>
        System.Threading.Tasks.Task<AppOrganisationModelIEnumerableCommandResponse> GetAppsByOrganisationAsync(string id, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppOrganisationModelIEnumerableCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<AppOrganisationModelIEnumerableCommandResponse>> GetAppsByOrganisationWithHttpInfoAsync(string id, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> UpdateAppAsync(string id, AppBody? appBody = default(AppBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> UpdateAppWithHttpInfoAsync(string id, AppBody? appBody = default(AppBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> UpdateAppAvatarAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> UpdateAppAvatarWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateAppConfigBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> UpdateAppConfigAsync(string id, UpdateAppConfigBody? updateAppConfigBody = default(UpdateAppConfigBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateAppConfigBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> UpdateAppConfigWithHttpInfoAsync(string id, UpdateAppConfigBody? updateAppConfigBody = default(UpdateAppConfigBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ValidationResultModelCommandResponse</returns>
        System.Threading.Tasks.Task<ValidationResultModelCommandResponse> ValidateApplicationAsync(string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ValidationResultModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<ValidationResultModelCommandResponse>> ValidateApplicationWithHttpInfoAsync(string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ValidationResultModelCommandResponse</returns>
        System.Threading.Tasks.Task<ValidationResultModelCommandResponse> ValidateApplicationByIdAsync(string id, string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ValidationResultModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<ValidationResultModelCommandResponse>> ValidateApplicationByIdWithHttpInfoAsync(string id, string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppApi : IAppApiSync, IAppApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class AppApi : IDisposable, IAppApi
    {
        private Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="AppApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public AppApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public AppApi(string basePath)
        {
            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public AppApi(Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppApi(HttpClient client, Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public AppApi(Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient client, Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient asyncClient, Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse CreateApp(AppBody? appBody = default(AppBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = CreateAppWithHttpInfo(appBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> CreateAppWithHttpInfo(AppBody? appBody = default(AppBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = appBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CommandResponse>("/v1/App", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateApp", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> CreateAppAsync(AppBody? appBody = default(AppBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await CreateAppWithHttpInfoAsync(appBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> CreateAppWithHttpInfoAsync(AppBody? appBody = default(AppBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = appBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CommandResponse>("/v1/App", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateApp", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns></returns>
        public void DeleteApp(string id)
        {
            DeleteAppWithHttpInfo(id);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<Object> DeleteAppWithHttpInfo(string id)
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->DeleteApp");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/v1/App/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteApp", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task DeleteAppAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            await DeleteAppWithHttpInfoAsync(id, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<Object>> DeleteAppWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->DeleteApp");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/v1/App/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteApp", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>AppModelIEnumerableCommandResponse</returns>
        public AppModelIEnumerableCommandResponse GetAdminAppList(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelIEnumerableCommandResponse> localVarResponse = GetAdminAppListWithHttpInfo(filter, orderBy, offset, limit);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of AppModelIEnumerableCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelIEnumerableCommandResponse> GetAdminAppListWithHttpInfo(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<AppModelIEnumerableCommandResponse>("/v1/App/admin", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAdminAppList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppModelIEnumerableCommandResponse</returns>
        public async System.Threading.Tasks.Task<AppModelIEnumerableCommandResponse> GetAdminAppListAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelIEnumerableCommandResponse> localVarResponse = await GetAdminAppListWithHttpInfoAsync(filter, orderBy, offset, limit, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppModelIEnumerableCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelIEnumerableCommandResponse>> GetAdminAppListWithHttpInfoAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<AppModelIEnumerableCommandResponse>("/v1/App/admin", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAdminAppList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>AppModelCommandResponse</returns>
        public AppModelCommandResponse GetApp(string id)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelCommandResponse> localVarResponse = GetAppWithHttpInfo(id);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of AppModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelCommandResponse> GetAppWithHttpInfo(string id)
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->GetApp");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<AppModelCommandResponse>("/v1/App/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetApp", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<AppModelCommandResponse> GetAppAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelCommandResponse> localVarResponse = await GetAppWithHttpInfoAsync(id, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelCommandResponse>> GetAppWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->GetApp");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<AppModelCommandResponse>("/v1/App/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetApp", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>AppModelIEnumerableCommandResponse</returns>
        public AppModelIEnumerableCommandResponse GetAppList(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelIEnumerableCommandResponse> localVarResponse = GetAppListWithHttpInfo(filter, orderBy, offset, limit);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of AppModelIEnumerableCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelIEnumerableCommandResponse> GetAppListWithHttpInfo(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<AppModelIEnumerableCommandResponse>("/v1/App", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppModelIEnumerableCommandResponse</returns>
        public async System.Threading.Tasks.Task<AppModelIEnumerableCommandResponse> GetAppListAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelIEnumerableCommandResponse> localVarResponse = await GetAppListWithHttpInfoAsync(filter, orderBy, offset, limit, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppModelIEnumerableCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppModelIEnumerableCommandResponse>> GetAppListWithHttpInfoAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<AppModelIEnumerableCommandResponse>("/v1/App", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>AppOrganisationModelIEnumerableCommandResponse</returns>
        public AppOrganisationModelIEnumerableCommandResponse GetAppsByOrganisation(string id, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppOrganisationModelIEnumerableCommandResponse> localVarResponse = GetAppsByOrganisationWithHttpInfo(id, filter, orderBy, offset, limit);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of AppOrganisationModelIEnumerableCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppOrganisationModelIEnumerableCommandResponse> GetAppsByOrganisationWithHttpInfo(string id, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->GetAppsByOrganisation");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<AppOrganisationModelIEnumerableCommandResponse>("/v1/App/Organisation/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppsByOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppOrganisationModelIEnumerableCommandResponse</returns>
        public async System.Threading.Tasks.Task<AppOrganisationModelIEnumerableCommandResponse> GetAppsByOrganisationAsync(string id, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppOrganisationModelIEnumerableCommandResponse> localVarResponse = await GetAppsByOrganisationWithHttpInfoAsync(id, filter, orderBy, offset, limit, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppOrganisationModelIEnumerableCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppOrganisationModelIEnumerableCommandResponse>> GetAppsByOrganisationWithHttpInfoAsync(string id, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->GetAppsByOrganisation");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<AppOrganisationModelIEnumerableCommandResponse>("/v1/App/Organisation/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppsByOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse UpdateApp(string id, AppBody? appBody = default(AppBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = UpdateAppWithHttpInfo(id, appBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> UpdateAppWithHttpInfo(string id, AppBody? appBody = default(AppBody?))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->UpdateApp");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.Data = appBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<CommandResponse>("/v1/App/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateApp", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> UpdateAppAsync(string id, AppBody? appBody = default(AppBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await UpdateAppWithHttpInfoAsync(id, appBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> UpdateAppWithHttpInfoAsync(string id, AppBody? appBody = default(AppBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->UpdateApp");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.Data = appBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<CommandResponse>("/v1/App/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateApp", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>CommandResponse</returns>
        public CommandResponse UpdateAppAvatar(string id)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = UpdateAppAvatarWithHttpInfo(id);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> UpdateAppAvatarWithHttpInfo(string id)
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->UpdateAppAvatar");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<CommandResponse>("/v1/App/{id}/avatar", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppAvatar", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> UpdateAppAvatarAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await UpdateAppAvatarWithHttpInfoAsync(id, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> UpdateAppAvatarWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->UpdateAppAvatar");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<CommandResponse>("/v1/App/{id}/avatar", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppAvatar", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateAppConfigBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse UpdateAppConfig(string id, UpdateAppConfigBody? updateAppConfigBody = default(UpdateAppConfigBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = UpdateAppConfigWithHttpInfo(id, updateAppConfigBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateAppConfigBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> UpdateAppConfigWithHttpInfo(string id, UpdateAppConfigBody? updateAppConfigBody = default(UpdateAppConfigBody?))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->UpdateAppConfig");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.Data = updateAppConfigBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<CommandResponse>("/v1/App/{id}/Config", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppConfig", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateAppConfigBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> UpdateAppConfigAsync(string id, UpdateAppConfigBody? updateAppConfigBody = default(UpdateAppConfigBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await UpdateAppConfigWithHttpInfoAsync(id, updateAppConfigBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateAppConfigBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> UpdateAppConfigWithHttpInfoAsync(string id, UpdateAppConfigBody? updateAppConfigBody = default(UpdateAppConfigBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->UpdateAppConfig");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.Data = updateAppConfigBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<CommandResponse>("/v1/App/{id}/Config", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppConfig", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ValidationResultModelCommandResponse</returns>
        public ValidationResultModelCommandResponse ValidateApplication(string? name = default(string?), string? codeName = default(string?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> localVarResponse = ValidateApplicationWithHttpInfo(name, codeName);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ApiResponse of ValidationResultModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> ValidateApplicationWithHttpInfo(string? name = default(string?), string? codeName = default(string?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (name != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "Name", name));
            }
            if (codeName != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "CodeName", codeName));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<ValidationResultModelCommandResponse>("/v1/App/validate", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ValidateApplication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ValidationResultModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<ValidationResultModelCommandResponse> ValidateApplicationAsync(string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> localVarResponse = await ValidateApplicationWithHttpInfoAsync(name, codeName, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ValidationResultModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse>> ValidateApplicationWithHttpInfoAsync(string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (name != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "Name", name));
            }
            if (codeName != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "CodeName", codeName));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<ValidationResultModelCommandResponse>("/v1/App/validate", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ValidateApplication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ValidationResultModelCommandResponse</returns>
        public ValidationResultModelCommandResponse ValidateApplicationById(string id, string? name = default(string?), string? codeName = default(string?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> localVarResponse = ValidateApplicationByIdWithHttpInfo(id, name, codeName);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ApiResponse of ValidationResultModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> ValidateApplicationByIdWithHttpInfo(string id, string? name = default(string?), string? codeName = default(string?))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->ValidateApplicationById");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            if (name != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "Name", name));
            }
            if (codeName != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "CodeName", codeName));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<ValidationResultModelCommandResponse>("/v1/App/{id}/validate", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ValidateApplicationById", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ValidationResultModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<ValidationResultModelCommandResponse> ValidateApplicationByIdAsync(string id, string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> localVarResponse = await ValidateApplicationByIdWithHttpInfoAsync(id, name, codeName, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ValidationResultModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse>> ValidateApplicationByIdWithHttpInfoAsync(string id, string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppApi->ValidateApplicationById");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            if (name != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "Name", name));
            }
            if (codeName != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "CodeName", codeName));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<ValidationResultModelCommandResponse>("/v1/App/{id}/validate", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ValidateApplicationById", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
