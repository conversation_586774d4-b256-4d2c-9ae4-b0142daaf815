﻿using System;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using System.Collections.Generic;
using System.Linq;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Shared.Api;
using Axon.Core.Domain.Entities;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Domain.Extensions;

namespace Axon.Core.Api.Services.Organisation
{
    public class OrganisationRequestService : IOrganisationRequestService
    {
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAccessService accessService;
        private readonly IUserRequestContext userRequestContext;
        private readonly IOrganisationModelMappingService modelMapper;

        public OrganisationRequestService(IOrganisationRepository organisationRepository,
                                          IAccessService accessService,
                                          IUserRequestContext userRequestContext,
                                          IOrganisationModelMappingService modelMapper)
        {
            this.organisationRepository = organisationRepository;
            this.accessService = accessService;
            this.userRequestContext = userRequestContext;
            this.modelMapper = modelMapper;
        }

        public async Task<CommandResponse<IEnumerable<OrganisationModel>>> GetFilteredOrganisations(ListParams listParams)
        {
            if (userRequestContext.IsApplication())
            {
                return await HandleAppPrincipalUser(listParams);
            }

            return await HandleStandardUser(listParams);
        }

        public async Task<CommandResponse<OrganisationModel>> GetOrganisationByCode(string orgCodeName)
        {
            var organisation = await organisationRepository.GetItemByCodeNameAsync(orgCodeName);

            if (organisation == null)
            {
                return CommandResponse<OrganisationModel>.NotFound(nameof(OrganisationModel), orgCodeName);
            }

            return await GetOrganisation(organisation);
        }

        public async Task<CommandResponse<OrganisationModel>> GetOrganisationById(string orgId)
        {
            var organisation = await organisationRepository.GetItemAsync(orgId);

            if (organisation == null)
            {
                return CommandResponse<OrganisationModel>.NotFound(nameof(OrganisationModel), orgId);
            }

            return await GetOrganisation(organisation);
        }

        private async Task<CommandResponse<OrganisationModel>> GetOrganisation(OrganisationEntity organisation)
        {
            if (userRequestContext.IsApplication())
            {
                return CommandResponse<OrganisationModel>.Data(modelMapper.MapForApp(organisation));
            }

            var userEmail = userRequestContext.GetEmailAddress();
            var orgAccess = await accessService.GetEffectivePermissions(userEmail, AppNameConstants.AxonCoreCodeName, organisation.CodeName);
            var appAccess = await accessService.GetUsersDirectEffectivePermissionsForAllAppsAndOrgs(userEmail);

            var appsForOrg = appAccess.Where(x => x.OrgCodeName.Equals(organisation.CodeName, System.StringComparison.OrdinalIgnoreCase)).ToList();

            return CommandResponse<OrganisationModel>.Data(modelMapper.MapForUser(organisation, orgAccess, appsForOrg));
        }

        private async Task<CommandResponse<IEnumerable<OrganisationModel>>> HandleAppPrincipalUser(ListParams listParams)
        {
            var organisations = await organisationRepository.GetAllByFilterLinqQueryableAsync(listParams);
            return CommandResponse<IEnumerable<OrganisationModel>>.Data(organisations.Select(modelMapper.MapForApp));
        }

        private async Task<CommandResponse<IEnumerable<OrganisationModel>>> HandleStandardUser(ListParams listParams)
        {
            var organisationsAndPermissions = await accessService.GetUsersAccessibleOrganisationsAndEffectivePermissions(userRequestContext.GetEmailAddress(), AppNameConstants.AxonCoreCodeName, listParams);

            var allPermissions = await accessService.GetUsersDirectEffectivePermissionsForAllAppsAndOrgs(userRequestContext.GetEmailAddress());

            //We filter by user having ViewOrganiation permission here, as they might have organisation access records but no permissions
            //This is not required on the individual (code and id) methods because the permissions will be enforced by the authorisation layer
            //It is not required for apps as they can see all organisations.
            var orgsWithPermission = organisationsAndPermissions.Where(x => x.EffectivePermissions.EffectivePermissions.HasPermission(nameof(CorePermissions.ViewOrganisation)));
            return CommandResponse<IEnumerable<OrganisationModel>>.Data(orgsWithPermission.Select(x => modelMapper.MapForUser(x.Organistaion,
                                                                                                                              x.EffectivePermissions,
                                                                                                                              [.. allPermissions.Where(p => p.OrgCodeName.Equals(x.Organistaion.CodeName, 
                                                                                                                              StringComparison.OrdinalIgnoreCase))])));
        }
    }
}
