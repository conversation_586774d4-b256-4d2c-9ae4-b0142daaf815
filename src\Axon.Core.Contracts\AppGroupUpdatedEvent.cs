﻿using System.Text.Json.Serialization;

namespace Axon.Core.Contracts
{
    public class AppGroupUpdatedEvent
    {
        public string OrgId { get; set; }
        public string OrgCodeName { get; set; }
        public string AppId { get; set; }
        public string AppCodeName { get; set; }
        public string GroupId { get; set; }
        [JsonConverter(typeof(JsonStringEnumConverter<EventActionType>))]
        public EventActionType Action { get; set; }
    }
}
