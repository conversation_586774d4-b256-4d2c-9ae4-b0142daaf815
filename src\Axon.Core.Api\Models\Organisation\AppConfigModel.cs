﻿using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.Organisation
{
    public class AppConfigModel
    {
        [Required]
        public string AppId { get; set; }
        [Required]
        public string DisplayName { get; set; }
        [Required]
        public string AppCodeName { get; set; }
        [Required]
        public bool Enabled { get; set; }
        [Required]
        public bool Deleted { get; set; }
        [Required]
        public string AppSetting { get; set; }
        [Required]
        public string Status { get; set; }
        [Required]
        public bool HasAccess { get; set; }
    }
}