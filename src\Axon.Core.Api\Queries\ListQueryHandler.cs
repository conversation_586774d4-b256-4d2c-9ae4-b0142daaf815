﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Domain.Entities.Base;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Infrastructure.Extensions;
using CommunityToolkit.Diagnostics;
using MediatR;


namespace Axon.Core.Api.Queries
{
    internal class ListQueryHandler<TEntity, TModel>: IRequestHandler<ListQueryRequest<TModel>, CommandResponse<IEnumerable<TModel>>>
        where TEntity : BaseEntity
    {
        protected readonly IRepository<TEntity> Repo;
        private readonly IMapper mapper;

        public ListQueryHandler(IRepository<TEntity> repo, IMapper mapper)
        {
            Guard.IsNotNull(repo);
            this.Repo = repo;
            Guard.IsNotNull(mapper);
            this.mapper = mapper;
        }

        public async Task<CommandResponse<IEnumerable<TModel>>> Handle(ListQueryRequest<TModel> request, CancellationToken cancellationToken)
        {

            return await Task.Run(() =>
            {
                var filtered = Repo
                    .GetAllLinqQueryable()
                    .FilteredResult(request.ListParams)
                    .AsEnumerable();
                return CommandResponse<IEnumerable<TModel>>.Data(filtered.Select(e => mapper.Map<TModel>(e)));
            }, cancellationToken);
        }
    }
}