﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.AddOrganisationGroup;

internal class AddOrganisationGroupCommandHandler : BaseCommandHandler<AccessEntity, AddOrganisationGroupCommandRequest, CommandResponse>
{
    private readonly IOrganisationRepository organisationRepository;
    private readonly IGroupRepository groupRepository;
    private readonly IClientDetailsProvider clientDetailsProvider;
    private readonly ICorrelationIdProvider correlationIdProvider;
    private readonly IAuditService<TenantAuditExtensions> auditService;

    private readonly ILogger<AddOrganisationGroupCommandHandler> logger;

    public AddOrganisationGroupCommandHandler(
        IAccessRepository accessRepository,
        IMapper mapper,
        IMessageBus messageBus,
        IOrganisationRepository organisationRepository,
        IGroupRepository groupRepository,
        IClientDetailsProvider clientDetailsProvider,
        ICorrelationIdProvider correlationIdProvider,
        IAuditService<TenantAuditExtensions> auditService,
        ILogger<AddOrganisationGroupCommandHandler> logger)
        : base(accessRepository, mapper, messageBus)
    {
        this.organisationRepository = organisationRepository;
        this.groupRepository = groupRepository;
        this.clientDetailsProvider = clientDetailsProvider;
        this.correlationIdProvider = correlationIdProvider;
        this.auditService = auditService;
        this.logger = logger;
    }

    public override async Task<CommandResponse> Handle(AddOrganisationGroupCommandRequest request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.AddOrganisationGroupCommandRequestBody.Name))
            return CommandResponse.BadRequest(nameof(request.AddOrganisationGroupCommandRequestBody.Name), "Group name missing");

        var organisationEntity = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);
        if (organisationEntity == null)
        {
            logger.LogWarning("Organisation {organisationCodeName} does not exist.", request.OrganisationCodeName);
            return CommandResponse.NotFound(nameof(OrganisationEntity), request.OrganisationCodeName);
        }

        var existingGroupEntity = (await groupRepository.GetForOrganisationAsync(request.OrganisationCodeName))
            .FirstOrDefault(x => string.Equals(x.Name, request.AddOrganisationGroupCommandRequestBody.Name, StringComparison.InvariantCultureIgnoreCase));
        if (existingGroupEntity != null)
            return CommandResponse.Conflict(nameof(existingGroupEntity), existingGroupEntity.Id, existingGroupEntity.Name);

        var newGroup = new GroupEntity();

        //Save group entity inside audit wrapper
        var correlationId = correlationIdProvider.Provide();
        var clientDetails = clientDetailsProvider.Provide();
        var tenantAuditExtensions = new TenantAuditExtensions(
            AuditEventCategories.Group,
            AuditEventDescriptions.GroupCreated(request.AddOrganisationGroupCommandRequestBody.Name),
            correlationId,
            clientDetails,
            request.OrganisationCodeName);
        var newGroupId = string.Empty;

        await auditService.LogAsync(AuditEventTypes.GroupCreated, tenantAuditExtensions, newGroup,
            async () =>
            {
                newGroup.Name = request.AddOrganisationGroupCommandRequestBody.Name;
                newGroup.OrganisationCodeName = request.OrganisationCodeName;
                newGroup.CreatedAt = DateTime.UtcNow;
                newGroup.LastUpdatedDate = newGroup.CreatedAt;

                newGroupId = await groupRepository.AddItemAsync(newGroup);
            });

        return CommandResponse.Data(new OrganisationGroupModel()
        {
            Name = newGroup.Name,
            CreatedAt = newGroup.CreatedAt,
            Id = newGroupId,
            LastUpdatedDate = newGroup.LastUpdatedDate,
            Users = []
        });
    }
}
