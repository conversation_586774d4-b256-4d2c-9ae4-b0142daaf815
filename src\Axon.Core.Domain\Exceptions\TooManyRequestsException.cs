﻿using System;
using System.Runtime.Serialization;

namespace Axon.Core.Domain.Exceptions
{
    [Serializable]
    public class TooManyRequestsException : Exception
    {
        public TooManyRequestsException()
        {
        }

        public TooManyRequestsException(string message, Exception innerException) : base(message, innerException)
        { }

        protected TooManyRequestsException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
    }
}
