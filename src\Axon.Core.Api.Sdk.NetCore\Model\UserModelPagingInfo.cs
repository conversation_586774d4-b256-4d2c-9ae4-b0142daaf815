/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// UserModelPagingInfo
    /// </summary>
    [DataContract(Name = "UserModelPagingInfo")]
    public partial class UserModelPagingInfo : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UserModelPagingInfo" /> class.
        /// </summary>
        /// <param name="offset">offset.</param>
        /// <param name="limit">limit.</param>
        /// <param name="totalItemCount">totalItemCount.</param>
        public UserModelPagingInfo(int offset = default(int), int limit = default(int), long totalItemCount = default(long))
        {
            this.Offset = offset;
            this.Limit = limit;
            this.TotalItemCount = totalItemCount;
        }

        /// <summary>
        /// Gets or Sets Offset
        /// </summary>
        [DataMember(Name = "offset", EmitDefaultValue = false)]
        public int Offset { get; set; }

        /// <summary>
        /// Gets or Sets Limit
        /// </summary>
        [DataMember(Name = "limit", EmitDefaultValue = false)]
        public int Limit { get; set; }

        /// <summary>
        /// Gets or Sets TotalItemCount
        /// </summary>
        [DataMember(Name = "totalItemCount", EmitDefaultValue = false)]
        public long TotalItemCount { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class UserModelPagingInfo {\n");
            sb.Append("  Offset: ").Append(Offset).Append("\n");
            sb.Append("  Limit: ").Append(Limit).Append("\n");
            sb.Append("  TotalItemCount: ").Append(TotalItemCount).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
