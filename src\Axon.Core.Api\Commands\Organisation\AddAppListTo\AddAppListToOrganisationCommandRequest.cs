﻿using MediatR;
using System.Collections.Generic;

namespace Axon.Core.Api.Commands.Organisation.AddAppListTo
{
    public record class AddAppListToOrganisationCommandRequest : IRequest<CommandResponse>
    {
        public List<string> Apps { get; }
        public string OrganisationId { get; }

        public AddAppListToOrganisationCommandRequest(string organisationId, List<string> apps)
        {
            OrganisationId = organisationId;
            Apps = apps;
        }
    }
}
