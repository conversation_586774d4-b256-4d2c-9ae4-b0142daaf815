﻿using System.Collections.Generic;
using System.Linq;

namespace Axon.Core.Domain.Extensions
{
    public static class EnumerableOfTExtensions
    {
        public static string ToCsv(this IEnumerable<string> source, string delimiter = ",")
            => source.Any()
                ? source.Aggregate("", (s, i) => (s ?? "") + i + delimiter)[..^1]
                : string.Empty;

        public static T[] SingleToArray<T>(this T source)
            => new[] { source };
    }
}