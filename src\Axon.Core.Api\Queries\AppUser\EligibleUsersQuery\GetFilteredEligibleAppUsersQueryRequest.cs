﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.User;
using Axon.Core.Shared.Api;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.AppUser.IdQuery
{
    public class GetFilteredEligibleAppUsersQueryRequest: IRequest<CommandResponse<ApiPagedListResult<UserModel>>>
    {
        public GetFilteredEligibleAppUsersQueryRequest(string organisationCodeName, string appCodeName, ListParams listParams)
        {
            OrganisationCodeName = organisationCodeName;
            AppCodeName = appCodeName;
            ListParams = listParams;
        }

        public string OrganisationCodeName { get; }
        public string AppCodeName { get; }
        public ListParams ListParams { get; }
    }
}
