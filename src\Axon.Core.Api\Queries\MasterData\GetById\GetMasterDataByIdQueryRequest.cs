using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.MasterData;
using MediatR;

namespace Axon.Core.Api.Queries.MasterData.GetById;

public class GetMasterDataByIdQueryRequest : IRequest<CommandResponse<MasterDataModel>>
{
    public string DataType { get; }
    public string MasterDataId { get; }

    public GetMasterDataByIdQueryRequest(string dataType, string masterDataId)
    {
        DataType = dataType;
        MasterDataId = masterDataId;
    }
}