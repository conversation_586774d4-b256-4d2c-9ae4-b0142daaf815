﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.OrganisationAccess;

public class ScopeResourceDataModel
{
    public ScopeResourceDataModel(string id, string name)
    {
        Name = name;
        Id = id;
    }

    [Required] public string Name { get; set; }
    [Required] public string Id { get; set; }
}

public class ScopeDataModel
{
    public ScopeDataModel(string scopeName, IEnumerable<ScopeResourceDataModel> resources)
    {
        Scope = scopeName;
        Resources = resources;
    }

    [Required] public string Scope { get; set; }
    [Required] public IEnumerable<ScopeResourceDataModel> Resources { get; set; }
}