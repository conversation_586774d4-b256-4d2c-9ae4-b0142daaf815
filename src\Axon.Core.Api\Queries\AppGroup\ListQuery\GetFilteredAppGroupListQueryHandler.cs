﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.AppGroup.ListQuery
{
    [UsedImplicitly]
    internal class GetFilteredAppGroupListQueryHandler : IRequestHandler<GetFilteredAppGroupListQueryRequest, CommandResponse<ApiPagedListResult<AppGroupModel>>>
    {
        private readonly IAppGroupRepository appGroupRepository;
        private readonly IAccessRepository accessRepository;
        private readonly IMapper mapper;

        public GetFilteredAppGroupListQueryHandler(IAppGroupRepository appGroupRepository, IAccessRepository accessRepository, IMapper mapper)
        {
            this.appGroupRepository = appGroupRepository;
            this.accessRepository = accessRepository;
            this.mapper = mapper;
        }

        public async Task<CommandResponse<ApiPagedListResult<AppGroupModel>>> Handle(GetFilteredAppGroupListQueryRequest request, CancellationToken cancellationToken)
        {
            var appGroupEntities = await appGroupRepository.GetAppGroupsAsync(request.OrganisationCodeName, request.AppCodeName, request.ListParams);
            var totalItemsCount = await appGroupRepository.GetTotalItemsCountAsync(request.OrganisationCodeName, request.AppCodeName, request.ListParams);
            
            if (!appGroupEntities.Any())
            {
                return CommandResponse<ApiPagedListResult<AppGroupModel>>.Data(new ApiPagedListResult<AppGroupModel>(new List<AppGroupModel>(), request.ListParams.Offset, request.ListParams.Limit, totalItemsCount));
            }

            var groupsMembersCount = await accessRepository.GetGroupsMembersCountAsync(request.OrganisationCodeName, request.AppCodeName, appGroupEntities.Select(x => x.GroupId).ToList());

            var appGroupModels = appGroupEntities.Select(appGroup =>
            {
                var appGroupModel = mapper.Map<AppGroupModel>(appGroup);
                appGroupModel.MembersCount = groupsMembersCount.SingleOrDefault(x => x.GroupId.Equals(appGroup.GroupId))?.Count ?? 0;

                return appGroupModel;
            });

            return CommandResponse<ApiPagedListResult<AppGroupModel>>.Data(new ApiPagedListResult<AppGroupModel>(appGroupModels, request.ListParams.Offset, request.ListParams.Limit, totalItemsCount));
        }
    }
}
