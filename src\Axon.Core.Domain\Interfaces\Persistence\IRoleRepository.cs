﻿using Axon.Core.Domain.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Domain.Interfaces.Persistence
{
    public interface IRoleRepository : IRepository<RoleEntity>
    {
        Task<IList<RoleEntity>> GetDefaultAndCustomRolesAsync(string orgCodeName, string appCodeName, object listParams);
        Task<IList<RoleEntity>> GetDefaultAndCustomRolesAsync(string orgCodeName, string appCodeName);
        Task<IList<RoleEntity>> GetOverriddenRolesAsync(string orgCodeName, string appCodeName);
        Task<RoleEntity> GetOverrideAsync(string id, string orgCodeName, string appCodeName);
        Task<IReadOnlyCollection<RoleEntity>> GetDefaultRolesByName(IEnumerable<string> roleNames, string appCodeName);
        Task<int> GetTotalItemsCountAsync(string orgCodeName, string appCodeName, object listParams);
        Task<bool> RoleNameExistsAsync(string orgCodeName, string appCodeName, string roleName);
        /// <summary>
        /// Determines if the name is in use already, but excludes the document with the matching id to allow for updates.
        /// </summary>
        Task<bool> RoleNameExistsAsync(string orgCodeName, string appCodeName, string roleName, string id);
        Task<IReadOnlyCollection<RoleEntity>> GetRolesAsync(IEnumerable<string> roleIds);
        Task<bool> RoleExistsForAppAsync(string orgCodeName, string appCodeName, string roleName, string id);

        /// <summary>
        /// Gets the enabled <see cref="RoleEntity"'s for the provided role ids, and any overrides that exist for the provided organisations/>
        /// </summary>
        /// <param name="roleIds">Role ids to retrieve</param>
        /// <param name="orgCodeNames">Organisations who's overrides we are interested in</param>
        /// <returns></returns>
        Task<IReadOnlyCollection<RoleEntity>> GetEnabledRolesAsync(IEnumerable<string> roleIds, IEnumerable<string> orgCodeNames);

        /// <summary>
        /// Finds which of the provided resources are in use
        /// </summary>
        /// <param name="orgCodeName">Organisation code</param>
        /// <param name="appCodeName">Application Code</param>
        /// <param name="scope">Scope to find resources for</param>
        /// <param name="resources">Resources to find</param>
        /// <returns>The set of provided resources that are in use for the provided organisation / application</returns>
        Task<IReadOnlyCollection<RoleEntity.Resource>> GetInUseResources(string orgCodeName, string appCodeName, string scope, IReadOnlyCollection<RoleEntity.Resource> resources);
    }
}
