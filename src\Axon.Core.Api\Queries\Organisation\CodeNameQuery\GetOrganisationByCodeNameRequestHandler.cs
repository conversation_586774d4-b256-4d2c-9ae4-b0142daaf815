﻿using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Api.Services.Organisation;
using MediatR;

namespace Axon.Core.Api.Queries.Organisation.CodeNameQuery
{
    internal class GetOrganisationByCodeNameRequestHandler : IRequestHandler<GetOrganisationByCodeNameRequest, CommandResponse<OrganisationModel>>
    {
        private readonly IOrganisationRequestService organisationRequestService;

        public GetOrganisationByCodeNameRequestHandler(IOrganisationRequestService organisationRequestService)
        {
            this.organisationRequestService = organisationRequestService;
        }

        public async Task<CommandResponse<OrganisationModel>> Handle(GetOrganisationByCodeNameRequest request, CancellationToken cancellationToken)
        {
            return await organisationRequestService.GetOrganisationByCode(request.CodeName);
        }
    }
}
