﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.Audit;

[UsedImplicitly]
public class GetAuditFiltersQueryRequestValidator : AbstractValidator<GetAuditFiltersQueryRequest>
{
    public GetAuditFiltersQueryRequestValidator()
    {
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.OrgCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.Host)
            .MustBeAValidHostname();
    }
}