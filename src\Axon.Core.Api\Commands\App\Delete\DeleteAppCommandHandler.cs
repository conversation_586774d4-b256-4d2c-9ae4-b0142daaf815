using AutoMapper;
using Axon.Core.Api.Models.App;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.App.Delete;

[UsedImplicitly]
internal class DeleteAppCommandHandler : DeleteCommandHandler<AppEntity, AppModel, AppDeleted>
{
    public DeleteAppCommandHandler(IAppRepository repo, IMapper mapper, IMessageBus messageBus) : base(repo, mapper, messageBus)
    {
    }
}