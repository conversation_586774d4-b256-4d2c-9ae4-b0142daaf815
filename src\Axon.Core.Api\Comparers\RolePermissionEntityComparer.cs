﻿using Axon.Core.Domain.Entities;
using System.Collections.Generic;

namespace Axon.Core.Api.Comparers
{
    public class RolePermissionEntityComparer : IEqualityComparer<RolePermissionEntity.Permission>
    {
        public bool Equals(RolePermissionEntity.Permission x, RolePermissionEntity.Permission y)
        {
            return y != null && x != null &&
                   x.Name == y.Name;
        }

        public int GetHashCode(RolePermissionEntity.Permission obj)
        {
            return obj.Name.GetHashCode();
        }
    }
}
