﻿using Axon.Core.Domain.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Linq;
using Axon.Core.Domain.Interfaces.Persistence;

namespace Axon.Core.Api.Services.UserOrganisation;

public interface IOrganisationUserProvider
{
    Task<(UserEntity user, OrganisationEntity userOrganisation)> ProvideAsync(IList<UserEntity> users, OrganisationEntity organisation, AccessEntity accessEntity, Dictionary<string, OrganisationEntity> userOrganisations);
}

public class OrganisationUserProvider : IOrganisationUserProvider
{
    private readonly IOrganisationRepository organisationRepository;

    public OrganisationUserProvider(IOrganisationRepository organisationRepository)
    {
        this.organisationRepository = organisationRepository;
    }

    public async Task<(UserEntity user, OrganisationEntity userOrganisation)> ProvideAsync(IList<UserEntity> users, OrganisationEntity organisation, AccessEntity accessEntity, Dictionary<string, OrganisationEntity> userOrganisations)
    {
        var user = users.FirstOrDefault(entity => string.Equals(entity.Id, accessEntity.User.Id, StringComparison.OrdinalIgnoreCase));

        if (string.IsNullOrWhiteSpace(user?.OwnerOrganisationId))
            return (user, null);

        if (string.Equals(user.OwnerOrganisationId, organisation.Id, StringComparison.OrdinalIgnoreCase))
            return (user, organisation);

        if (userOrganisations.TryGetValue(user.OwnerOrganisationId, out var userOrganisation))
            return (user, userOrganisation);

        userOrganisation = await organisationRepository.GetItemAsync(user.OwnerOrganisationId);
        userOrganisations.Add(user.OwnerOrganisationId, userOrganisation);

        return (user, userOrganisation);
    }
}