using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.App.Create;

internal class CreateAppCommandHandler
    : BaseCreateCommandHandler<AppBody, AppEntity, AppCreated>
{
    public CreateAppCommandHandler(IAppRepository repo, IMapper mapper, IMessageBus messageBus)
        : base(repo, mapper, messageBus)
    {
    }

    public override async Task<CommandResponse> Handle(CreateCommandRequest<AppBody> request, CancellationToken cancellationToken)
    {
        var appRepository = (IAppRepository) Repo;
        var existing = await appRepository.GetItemByDisplayNameAsync(request.Model.DisplayName);

        if (existing != null) return CommandResponse.Failed(nameof(request.Model.DisplayName), $"App `{request.Model.DisplayName}` already exists");

        existing = await appRepository.GetItemByAppCodeNameAsync(request.Model.AppCodeName);
        if (existing != null) return CommandResponse.Failed(nameof(request.Model.AppCodeName), $"App with application CodeName `{request.Model.AppCodeName}` already exists.");
        else return await base.Handle(request, cancellationToken);
           
    }
}