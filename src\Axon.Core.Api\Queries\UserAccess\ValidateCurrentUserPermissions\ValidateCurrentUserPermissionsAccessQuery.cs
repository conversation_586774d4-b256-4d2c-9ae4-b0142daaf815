﻿using System.Collections.Generic;
using Axon.Core.Api.Commands;
using MediatR;

namespace Axon.Core.Api.Queries.UserAccess.ValidateCurrentUserPermissions;

public class ValidateCurrentUserPermissionsAccessQuery : IRequest<CommandResponse<ValidateCurrentUserPermissionsAccessQueryResponse>>
{
    public ValidateCurrentUserPermissionsAccessQuery(List<string> permissions, string appCodeName = null, string orgCodeName = null)
    {
        Permissions = permissions;
        AppCodeName = appCodeName;
        OrgCodeName = orgCodeName;
    }

    public List<string> Permissions { get; }
    public string AppCodeName { get; }
    public string OrgCodeName { get; }
}