﻿using System;
using System.Threading.Tasks;
using Axon.Core.Infrastructure.Auth;
using CommunityToolkit.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;

namespace Axon.Core.Api.Attributes;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public class HasPermissionsAttribute : HasAuthorizationBaseAttribute
{
    public readonly string Permission;

    /// <summary>
    ///     Define one or more permissions that are allowed to access a particular resource.
    ///     This permission check is done at the user level, so will check they have it at all, not whether they have the permission in regard to a specific organisation.
    /// </summary>
    /// <param name="permissionsKey">string permissions required to access a particular resource</param>
    /// </param>
    public HasPermissionsAttribute(string permissionsKey)
    {
        Guard.IsNotNullOrEmpty(permissionsKey);

        Permission = permissionsKey;
    }

    protected override async Task Authorise(AuthorizationFilterContext context)
    {
        (var _, var accessGate, var authenticationSchemeProvider, var logger) = GetStandardServices<HasPermissionsAttribute>(context);
        (var validatedClaims, var errorResponse) = TryGetStandardClaims(context, logger);

        if(validatedClaims == null)
        {
            context.Result =  errorResponse;
            return;
        }

        if (!await accessGate.IsAuthorised(ClaimsExtensions.AxonCoreAppName, requestedPermissions:Permission))
        {
            logger.LogDebug("Auth failed: For user with sub/nameidentifier `{OidClaim}`, permission (`{Permission}`) denied", validatedClaims.OidClaim, Permission);
            context.Result = new ForbidResult(authenticationSchemeProvider.AllSchemes());
        }
    }
}
