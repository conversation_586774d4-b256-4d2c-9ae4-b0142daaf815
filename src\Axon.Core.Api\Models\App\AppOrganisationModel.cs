﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Axon.Core.Api.Models.App
{
    public class AppOrganisationModel
    {

        [Required]
        public string Id { get; set; }
        [Required]
        public string DisplayName { get; set; }

        [Required]
        public string Description { get; set; }
        [Required]
        public string Icon { get; set; }
        [Required]
        public string CodeName { get; set; }
        [Required]
        public string AppCodeName { get; set; }

        [Required]
        public bool IsDeleted { get; set; }
        [Required]
        public bool IsEnabled { get; set; }

        [Required]
        public bool IsSystemApp { get; set; }

        [Required]
        public string Status { get; set; }

        [Required]
        public bool IsDefault { get; set; }

        [Required]
        public string RunAs { get; set; }

        [Required]
        public string Url { get; set; }

        [Required]
        public bool ShowAuditLog { get; set; }

        [Required]
        public bool HasAccess { get; set; }

        public AppThemeConfigModel Theme { get; set; }
    }
}
