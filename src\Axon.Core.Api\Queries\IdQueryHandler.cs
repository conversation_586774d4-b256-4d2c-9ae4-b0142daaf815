﻿using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Extensions;
using Axon.Core.Domain.Entities.Base;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using MediatR;

namespace Axon.Core.Api.Queries
{
    internal class IdQueryHandler<TEntity, TModel> : IRequestHandler<IdQueryRequest<TModel>, CommandResponse<TModel>> where TEntity : BaseEntity
    {
        private readonly IRepository<TEntity> repo;
        private readonly IMapper mapper;

        public IdQueryHandler(IRepository<TEntity> repo, IMapper mapper)
        {
            Guard.IsNotNull(repo);
            this.repo = repo;
            Guard.IsNotNull(mapper);
            this.mapper = mapper;
        }

        public async Task<CommandResponse<TModel>> Handle(IdQueryRequest<TModel> request, CancellationToken cancellationToken)
        {
            var entity = await repo.GetItemOrThrowAsync(request.Id);
            
            return CommandResponse<TModel>.Data(mapper.Map<TModel>(entity));
        }
    }
}