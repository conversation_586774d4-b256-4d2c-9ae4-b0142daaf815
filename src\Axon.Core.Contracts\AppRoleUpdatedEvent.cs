﻿using System.Text.Json.Serialization;

namespace Axon.Core.Contracts
{
    public class AppRoleUpdatedEvent
    {
        public string OrgId { get; set; }
        public string OrgCodeName { get; set; }
        public string AppId { get; set; }
        public string AppCodeName { get; set; }
        public string RoleId { get; set; }
        [JsonConverter(typeof(JsonStringEnumConverter<EventActionType>))]
        public EventActionType Action { get; set; }
    }
}
