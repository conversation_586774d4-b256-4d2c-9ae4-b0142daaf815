﻿using System;
using System.Reflection;
using Asp.Versioning;
using Asp.Versioning.ApiExplorer;

namespace Axon.Core.Api.Infrastructure
{
    public static class VersionConfiguration
    {
        private static readonly Version Version = Assembly.GetEntryAssembly()?.GetName().Version;
        public static readonly string DisplayVersion = $"{Version.Major}.{Version.Minor}.{Version.Build}.{Version.Revision}";
        public static readonly ApiVersion ApiVersion = new ApiVersion(1, 0);

        public static void ExplorerOptions(ApiExplorerOptions options)
        {
            // add the versioned API explorer, which also adds IApiVersionDescriptionProvider service
            // note: the specified format code will format the version as "'v'major[.minor][-status]"
            options.GroupNameFormat = "'v'V";

            // note: this option is only necessary when versioning by URL segment. the SubstitutionFormat
            // can also be used to control the format of the API version in route templates
            options.SubstituteApiVersionInUrl = true;
        }

        public static void VersioningOptions(ApiVersioningOptions options)
        {
            // Advertise the API versions supported for the particular endpoint
            options.ReportApiVersions = true;
            // If the client hasn't specified the API version in the request, use the default API version number
            options.AssumeDefaultVersionWhenUnspecified = true;
            // Specify the default API Version as 1.0
            options.DefaultApiVersion = ApiVersion;
        }
    }
}
