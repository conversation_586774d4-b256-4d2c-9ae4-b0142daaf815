﻿using System.Threading.Tasks;

namespace Axon.Core.Domain.Interfaces.Access
{
    internal interface IContextUserAccessGate
    {
        /// <summary>
        /// Determines if the current user has the requested permission(s) on the provided org and application.
        /// This includes:
        ///     Does the user have the fundamental ability to see the organisation?
        ///     Does the organisation have the application enabled on it?
        ///     Does the user have all of the requested permissions against that application and organisation?
        /// </summary>
        /// <param name="appCodeName">The code for the application to check</param>
        /// <param name="orgCodeName">The code for the organiastion to check</param>
        /// <param name="requestedPermissions">The permissions the user requires</param>
        /// <returns>True if the user can be considered to have the requested permissions against the provided application and organisation</returns>
        Task<bool> IsAuthorisedForOrganisation(string appCodeName, string orgCodeName, params string[] requestedPermissions);

        /// <summary>
        /// Determines if the current user has the requested permission(s) on the provided org and application.
        /// This includes:
        ///     Does the user have the fundamental ability to see the organisation?
        ///     Does the organisation have the application enabled on it?
        ///     Does the user have all of the requested permissions against that application and organisation?
        /// </summary>
        /// <param name="appCodeName">The code for the application to check</param>
        /// <param name="organisationId">The id for the organiastion to check</param>
        /// <param name="requestedPermissions">The permissions the user requires</param>
        /// <returns>True if the user can be considered to have the requested permissions against the provided application and organisation</returns>
        Task<bool> IsAuthorisedForOrganisationById(string appCodeName, string organisationId, params string[] requestedPermissions);

        /// <summary>
        /// Determines if the current user has the requested permission(s) on the provided org and application.
        /// This includes:
        ///     Does the user have the fundamental ability to see the organisation?
        ///     Does the organisation have the application enabled on it?
        ///     Does the user have all of the requested permissions against that application and organisation?
        /// </summary>
        /// <param name="appCodeName">The code for the application to check</param>
        /// <param name="requestedPermissions">The permissions the user requires</param>
        /// <returns>True if the user can be considered to have the requested permissions against the provided application and organisation</returns>
        Task<bool> IsAuthorised(string appCodeName, params string[] requestedPermissions);

        /// <summary>
        /// Determines if the current user has any role.
        /// This includes:
        ///     Does the user have any role assigned to the application?
        /// </summary>
        /// <param name="appCodeName">The code for the application to check</param>
        /// <returns>True if the user can be considered to have any role</returns>
        Task<bool> IsAuthorisedByRoles(string appCodeName, string orgCodeName);
    }
}
