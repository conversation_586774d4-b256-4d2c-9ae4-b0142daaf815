/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// UserIdentityProviderModel
    /// </summary>
    [DataContract(Name = "UserIdentityProviderModel")]
    public partial class UserIdentityProviderModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UserIdentityProviderModel" /> class.
        /// </summary>
        /// <param name="name">name.</param>
        /// <param name="type">type.</param>
        /// <param name="autoLogoutTime">autoLogoutTime.</param>
        /// <param name="autoLogoutWarningTime">autoLogoutWarningTime.</param>
        public UserIdentityProviderModel(string name = default(string), string type = default(string), int? autoLogoutTime = default(int?), int? autoLogoutWarningTime = default(int?))
        {
            this.Name = name;
            this.Type = type;
            this.AutoLogoutTime = autoLogoutTime;
            this.AutoLogoutWarningTime = autoLogoutWarningTime;
        }

        /// <summary>
        /// Gets or Sets Name
        /// </summary>
        [DataMember(Name = "name", EmitDefaultValue = true)]
        public string Name { get; set; }

        /// <summary>
        /// Gets or Sets Type
        /// </summary>
        [DataMember(Name = "type", EmitDefaultValue = true)]
        public string Type { get; set; }

        /// <summary>
        /// Gets or Sets AutoLogoutTime
        /// </summary>
        [DataMember(Name = "autoLogoutTime", EmitDefaultValue = true)]
        public int? AutoLogoutTime { get; set; }

        /// <summary>
        /// Gets or Sets AutoLogoutWarningTime
        /// </summary>
        [DataMember(Name = "autoLogoutWarningTime", EmitDefaultValue = true)]
        public int? AutoLogoutWarningTime { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class UserIdentityProviderModel {\n");
            sb.Append("  Name: ").Append(Name).Append("\n");
            sb.Append("  Type: ").Append(Type).Append("\n");
            sb.Append("  AutoLogoutTime: ").Append(AutoLogoutTime).Append("\n");
            sb.Append("  AutoLogoutWarningTime: ").Append(AutoLogoutWarningTime).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
