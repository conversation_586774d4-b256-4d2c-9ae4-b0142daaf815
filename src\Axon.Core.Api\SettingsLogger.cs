using System;
using Axon.Core.Infrastructure.AppSettings;
using Axon.Core.Infrastructure.Extensions;
using CommunityToolkit.Diagnostics;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus.Configuration;

namespace Axon.Core.Api
{
    public static class SettingsLogger<T>
    {
        private const string AspNetCoreEnvironment = "ASPNETCORE_ENVIRONMENT";
        private static ILogger<T> logger;

        public static ILogger<T> LogAppSettingsUsed(IServiceProvider services)
        {
            if (logger != null) return logger;

            logger = (ILogger<T>)services.GetService(typeof(ILogger<T>));
            logger.LogInformation($"Current Dir: `{Environment.CurrentDirectory}`");

            var hostEnv = (IWebHostEnvironment) services.GetService(typeof(IWebHostEnvironment));
            logger.LogInformation($"Host Environment from App determined as: {hostEnv?.EnvironmentName ?? "(not-set)"}");

            var hostEnvVar = Environment.GetEnvironmentVariable(AspNetCoreEnvironment) ?? "(not-set)";
            logger.LogInformation($"Host Environment from `{AspNetCoreEnvironment}` determined as: {hostEnvVar}");

            var config = (IConfiguration)services.GetService(typeof(IConfiguration));
            Guard.IsNotNull(config);

            LogCosmosSettings(config.CosmosDbSettings());

            LogMessageBusSettings(config.MessageBus());
            
            return logger;
        }

        private static void LogCosmosSettings(CosmosDbSettings cosmosDbSettings)
        {
            var cfg = cosmosDbSettings;

            logger.LogDebug(" Cosmos settings");
            logger.LogDebug($"   Database : {cfg.DatabaseName}");
            logger.LogDebug($"   Endpoint : {cfg.EndpointUrl}");
        }

        private static void LogMessageBusSettings(MessageBusSettings messageBusSettings)
        {

            var cfg = messageBusSettings;

            logger.LogDebug(" MessageBus settings");
            logger.LogDebug($"   TransportType : {cfg.TransportType}");

            switch (cfg.TransportType)
            {
                case nameof(TransportType.RabbitMq):
                    logger.LogDebug($"   Host : {cfg.RabbitMq.Host}");
                    logger.LogDebug($"   Port : {cfg.RabbitMq.Port}");
                    logger.LogDebug($"   Username : {cfg.RabbitMq.Username}");
                    break;
            }
        }

    }
}