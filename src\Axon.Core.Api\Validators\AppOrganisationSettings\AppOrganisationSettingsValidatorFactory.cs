﻿using System;
using Axon.Core.Domain.Enums;

namespace Axon.Core.Api.Validators.AppOrganisationSettings
{
    public interface IAppOrganisationSettingsValidatorFactory
    {
        ISettingValidator Create(SettingDataType settingDataType);
    }

    internal class AppOrganisationSettingsValidatorFactory : IAppOrganisationSettingsValidatorFactory
    {
        private readonly Func<SettingDataType, ISettingValidator> participantFactory;

        public AppOrganisationSettingsValidatorFactory(Func<SettingDataType, ISettingValidator> participantFactory)
        {
            this.participantFactory = participantFactory;
        }

        public ISettingValidator Create(SettingDataType settingDataType)
        {
            return participantFactory(settingDataType);
        }
    }
}
