﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Role;
using MediatR;
using System.Collections.Generic;

namespace Axon.Core.Api.Queries.RoleDefinition.GetByAppOrg
{
    public class GetOrgSpecificRoleDefinitionQueryRequest : IRequest<CommandResponse<IEnumerable<AppRoleDefinitionPermission>>>
    {
        public GetOrgSpecificRoleDefinitionQueryRequest(string appCode, string orgCodeName)
        {
            AppCode = appCode;
            OrgCodeName = orgCodeName;
        }
        public string OrgCodeName { get; }
        public string AppCode { get; }

    }
}
