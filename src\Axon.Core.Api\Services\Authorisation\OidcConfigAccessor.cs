﻿using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Services.Authorisation
{
    internal interface IOidcConfigAccessor
    {
        Task<OpenIdConnectConfiguration> GetAsync(string url);
    }
    internal class OidcConfigAccessor : IOidcConfigAccessor
    {
        public async Task<OpenIdConnectConfiguration> GetAsync(string url)
        {
            return await OpenIdConnectConfigurationRetriever.GetAsync(url, CancellationToken.None);
        }
    }
}
