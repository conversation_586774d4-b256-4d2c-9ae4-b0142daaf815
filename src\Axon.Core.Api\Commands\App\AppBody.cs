﻿namespace Axon.Core.Api.Commands.App
{
    public class AppBody
    {
        public string DisplayName { get; }
        public string Description { get; }
        public string Icon { get; }
        public string AppCodeName { get; }

        public bool IsDeleted { get; }
        public bool IsEnabled { get; }
        public bool IsDefault { get; }
        public bool IsSystemApp { get; set; }
        public string ClientId { get; set; }
        public string RunAs { get; set; }

        #pragma warning disable S107
        public AppBody(string displayName, string description, string icon, string appCodeName, bool isDeleted, bool isEnabled, bool isDefault,
            bool isSystemApp, string clientId, string runAs)
        #pragma warning restore S107
        {
            DisplayName = displayName;
            Description = description;
            Icon = icon;
            AppCodeName = appCodeName;
            IsDeleted = isDeleted;
            IsEnabled = isEnabled;
            IsDefault = isDefault;
            IsSystemApp = isSystemApp;
            ClientId = clientId;
            RunAs = runAs;
        }
    }
}
