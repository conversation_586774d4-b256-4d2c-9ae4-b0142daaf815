using System.Collections.Generic;
using System.Threading.Tasks;
using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Filters;
using Axon.Core.Api.Models.MasterData;
using Axon.Core.Api.Queries.MasterData.GetById;
using Axon.Core.Api.Queries.MasterData.ListQuery;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Axon.Core.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("v{version:apiVersion}/MasterData/{dataType}")]
[ProducesResponseType(401, Type = typeof(CommandResponse))]
[ProducesResponseType(403, Type = typeof(CommandResponse))]
[ProducesResponseType(500, Type = typeof(CommandResponse))]
[Authorize]
public class MasterDataController : ApiControllerBase
{
    private readonly IValidator<GetFilteredMasterDataListQueryRequest> getFilteredMasterDataListQueryRequestValidator;
    private readonly IValidator<GetMasterDataByIdQueryRequest> getMasterDataByIdQueryRequestValidator;

    public MasterDataController(IMediator mediator,
        IValidator<GetFilteredMasterDataListQueryRequest> getFilteredMasterDataListQueryRequestValidator,
        IValidator<GetMasterDataByIdQueryRequest> getMasterDataByIdQueryRequestValidator) : base(mediator)
    {
        this.getFilteredMasterDataListQueryRequestValidator = getFilteredMasterDataListQueryRequestValidator;
        this.getMasterDataByIdQueryRequestValidator = getMasterDataByIdQueryRequestValidator;
    }

    [HttpGet(Name = "GetAllMasterDataForType")]
    [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<MasterDataModel>>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetAllMasterDataForType([FromRoute] string dataType)
    {
        var request = new GetFilteredMasterDataListQueryRequest(dataType);
        var validationResult = await getFilteredMasterDataListQueryRequestValidator
            .ValidateAsync(request);

        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpGet("{id}", Name = "GetMasterDataById")]
    [ProducesResponseType(200, Type = typeof(CommandResponse<MasterDataModel>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetMasterDataByIdAsync([FromRoute] string dataType, [FromRoute] string id)
    {
        var request = new GetMasterDataByIdQueryRequest(dataType, id);
        var validationResult = await getMasterDataByIdQueryRequestValidator
            .ValidateAsync(request);

        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }
}