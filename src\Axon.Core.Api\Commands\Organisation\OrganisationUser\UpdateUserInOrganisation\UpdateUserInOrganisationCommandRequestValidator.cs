﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser.UpdateUserInOrganisation;

[UsedImplicitly]
public class UpdateUserInOrganisationCommandRequestValidator : AbstractValidator<UpdateUserInOrganisationCommandRequest>
{
    public UpdateUserInOrganisationCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.UserId)
            .MustBeAValidGuid();
    }
}