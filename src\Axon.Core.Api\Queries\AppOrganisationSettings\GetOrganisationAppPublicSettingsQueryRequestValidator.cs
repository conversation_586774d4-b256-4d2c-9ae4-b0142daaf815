﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.AppOrganisationSettings;

[UsedImplicitly]
public class GetOrganisationAppPublicSettingsQueryRequestValidator : AbstractValidator<GetOrganisationAppPublicSettingsQueryRequest>
{
    public GetOrganisationAppPublicSettingsQueryRequestValidator()
    {
        RuleFor(x => x.OrgCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
    }
}