﻿using JetBrains.Annotations;

namespace Axon.Core.Api.Models.Organisation
{
    public class UpdateOrganisationBody
    {
        public string DisplayName { get; }
        public string Description { get; }
        public string Icon { get; }
        public string AccessLevel { get; }
        public bool IsDeleted { get; }
        public bool IsEnabled { get; }
        public SetupThemeBody Theme { get; }
        [CanBeNull]
        public string ParentOrganisationId { get; }

        public UpdateOrganisationBody(string displayName, string description, string icon, string accessLevel, bool isDeleted, bool isEnabled, SetupThemeBody theme = default(SetupThemeBody), string parentOrganisationId = null)
        {
            DisplayName = displayName;
            Description = description;
            Icon = icon;
            AccessLevel = accessLevel;
            IsDeleted = isDeleted;
            IsEnabled = isEnabled;
            Theme = theme;
            ParentOrganisationId = parentOrganisationId;
        }
    }
}
