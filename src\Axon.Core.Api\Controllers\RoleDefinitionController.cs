﻿using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Role;
using Axon.Core.Api.Services.Authorisation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentValidation;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Queries.RoleDefinition.GetByAppOrg;
using Axon.Core.Api.Queries.RoleDefinition.RoleDefinitionListQuery;

namespace Axon.Core.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("v{version:apiVersion}")]
[ProducesResponseType(401, Type = typeof(CommandResponse))]
[ProducesResponseType(403, Type = typeof(CommandResponse))]
[ProducesResponseType(500, Type = typeof(CommandResponse))]
[Authorize]
public class RoleDefinitionController : ApiControllerBase
{
    private readonly IValidator<GetRoleDefinitionQueryRequest> getRoleDefinitionListQuery;
    private readonly IValidator<GetOrgSpecificRoleDefinitionQueryRequest> getOrgSpecificRoleDefinitionListQuery;

    public RoleDefinitionController(IMediator mediator, 
        IValidator<GetRoleDefinitionQueryRequest> getRoleDefinitionListQuery,
        IValidator<GetOrgSpecificRoleDefinitionQueryRequest> getOrgSpecificRoleDefinitionListQuery) : base(mediator)
    {
        this.getRoleDefinitionListQuery = getRoleDefinitionListQuery;
        this.getOrgSpecificRoleDefinitionListQuery = getOrgSpecificRoleDefinitionListQuery;

    }

    [HttpGet("App/{appCodeName}/role-definition", Name = "GetByAppCode")]
    [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<AppRoleDefinitionPermission>>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [HasPermissions(permissionsKey: nameof(CorePermissions.EditOrganisation))]
    public async Task<IActionResult> GetRoleDefinitionByAppCodeAsync(string appCodeName)
    {
        var request = new GetRoleDefinitionQueryRequest(appCodeName);
        var validationResult = await getRoleDefinitionListQuery.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpGet("Organisation/{orgCodeName}/App/{appCodeName}/role-definition", Name = "GetByOrganisationAndAppCode")]
    [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<AppRoleDefinitionPermission>>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [HasOrganisationPermissionsAttribute(permissionsKey: nameof(CorePermissions.EditOrganisation))]
    public async Task<IActionResult> GetRoleDefinitionByOrganisationAndAppCodeAsync(string orgCodeName, string appCodeName)
    {
        var request = new GetOrgSpecificRoleDefinitionQueryRequest(appCodeName, orgCodeName);
        var validationResult = await getOrgSpecificRoleDefinitionListQuery.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }
}