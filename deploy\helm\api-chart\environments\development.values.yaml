api:
  tag: dev

grpc:
  tag: dev

ingress:
  tls:
    - tlsSecretName: tls-app-dev-smartphlex-com
      hosts:
        - app-dev.smartphlex.com
  hosts:
    - host: app-dev.smartphlex.com
      paths:
        - path: /api/core/(.*)
          pathType: ImplementationSpecific

newrelic_api_app_name: axon-core-dev-api
newrelic_grpc_app_name: axon-core-dev-grpc
keyVaultName: axn-dev-kv-eun
clientId: 167cd45b-7d4f-4b3d-8c05-a87f12c40609
azureIssuer: https://login.microsoftonline.com/common/v2.0
azureUseCustomRefresh: false

gigyaClientId: cUDSdI53tU5LgmVJH2AkCH-8
gigyaIssuer: https://tst.aaas.cencora.com/oidc/op/v1.0/4_Pv18t6XTOc51PxyYytQzHA/authorize
gigyaUseCustomRefresh: true


corsOriginUrl0: https://app-dev.smartphlex.com
corsOriginUrl1: https://localhost:4000

BlobStorageConnectionString: https://phcgvsharedstaticeun.blob.core.windows.net/

NamespaceName: "axn-dev-servicebus-eun"

cosmosdbName: Axon-Core-ApiDb
cosmosdbUrl: 'https://axn-dev-cosmos-eun.documents.azure.com:443/'

managedIdentityClientId: c1465e0f-ca24-4384-b1e9-05d273a4b6c1
azureWorkload:
  clientId: c1465e0f-ca24-4384-b1e9-05d273a4b6c1
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AzureBlobStorageContainersAppAvatarsFolderPrefix: dev/axon-avatar
AzureBlobStorageContainersOrganisationAvatarsFolderPrefix: dev/organisations
AzureBlobStorageContainersThemesFolderPrefix: dev/axon-theme

DataProtectionBlobStorageUri: 'https://axndevstorageeun.blob.core.windows.net/'
DataProtectionKeyVaultKey: 'https://axn-dev-kv-eun.vault.azure.net/keys/AxonDataProtection'

GoodDataBaseUri: 'https://phlexglobal-dev.cloud.gooddata.com/'
GoodDataEnvironment: 'dev'
