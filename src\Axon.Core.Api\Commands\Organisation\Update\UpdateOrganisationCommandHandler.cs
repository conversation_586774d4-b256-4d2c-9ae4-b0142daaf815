using AutoMapper;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Contracts;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Domain.Services.Access;
using Axon.Core.Domain.Services.AzureBlobStorage;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;
using System;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Commands.Organisation.Update;

internal class UpdateOrganisationCommandHandler : BaseUpdateCommandHandler<UpdateOrganisationBody, OrganisationEntity, OrganisationUpdated>
{
    private readonly IOrganisationThemeAzureBlobStorageManager organisationThemeAzureBlobStorageManager;
    private readonly ILogger<UpdateOrganisationCommandHandler> logger;
    private readonly IAccessLevelProvider accessLevelProvider;
    private readonly IClientDetailsProvider clientDetailsProvider;
    private readonly ICorrelationIdProvider correlationIdProvider;
    private readonly IAuditService<TenantAuditExtensions> auditService;

    public UpdateOrganisationCommandHandler(
        IOrganisationRepository repo,
        IMapper mapper,
        IMessageBus messageBus,
        IOrganisationThemeAzureBlobStorageManager organisationThemeAzureBlobStorageManager,
        ILogger<UpdateOrganisationCommandHandler> logger,
        IAccessLevelProvider accessLevelProvider,
        IClientDetailsProvider clientDetailsProvider,
        ICorrelationIdProvider correlationIdProvider,
        IAuditService<TenantAuditExtensions> auditService) :
        base(repo, mapper, messageBus)
    {
        this.organisationThemeAzureBlobStorageManager = organisationThemeAzureBlobStorageManager;
        this.logger = logger;
        this.accessLevelProvider = accessLevelProvider;
        this.clientDetailsProvider = clientDetailsProvider;
        this.correlationIdProvider = correlationIdProvider;
        this.auditService = auditService;
    }

    public override async Task<CommandResponse> Handle(UpdateCommandRequest<UpdateOrganisationBody> request, CancellationToken cancellationToken)
    {
        var organisationRepository = (IOrganisationRepository)Repo;

        var existing = (await organisationRepository.GetItemsByDisplayNameAsync(request.Model.DisplayName)).AsEnumerable();
        if (existing.Any(e => e.Id != request.Id))
            return CommandResponse.Failed(nameof(request.Model.DisplayName), $"Organisation `{request.Model.DisplayName}` already exists");

        if (!await Repo.TryGetItemAsync(request.Id, out var entity))
        {
            return CommandResponse.NotFound(nameof(OrganisationEntity), request.Id);
        }

        var userAccessLevel = await accessLevelProvider.ProvideAsync();

        var (validationError, organisationAccessLevel) = await ValidateOrganisationAccessLevel(userAccessLevel, request, entity, organisationRepository);
        if (validationError != null)
            return validationError;

        validationError = await ValidateParentOrganisation(userAccessLevel, request, entity, organisationRepository);
        if (validationError != null)
            return validationError;

        var stylesheetUrl = string.Empty;
        try
        {
            if (request.Model.Theme != null && !request.Model.Theme.IsEmpty())
            {
                stylesheetUrl = await UpdateOrganisationStylesheetUrl(request, entity.CodeName, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error with organisation theme setup occurred");
            return CommandResponse.Failed(nameof(request.Model.Theme), "Error with organisation theme setup occurred");
        }

        await AuditOrganisationUpdate(request, entity, organisationAccessLevel, stylesheetUrl);

        await SendMessage(entity.Id, entity);

        return CommandResponse.Success();
    }

    private static async Task<(CommandResponse, AccessLevel)> ValidateOrganisationAccessLevel(AccessLevel userAccessLevel, UpdateCommandRequest<UpdateOrganisationBody> request, OrganisationEntity organisation, IOrganisationRepository organisationRepository)
    {
        var isParentOrganisation = (await organisationRepository.GetAllChildOrganisationsByParentId(organisation.Id)).Count > 0;

        if (userAccessLevel != AccessLevel.Global || string.IsNullOrWhiteSpace(request.Model.AccessLevel))
            return (null, GetOrganisationAccessLevel(organisation, isParentOrganisation));

        if (!Enum.TryParse<AccessLevel>(request.Model.AccessLevel, true, out var organisationAccessLevel))
            return (CommandResponse.Failed(nameof(OrganisationBody.AccessLevel), $"Invalid Access Level `{request.Model.AccessLevel}`"), AccessLevel.Restricted);

        if (!string.IsNullOrWhiteSpace(request.Model.ParentOrganisationId) && organisationAccessLevel != AccessLevel.Restricted)
            return (CommandResponse.Failed(nameof(OrganisationBody.AccessLevel), $"Organisation needs to have an Access Level of `{nameof(AccessLevel.Restricted)}` to allow it to be a child organisation"), AccessLevel.Restricted);

        if (isParentOrganisation && organisationAccessLevel != AccessLevel.ParentChild)
            return (CommandResponse.Failed(nameof(OrganisationBody.AccessLevel), $"Organisation needs to have an Access Level of `{nameof(AccessLevel.ParentChild)}` as it is a parent of another organisation"), AccessLevel.ParentChild);

        return (null, organisationAccessLevel);
    }

    private static AccessLevel GetOrganisationAccessLevel(OrganisationEntity organisation, bool isParentOrganisation)
    {
        if (organisation.AccessLevel.HasValue)
            return organisation.AccessLevel.Value;

        // Note: All organisations SHOULD have an access level, but old data may not have it, so determine it if we can
        return isParentOrganisation
            ? AccessLevel.ParentChild
            : AccessLevel.Restricted;
    }

    private static async Task<CommandResponse> ValidateParentOrganisation(AccessLevel userAccessLevel, UpdateCommandRequest<UpdateOrganisationBody> request, OrganisationEntity entity, IOrganisationRepository organisationRepository)
    {
        if (string.Equals(request.Model.ParentOrganisationId, entity.ParentOrganisationId, StringComparison.OrdinalIgnoreCase))
            return null;

        if (userAccessLevel != AccessLevel.Global)
            return CommandResponse.Failed(nameof(OrganisationBody.ParentOrganisationId), "User does not have permission to add, update or remove parent organisation", HttpStatusCode.Forbidden);

        if (string.IsNullOrWhiteSpace(request.Model.ParentOrganisationId))
            return null;

        var parentOrganisation = await organisationRepository.GetItemAsync(request.Model.ParentOrganisationId);
        if (parentOrganisation == null)
            return CommandResponse.Failed(nameof(OrganisationBody.ParentOrganisationId), $"Organisation with Id `{request.Model.ParentOrganisationId}` does not exist", HttpStatusCode.NotFound);

        if (parentOrganisation.AccessLevel != AccessLevel.ParentChild)
            return CommandResponse.Failed(nameof(OrganisationBody.ParentOrganisationId), $"Organisation with Id `{request.Model.ParentOrganisationId}` is not a parent organisation");

        if (!string.IsNullOrWhiteSpace(parentOrganisation.ParentOrganisationId))
            return CommandResponse.Failed(nameof(OrganisationBody.ParentOrganisationId), $"Organisation with Id `{request.Model.ParentOrganisationId}` is a child of another organisation");

        return null;
    }

    private async Task AuditOrganisationUpdate(UpdateCommandRequest<UpdateOrganisationBody> request, OrganisationEntity entity, AccessLevel organisationAccessLevel, string stylesheetUrl)
    {
        var correlationId = correlationIdProvider.Provide();
        var clientDetails = clientDetailsProvider.Provide();

        var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Organisation, AuditEventDescriptions.OrganisationUpdated, correlationId, clientDetails, entity.CodeName);

        await auditService.LogAsync(AuditEventTypes.OrganisationUpdated, tenantAuditExtensions, entity,
            async () =>
            {
                Mapper.Map(request.Model, entity);

                entity.AccessLevel = organisationAccessLevel;

                if (!string.IsNullOrWhiteSpace(stylesheetUrl))
                {
                    entity.Theme.StylesheetUrl = stylesheetUrl;
                }

                await Repo.UpdateItemAsync(request.Id, entity);
            });
    }

    private async Task<string> UpdateOrganisationStylesheetUrl(UpdateCommandRequest<UpdateOrganisationBody> request, string codeName, CancellationToken cancellationToken)
    {
        var templateStylesheetContents = await organisationThemeAzureBlobStorageManager.GetTemplateStylesheetContentsAsync(cancellationToken);
        var updatedStylesheetContents = ReplaceOrganisationThemeColors(templateStylesheetContents, request.Model.Theme);

        var stylesheetBlobUri = await organisationThemeAzureBlobStorageManager.UploadStylesheetAsync(updatedStylesheetContents, codeName);
        return stylesheetBlobUri.AbsoluteUri;
    }

    private static string ReplaceOrganisationThemeColors(string template, SetupThemeBody theme)
    {
        var newTemplate = ReplaceFullWords(template, "{default_brandHighlightColor}", theme.DefaultColours.Highlights);
        newTemplate = ReplaceFullWords(newTemplate, "{default_brandButtonColor}", theme.DefaultColours.Buttons);
        newTemplate = ReplaceFullWords(newTemplate, "{default_brandButtonHoverColor}", theme.DefaultColours.Hover);
        newTemplate = ReplaceFullWords(newTemplate, "{dark_brandHighlightColor}", theme.DarkColours.Highlights);
        newTemplate = ReplaceFullWords(newTemplate, "{dark_brandButtonColor}", theme.DarkColours.Buttons);
        newTemplate = ReplaceFullWords(newTemplate, "{dark_brandButtonHoverColor}", theme.DarkColours.Hover);

        return newTemplate;
    }

    private static string ReplaceFullWords(string input, string from, string to)
    {
        return input == null ? null : Regex.Replace(input, Regex.Escape(from), to, RegexOptions.None, TimeSpan.FromMilliseconds(500));
    }
}