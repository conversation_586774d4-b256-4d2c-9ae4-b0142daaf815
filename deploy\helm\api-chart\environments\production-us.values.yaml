api:
  tag: latest

grpc:
  tag: latest

replicas: 3

ingress:
  tls:
    - tlsSecretName: tls-app-us-smartphlex-com
      hosts:
        - app-us.smartphlex.com
        - app.smartphlex.com
  hosts:
    - host: app-us.smartphlex.com
      paths:
        - path: /api/core/(.*)
          pathType: ImplementationSpecific
    - host: app.smartphlex.com
      paths:
        - path: /api/core/(.*)
          pathType: ImplementationSpecific

scaledObject:
  # -- Minimum number of replicas that the scaled object will create
  minReplicas: 3
  # -- Maximum number of replicas that the scaled object will create
  maxReplicas: 10
  # -- This is the polling interval to check each trigger on for the Scaled object type. By default its 30 seconds.
  pollingInterval: 30
  # -- The period to wait after the last trigger reported active before scaling the deployment back to 0. By default it’s 5 minutes (300 seconds).
  cooldownPeriod: 300
  # -- The CPU percentage value to scale up on
  utilisation: 70

newrelic_api_app_name: axon-core-prod-api
newrelic_grpc_app_name: axon-core-prod-grpc
keyVaultName: axn-prod-kv-use
clientId: 4b7a0e49-d4c4-48a0-b05c-5611c77e465b
azureIssuer: https://login.microsoftonline.com/common/v2.0
azureUseCustomRefresh: false


gigyaClientId: rjYASuMfKHsbId9RgH9Qurhf
gigyaIssuer: https://aaas.cencora.com/oidc/op/v1.0/4_PGj6CfqhMMUKzG9BHAEdwA/authorize
gigyaUseCustomRefresh: true


corsOriginUrl0: https://app-us.smartphlex.com
corsOriginUrl1: https://app.smartphlex.com

BlobStorageConnectionString: https://phcgvsharedstaticeun.blob.core.windows.net/

NamespaceName: "axn-prod-servicebus-use"
cosmosdbName: Axon-Core-ApiDb
cosmosdbUrl: 'https://axn-prod-cosmos-use.documents.azure.com:443/'

managedIdentityClientId: 04d08e84-1ece-417d-99c5-38a4af8241a0

azureWorkload:
  clientId: 2df9726b-862b-4681-a3c2-f406e1d7d4fe
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AzureBlobStorageContainersAppAvatarsFolderPrefix: produs/axon-avatar
AzureBlobStorageContainersOrganisationAvatarsFolderPrefix: produs/organisations
AzureBlobStorageContainersThemesFolderPrefix: produs/axon-theme


DataProtectionBlobStorageUri: 'https://axnprodstorageuse.blob.core.windows.net/'
DataProtectionKeyVaultKey: 'https://axn-prod-kv-use.vault.azure.net/keys/AxonDataProtection'

GoodDataBaseUri: 'https://phlexglobal-embedded.cloud.gooddata.com/'
GoodDataEnvironment: 'prod'