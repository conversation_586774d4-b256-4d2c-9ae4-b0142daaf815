﻿using System.Threading.Tasks;
using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Commands.AppUser.Create;
using Axon.Core.Api.Commands.AppUser.Delete;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Filters;
using Axon.Core.Api.Models.AppUser;
using Axon.Core.Api.Models.User;
using Axon.Core.Api.Queries.AppUser.IdQuery;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Shared.Api;
using FluentValidation;
using JetBrains.Annotations;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;
using GetFilteredAppUsersQueryRequest = Axon.Core.Api.Queries.AppUser.ListQuery.GetFilteredAppUsersQueryRequest;

namespace Axon.Core.Api.Controllers
{
    [ApiController]
    [Produces("application/json", "application/xml")]
    [ProducesResponseType(401, Type = typeof(CommandResponse))]
    [ProducesResponseType(403, Type = typeof(CommandResponse))]
    [ProducesResponseType(500, Type = typeof(CommandResponse))]
    [Route("v{version:apiVersion}/organisation/{orgCodeName}/app/{appCodeName}/user")]
    [Authorize]
    public class AppUserController : ApiControllerBase
    {
        private readonly IValidator<ListParams> listParamsValidator;
        private readonly IValidator<GetFilteredAppUsersQueryRequest> getFilteredAppUsersQueryRequestValidator;
        private readonly IValidator<GetFilteredEligibleAppUsersQueryRequest> getFilteredEligibleAppUsersQueryRequestValidator;
        private readonly IValidator<DeleteAppUserCommand> deleteAppUserCommandValidator;
        private readonly IValidator<CreateAppUserCommand> createAppUserCommandValidator;
        private readonly IValidator<UpdateAppUserCommand> updateAppUserCommandValidator;

        public AppUserController(IMediator mediator,
                                 IValidator<ListParams> listParamsValidator,
                                 IValidator<GetFilteredAppUsersQueryRequest> getFilteredAppUsersQueryRequestValidator,
                                 IValidator<DeleteAppUserCommand> deleteAppUserCommandValidator,
                                 IValidator<GetFilteredEligibleAppUsersQueryRequest> getFilteredEligibleAppUsersQueryRequestValidator,
                                 IValidator<CreateAppUserCommand> createAppUserCommandValidator,
                                 IValidator<UpdateAppUserCommand> updateAppUserCommandValidator) : base(mediator)
        {
            this.listParamsValidator = listParamsValidator;
            this.getFilteredAppUsersQueryRequestValidator = getFilteredAppUsersQueryRequestValidator;
            this.deleteAppUserCommandValidator = deleteAppUserCommandValidator;
            this.getFilteredEligibleAppUsersQueryRequestValidator = getFilteredEligibleAppUsersQueryRequestValidator;
            this.createAppUserCommandValidator = createAppUserCommandValidator;
            this.updateAppUserCommandValidator = updateAppUserCommandValidator;
        }

        [HttpGet(Name = "GetFilteredAppUsers")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<ApiPagedListResult<AppUserModel>>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [ListParamsActionFilter]
        public async Task<IActionResult> GetFilteredAppUsersAsync(string orgCodeName,
                                                                  string appCodeName,
                                                                  [FromQuery][CanBeNull] string filter = "",
                                                                  [FromQuery][CanBeNull] string orderBy = "",
                                                                  [FromQuery] int? offset = 0,
                                                                  [FromQuery] int? limit = 20)
        {
            var request = new GetFilteredAppUsersQueryRequest(orgCodeName, appCodeName, ListParams);
            var validationResult = await listParamsValidator.ValidateAsync(ListParams);
            var requestValidationResult = await getFilteredAppUsersQueryRequestValidator.ValidateAsync(request);
            if (!validationResult.IsValid || !requestValidationResult.IsValid)
            {
                var validationResultErrors = validationResult.ToErrorDictionary();
                var requestValidationResultErrors = requestValidationResult.ToErrorDictionary();
                var errors = validationResultErrors.Merge(requestValidationResultErrors);
                return BadRequest(errors);
            }

            return await Send(request);
        }

        [HttpGet("eligible", Name = "GetFilteredEligibleUsers")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<ApiPagedListResult<UserModel>>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [ListParamsActionFilter]
        public async Task<IActionResult> GetFilteredEligibleUsers(string orgCodeName, 
                                                                  string appCodeName, 
                                                                 [FromQuery][CanBeNull] string filter = "",
                                                                 [FromQuery][CanBeNull] string orderBy = "",
                                                                 [FromQuery] int? offset = 0,
                                                                 [FromQuery] int? limit = 20)
        {
            var request = new GetFilteredEligibleAppUsersQueryRequest(orgCodeName, appCodeName, ListParams);
            var validationResult = await listParamsValidator.ValidateAsync(ListParams);
            var requestValidationResult = await getFilteredEligibleAppUsersQueryRequestValidator.ValidateAsync(request);
            if (!validationResult.IsValid || !requestValidationResult.IsValid)
            {
                var validationResultErrors = validationResult.ToErrorDictionary();
                var requestValidationResultErrors = requestValidationResult.ToErrorDictionary();
                var errors = validationResultErrors.Merge(requestValidationResultErrors);
                return BadRequest(errors);
            }

            return await Send(request);
        }

        [HttpGet("{emailAddress}", Name = "GetAppUser")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<SpecificAppUserModel>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        public async Task<IActionResult> GetAppUserAsync([FromRoute] string orgCodeName, [FromRoute] string appCodeName, [FromRoute] string emailAddress)
        {
            var request = new GetAppUserByIdQueryRequest(emailAddress, appCodeName, orgCodeName);
            return await Send(request);
        }

        [HttpPut("{emailAddress}", Name = "UpdateAppUser")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
        public async Task<IActionResult> UpdateAppUserAsync([FromRoute] string appCodeName, [FromRoute] string orgCodeName, [FromRoute] string emailAddress, [FromBody] UpdateAppUserModel request)
        {
            var command = new UpdateAppUserCommand(emailAddress, appCodeName, orgCodeName, request.RoleId, request.Scopes);

            var validationResult = await updateAppUserCommandValidator.ValidateAsync(command);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(command);
        }

        [HttpDelete("{emailAddress}", Name = "DeleteAppUser")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
        public async Task<IActionResult> DeleteAppUserAsync([FromRoute] string appCodeName, [FromRoute] string orgCodeName, [FromRoute] string emailAddress)
        {
            var command = new DeleteAppUserCommand(emailAddress, appCodeName, orgCodeName);
            var validationResult = await deleteAppUserCommandValidator.ValidateAsync(command);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(command);
        }


        [HttpPost("{emailAddress}", Name = "CreateAppUser")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(201, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
        public async Task<IActionResult> CreateAppUserAsync([FromRoute] string appCodeName, [FromRoute] string orgCodeName, [FromRoute] string emailAddress, [FromBody] CreateAppUserModel request)
        {
            var command = new CreateAppUserCommand(emailAddress, appCodeName, orgCodeName, request.RoleId, request.Scopes);

            var validationResult = await createAppUserCommandValidator.ValidateAsync(command);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(command);
        }
    }
}
