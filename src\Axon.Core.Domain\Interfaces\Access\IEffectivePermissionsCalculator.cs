﻿using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Models.Access;
using Axon.Core.Shared.Api;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Domain.Interfaces.Access
{
    /// <summary>
    /// Given the <see cref="AccessEntity"/> items present, will calculate the users effective permissions at different scopes.
    /// This is internal and exists to break up logic intended for use only in the AccessService which is how the system at large will retrieve the users effective permissions.
    /// Because of this it will assume the <see cref="AccessEntity"/> records provided are valid for the scope requested.
    /// The only validation performed is a definition must exist for all applications referenced by the <see cref="AccessEntity"/> items.
    /// </summary>
    internal interface IEffectivePermissionsCalculator
    {
        /// <summary>
        /// Calculates the effective permissions against the provided application, for all organisations
        /// represented by the provided <see cref="AccessEntity"/> items
        /// </summary>
        /// <param name="appCodeName">The application the permissions are being calculated for</param>
        /// <param name="userAccessItems">The <see cref="AccessEntity"/> records relevant for this application and organisation</param>
        /// <returns>A set of <see cref="UserEffectivePermissionsForAppAndOrg"/> which represent the effective permissions for this app, seperated by organisation</returns>
        Task<IReadOnlyCollection<UserEffectivePermissionsForAppAndOrg>> Calculate(string appCodeName, IEnumerable<UserRoleAccess> userRoleAccess, OrganisationEntity ownerOrganisation);

        /// <summary>
        /// Calculates the effective permissions represented by the provided <see cref="AccessEntity"/> items.
        /// </summary>
        /// <param name="userAccessItems">The <see cref="AccessEntity"/> records relevant for this application and organisation</param>
        /// <returns>A set of <see cref="UserEffectivePermissionsForAppAndOrg"/> which represent the effective permissions for this app, seperated by organisation and app</returns>
        Task<IReadOnlyCollection<UserEffectivePermissionsForAppAndOrg>> Calculate(IEnumerable<UserRoleAccess> userRoleAccess, OrganisationEntity ownerOrganisation);

        /// <summary>
        /// Returns a set of organistaions the user has access to, filtered by the provided <see cref="ListParams"/>
        /// Alongside the calculated effective permissions the user has against that organistion/app combination.
        /// If a user has cross organistion admin privledges this will be applied here and will affect the organistions returned.
        /// If a user is present on an organistion, it will be included in the result set even if no permissions are set to allowed.
        /// </summary>
        /// <param name="appCodeName">The code name for the app trying to be accessed</param>
        /// <param name="userOrganisationAccess">The access documents for the user for the target and the owner organisation</param>
        /// <param name="userRoleAccesses">The roles the user has access to for the target and owner organisation</param>
        /// <param name="ownerOrganisation">The users owning organistion</param>
        /// <param name="listParams">The filters to apply to the organistion list</param>
        /// <returns></returns>
        Task<IReadOnlyCollection<(OrganisationEntity Organisation, UserEffectivePermissionsForAppAndOrg EffectivePermissions)>> CalculateIncludingAdminEligibility(string appCodeName,
                                                                                                                                                                   IEnumerable<UserOrganisationAccess> userOrganisationAccess,
                                                                                                                                                                   IEnumerable<UserRoleAccess> userRoleAccesses,
                                                                                                                                                                   OrganisationEntity ownerOrganisation,
                                                                                                                                                                   ListParams listParams);

        /// <summary>
        /// Calculates effective permissions the user has against that organistion/app combination.
        /// If a user has cross organistion admin privledges this will be applied here and will affect the organistions returned.
        /// Effective permissions will be returned even if a user has no permissions. This will have all permissions on the definition set to false.
        /// </summary>
        /// <param name="appCodeName">The </param>
        /// <param name="targetOrgCodeName">The organisation </param>
        /// <param name="userOrganisationAccess">The access documents for the user for the target and the owner organisation</param>
        /// <param name="userRoleAccesses">The roles the user has access to for the target and owner organisation</param>
        /// <param name="ownerOrganisation">The users owning organisation</param>
        /// <returns></returns>
        Task<UserEffectivePermissionsForAppAndOrg> CalculateIncludingAdminEligibility(string appCodeName,
                                                                                      string targetOrgCodeName,
                                                                                      IEnumerable<UserOrganisationAccess> userOrganisationAccess,
                                                                                      IEnumerable<UserRoleAccess> userRoleAccesses,
                                                                                      OrganisationEntity ownerOrganisation);
    }
}
