﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Commands.Organisation.OrganisationUser;
using Axon.Core.Api.Extensions;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Microsoft.Extensions.Logging;

namespace Axon.Core.Api.Validators.ManageOrganisationUser;

public interface IManageOrganisationUserValidator
{
    Task<CommandResponse> ValidateUser(string email, string organisationCodeName);
    Task<(CommandResponse, GroupEntity[])> ValidateChildOrganisations(GroupOrganisation[] groupOrganisations, string parentOrganisationCodeName);
    Task<(CommandResponse, GroupEntity[])> ValidateGroups(string[] groups, string organisationCodeName);
    Task<CommandResponse> ValidateOrganisation(string organisationCodeName, string parentOrganisationCodeName = null);
}

public class ManageOrganisationUserValidator : IManageOrganisationUserValidator
{
    private readonly IGroupRepository groupRepository;
    private readonly IOrganisationRepository organisationRepository;
    private readonly IUserRepository userRepository;
    private readonly IAccessRepository accessRepository;
    private readonly ILogger<ManageOrganisationUserValidator> logger;

    public ManageOrganisationUserValidator(ILogger<ManageOrganisationUserValidator> logger, IGroupRepository groupRepository, IOrganisationRepository organisationRepository, IUserRepository userRepository, IAccessRepository accessRepository)
    {
        this.logger = logger;
        this.groupRepository = groupRepository;
        this.organisationRepository = organisationRepository;
        this.userRepository = userRepository;
        this.accessRepository = accessRepository;
    }

    public async Task<CommandResponse> ValidateUser(string email, string organisationCodeName)
    {
        var user = await userRepository.GetUserByEmailAsync(email);
        var userAccessEntities = user != null ? await accessRepository.GetAccessItemsForUserAsync(user.Id, AccessType.OrganisationAccess) : Enumerable.Empty<AccessEntity>();

        var existingAccessEntity = userAccessEntities.FirstOrDefault(x => x.OrganisationCodeName == organisationCodeName);
        if (existingAccessEntity != null)
        {
            logger.LogWarning("User {Email} is already in organisation {OrganisationCodeName}", email, organisationCodeName);
            return CommandResponse.Conflict(nameof(AccessEntity), existingAccessEntity.Id, existingAccessEntity.OrganisationCodeName);
        }

        return null;
    }

    public async Task<(CommandResponse, GroupEntity[])> ValidateChildOrganisations(GroupOrganisation[] groupOrganisations, string parentOrganisationCodeName)
    {
        if (groupOrganisations == null || groupOrganisations.Length == 0)
            return (null, []);

        var groupEntities = new List<GroupEntity>();
        foreach (var childOrganisation in groupOrganisations)
        {
            var childOrganisationResponse = await ValidateOrganisation(childOrganisation.OrganisationCodeName, parentOrganisationCodeName);
            if (childOrganisationResponse != null)
                return (childOrganisationResponse, []);

            var (groupResponse, organisationGroupEntities) = await ValidateGroups(childOrganisation.Groups, childOrganisation.OrganisationCodeName);
            if (groupResponse != null) 
                return (groupResponse, []);

            groupEntities.AddRange(organisationGroupEntities);
        }

        return (null, groupEntities.ToArray());
    }

    public async Task<(CommandResponse, GroupEntity[])> ValidateGroups(string[] groups, string organisationCodeName)
    {
        if (groups == null || groups.Length == 0)
            return (null, []);

        var groupEntities = new List<GroupEntity>();
        foreach (var groupId in groups)
        {
            if (string.IsNullOrEmpty(groupId))
            {
                logger.LogWarning("Group is empty.");
                return (CommandResponse.BadRequest(nameof(GroupEntity), "Group is empty."), []);
            }
            if (!await groupRepository.TryGetItemAsync(groupId, out var groupEntity))
            {
                logger.LogWarning("Group {groupId} does not exist.", groupId);
                return (CommandResponse.NotFound(nameof(GroupEntity), groupId), []);
            }
            if (!groupEntity.OrganisationCodeName.Equals(organisationCodeName, StringComparison.OrdinalIgnoreCase))
            {
                logger.LogWarning("Group {groupId} is not in {organisationCodeName} organisation.", groupId, organisationCodeName);
                return (CommandResponse.BadRequest(nameof(GroupEntity), $"Group {groupId} is not in {organisationCodeName} organisation."), []);
            }
            groupEntities.Add(groupEntity);
        }

        return (null, groupEntities.ToArray());
    }

    public async Task<CommandResponse> ValidateOrganisation(string organisationCodeName, string parentOrganisationCodeName = null)
    {
        var organisationEntity = await organisationRepository.GetItemByCodeNameAsync(organisationCodeName);
        if (organisationEntity == null)
        {
            logger.LogWarning("Organisation {organisationCodeName} does not exist.", organisationCodeName);
            return CommandResponse.NotFound(nameof(OrganisationEntity), organisationCodeName);
        }

        if (parentOrganisationCodeName is not null)
        {
            var parentOrganisationEntity = await organisationRepository.GetItemByCodeNameAsync(parentOrganisationCodeName);
            if (organisationEntity.ParentOrganisationId is null || !organisationEntity.ParentOrganisationId.Equals(parentOrganisationEntity.Id, StringComparison.OrdinalIgnoreCase))
            {
                logger.LogWarning("Organisation {organisationCodeName} is not a child of organisation {parentOrganisationCodeName}.", organisationCodeName, parentOrganisationCodeName);
                return CommandResponse.BadRequest(nameof(OrganisationEntity), $"Organisation {organisationCodeName} is not a child of organisation {parentOrganisationCodeName}");
            }
        }

        return null;
    }
}