﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Services.UserOrganisation;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser.DeleteOrganisationUser;

internal class DeleteOrganisationUserCommandHandler : BaseCommandHandler<AccessEntity, DeleteOrganisationUserCommandRequest, CommandResponse>
{
    private readonly IAccessRepository accessRepository;
    private readonly IClientDetailsProvider clientDetailsProvider;
    private readonly ICorrelationIdProvider correlationIdProvider;
    private readonly IOrganisationRepository organisationRepository;
    private readonly IUserRepository userRepository;
    private readonly IOrganisationUserManager organisationUserManager;

    public DeleteOrganisationUserCommandHandler(
        IAccessRepository accessRepository,
        IMapper mapper,
        IMessageBus messageBus,
        IClientDetailsProvider clientDetailsProvider,
        ICorrelationIdProvider correlationIdProvider,
        IOrganisationRepository organisationRepository,
        IUserRepository userRepository,
        IOrganisationUserManager organisationUserManager) : base(accessRepository, mapper, messageBus)
    {
        this.accessRepository = accessRepository;
        this.clientDetailsProvider = clientDetailsProvider;
        this.correlationIdProvider = correlationIdProvider;
        this.organisationRepository = organisationRepository;
        this.userRepository = userRepository;
        this.organisationUserManager = organisationUserManager;
    }

    public override async Task<CommandResponse> Handle(DeleteOrganisationUserCommandRequest request, CancellationToken cancellationToken)
    {
        var correlationId = correlationIdProvider.Provide();
        var clientDetails = clientDetailsProvider.Provide();

        var organisationEntity = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);
        if (organisationEntity == null)
        {
            return CommandResponse.NotFound(nameof(request.OrganisationCodeName), request.OrganisationCodeName);
        }

        var userEntity = await userRepository.GetItemAsync(request.UserId);
        if (userEntity == null)
        {
            return CommandResponse.NotFound(nameof(request.UserId), request.UserId);
        }

        var organisationAccessEntities = (await accessRepository.GetOrganisationsForUserAsync(request.UserId))
            .Where(x => x.OrganisationCodeName.Equals(request.OrganisationCodeName, StringComparison.InvariantCultureIgnoreCase))
            .ToArray();

        foreach (var organisationAccessEntity in organisationAccessEntities)
        {
            await organisationUserManager.UnlinkUserFromOrganisation(correlationId, clientDetails, organisationAccessEntity.Id, userEntity.Id, userEntity.Email);
            OrgUserUpdatedEvent message = new OrgUserUpdatedEvent()
            {
                OrgId = organisationEntity.Id, 
                OrgCodeName = organisationEntity.CodeName, 
                UserId = userEntity.Id, 
                UserEmail = userEntity.Email, 
                Action = EventActionType.Deleted
            };
            MessageBus.PublishAsync(message, cancellationToken: cancellationToken);
        }

        //changed as NoContent caused errors locally
        return CommandResponse.Success();
    }
}
