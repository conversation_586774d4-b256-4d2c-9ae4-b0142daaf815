﻿using Axon.Core.Contracts;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Axon.Core.Api.Services.AppGroup;

internal class AppGroupManager : IAppGroupManager
{
    private readonly IAccessRepository accessRepository;
    private readonly IAppGroupRepository appGroupRepository;
    private readonly IAuditService<TenantAuditExtensions> auditService;
    private readonly IMessageBus messageBus;
    private readonly ILogger<AppGroupManager> logger;

    public AppGroupManager(IAccessRepository accessRepository, IAppGroupRepository appGroupRepository, IAuditService<TenantAuditExtensions> auditService, IMessageBus messageBus, ILogger<AppGroupManager> logger)
    {
        this.accessRepository = accessRepository;
        this.appGroupRepository = appGroupRepository;
        this.auditService = auditService;
        this.messageBus = messageBus;
        this.logger = logger;
    }

    public async Task RemoveAppGroupAndAccessEntities(AppGroupEntity appGroupEntity, Guid correlationId, ClientDetails clientDetails)
    {
        await DeleteGroupAccessEntities(appGroupEntity, correlationId, clientDetails);
        await DeleteAppGroup(appGroupEntity, correlationId, clientDetails);
    }

    private async Task DeleteAppGroup(AppGroupEntity existingEntity, Guid correlationId, ClientDetails clientDetails)
    {
        var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.AppGroup, AuditEventDescriptions.AppGroupDeleted, correlationId, clientDetails, existingEntity.OrganisationCodeName);

        await auditService.LogAsync(AuditEventTypes.AppGroupDeleted, tenantAuditExtensions, existingEntity,
            async () =>
            {
                await appGroupRepository.DeleteItemAsync(existingEntity.Id);
            });
    }

    private async Task DeleteGroupAccessEntities(AppGroupEntity existingEntity, Guid correlationId, ClientDetails clientDetails)
    {
        var groupAccessEntities = await accessRepository.GetAccessItemsForGroupAsync(existingEntity.OrganisationCodeName, existingEntity.AppCodeName, existingEntity.GroupId);
        if (!groupAccessEntities.Any())
        {
            return;
        }
        foreach (var access in groupAccessEntities)
        {
            var appUserEvent = new AppUserUpdatedEvent()
            {
                Action = EventActionType.Deleted,
                AppCodeName = access.AppCodeName,
                AppId = access.AppId,
                OrgCodeName = access.OrganisationCodeName,
                OrgId = access.OrganisationId,
                UserId = access.User.Id
            };
            await messageBus.PublishAsync(appUserEvent);
            logger.LogInformation("""
                    AppUserUpdatedEvent sent - 
                    OrgCodeName:{orgCodeName}, 
                    AppCodeName:{appCodeName},
                    UserId:{userId},
                    EventAction:{action},
                    OrgId:{orgId}
                    AppId:{appId}
                    """, [appUserEvent.OrgCodeName, appUserEvent.AppCodeName, appUserEvent.UserId, appUserEvent.Action, appUserEvent.OrgId, appUserEvent.AppId]
                    );
        }
        await Task.WhenAll(groupAccessEntities.Select(e => DeleteGroupAccessEntity(e, correlationId, clientDetails)));
    }

    private async Task DeleteGroupAccessEntity(AccessEntity accessEntity, Guid correlationId, ClientDetails clientDetails)
    {
        var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Access, AuditEventDescriptions.AccessDeleted, correlationId, clientDetails, accessEntity.OrganisationCodeName);

        await auditService.LogAsync(AuditEventTypes.AccessDeleted, tenantAuditExtensions, accessEntity,
            async () =>
            {
                await accessRepository.DeleteItemAsync(accessEntity.Id);
            });
    }
}
