/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Axon.Core.Api.Sdk.NetCore.Client;
using Axon.Core.Api.Sdk.NetCore.Model;

namespace Axon.Core.Api.Sdk.NetCore.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IRolePermissionApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="permission"></param>
        /// <param name="role"></param>
        /// <returns>CommandResponse</returns>
        CommandResponse DeleteRoleGeneralPermission(string orgCodeName, string appCodeName, string permission, string role);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="permission"></param>
        /// <param name="role"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> DeleteRoleGeneralPermissionWithHttpInfo(string orgCodeName, string appCodeName, string permission, string role);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <returns>AppOrganisationPermissionIEnumerableCommandResponse</returns>
        AppOrganisationPermissionIEnumerableCommandResponse GetAppOrgRolePermissions(string orgCodeName, string appCodeName, string role);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <returns>ApiResponse of AppOrganisationPermissionIEnumerableCommandResponse</returns>
        ApiResponse<AppOrganisationPermissionIEnumerableCommandResponse> GetAppOrgRolePermissionsWithHttpInfo(string orgCodeName, string appCodeName, string role);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse UpdateAppOrgRolePermissions(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionBody? appOrganisationPermissionBody = default(AppOrganisationPermissionBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> UpdateAppOrgRolePermissionsWithHttpInfo(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionBody? appOrganisationPermissionBody = default(AppOrganisationPermissionBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionListBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse UpdateAppOrgRolePermissionsList(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionListBody? appOrganisationPermissionListBody = default(AppOrganisationPermissionListBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionListBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> UpdateAppOrgRolePermissionsListWithHttpInfo(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionListBody? appOrganisationPermissionListBody = default(AppOrganisationPermissionListBody?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IRolePermissionApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="permission"></param>
        /// <param name="role"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> DeleteRoleGeneralPermissionAsync(string orgCodeName, string appCodeName, string permission, string role, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="permission"></param>
        /// <param name="role"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> DeleteRoleGeneralPermissionWithHttpInfoAsync(string orgCodeName, string appCodeName, string permission, string role, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppOrganisationPermissionIEnumerableCommandResponse</returns>
        System.Threading.Tasks.Task<AppOrganisationPermissionIEnumerableCommandResponse> GetAppOrgRolePermissionsAsync(string orgCodeName, string appCodeName, string role, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppOrganisationPermissionIEnumerableCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<AppOrganisationPermissionIEnumerableCommandResponse>> GetAppOrgRolePermissionsWithHttpInfoAsync(string orgCodeName, string appCodeName, string role, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> UpdateAppOrgRolePermissionsAsync(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionBody? appOrganisationPermissionBody = default(AppOrganisationPermissionBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> UpdateAppOrgRolePermissionsWithHttpInfoAsync(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionBody? appOrganisationPermissionBody = default(AppOrganisationPermissionBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionListBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> UpdateAppOrgRolePermissionsListAsync(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionListBody? appOrganisationPermissionListBody = default(AppOrganisationPermissionListBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionListBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> UpdateAppOrgRolePermissionsListWithHttpInfoAsync(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionListBody? appOrganisationPermissionListBody = default(AppOrganisationPermissionListBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IRolePermissionApi : IRolePermissionApiSync, IRolePermissionApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class RolePermissionApi : IDisposable, IRolePermissionApi
    {
        private Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="RolePermissionApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public RolePermissionApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="RolePermissionApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public RolePermissionApi(string basePath)
        {
            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="RolePermissionApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public RolePermissionApi(Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="RolePermissionApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public RolePermissionApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="RolePermissionApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public RolePermissionApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="RolePermissionApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public RolePermissionApi(HttpClient client, Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="RolePermissionApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public RolePermissionApi(Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient client, Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient asyncClient, Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="permission"></param>
        /// <param name="role"></param>
        /// <returns>CommandResponse</returns>
        public CommandResponse DeleteRoleGeneralPermission(string orgCodeName, string appCodeName, string permission, string role)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = DeleteRoleGeneralPermissionWithHttpInfo(orgCodeName, appCodeName, permission, role);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="permission"></param>
        /// <param name="role"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> DeleteRoleGeneralPermissionWithHttpInfo(string orgCodeName, string appCodeName, string permission, string role)
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling RolePermissionApi->DeleteRoleGeneralPermission");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling RolePermissionApi->DeleteRoleGeneralPermission");

            // verify the required parameter 'permission' is set
            if (permission == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'permission' when calling RolePermissionApi->DeleteRoleGeneralPermission");

            // verify the required parameter 'role' is set
            if (role == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'role' when calling RolePermissionApi->DeleteRoleGeneralPermission");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("permission", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(permission)); // path parameter
            localVarRequestOptions.PathParameters.Add("role", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(role)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<CommandResponse>("/v1/Organisation/{orgCodeName}/app/{appCodeName}/role-permission/{role}/permission/{permission}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteRoleGeneralPermission", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="permission"></param>
        /// <param name="role"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> DeleteRoleGeneralPermissionAsync(string orgCodeName, string appCodeName, string permission, string role, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await DeleteRoleGeneralPermissionWithHttpInfoAsync(orgCodeName, appCodeName, permission, role, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="permission"></param>
        /// <param name="role"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> DeleteRoleGeneralPermissionWithHttpInfoAsync(string orgCodeName, string appCodeName, string permission, string role, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling RolePermissionApi->DeleteRoleGeneralPermission");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling RolePermissionApi->DeleteRoleGeneralPermission");

            // verify the required parameter 'permission' is set
            if (permission == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'permission' when calling RolePermissionApi->DeleteRoleGeneralPermission");

            // verify the required parameter 'role' is set
            if (role == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'role' when calling RolePermissionApi->DeleteRoleGeneralPermission");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("permission", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(permission)); // path parameter
            localVarRequestOptions.PathParameters.Add("role", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(role)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<CommandResponse>("/v1/Organisation/{orgCodeName}/app/{appCodeName}/role-permission/{role}/permission/{permission}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteRoleGeneralPermission", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <returns>AppOrganisationPermissionIEnumerableCommandResponse</returns>
        public AppOrganisationPermissionIEnumerableCommandResponse GetAppOrgRolePermissions(string orgCodeName, string appCodeName, string role)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppOrganisationPermissionIEnumerableCommandResponse> localVarResponse = GetAppOrgRolePermissionsWithHttpInfo(orgCodeName, appCodeName, role);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <returns>ApiResponse of AppOrganisationPermissionIEnumerableCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppOrganisationPermissionIEnumerableCommandResponse> GetAppOrgRolePermissionsWithHttpInfo(string orgCodeName, string appCodeName, string role)
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling RolePermissionApi->GetAppOrgRolePermissions");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling RolePermissionApi->GetAppOrgRolePermissions");

            // verify the required parameter 'role' is set
            if (role == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'role' when calling RolePermissionApi->GetAppOrgRolePermissions");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("role", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(role)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<AppOrganisationPermissionIEnumerableCommandResponse>("/v1/Organisation/{orgCodeName}/app/{appCodeName}/role-permission/{role}/permission", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppOrgRolePermissions", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppOrganisationPermissionIEnumerableCommandResponse</returns>
        public async System.Threading.Tasks.Task<AppOrganisationPermissionIEnumerableCommandResponse> GetAppOrgRolePermissionsAsync(string orgCodeName, string appCodeName, string role, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppOrganisationPermissionIEnumerableCommandResponse> localVarResponse = await GetAppOrgRolePermissionsWithHttpInfoAsync(orgCodeName, appCodeName, role, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppOrganisationPermissionIEnumerableCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppOrganisationPermissionIEnumerableCommandResponse>> GetAppOrgRolePermissionsWithHttpInfoAsync(string orgCodeName, string appCodeName, string role, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling RolePermissionApi->GetAppOrgRolePermissions");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling RolePermissionApi->GetAppOrgRolePermissions");

            // verify the required parameter 'role' is set
            if (role == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'role' when calling RolePermissionApi->GetAppOrgRolePermissions");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("role", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(role)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<AppOrganisationPermissionIEnumerableCommandResponse>("/v1/Organisation/{orgCodeName}/app/{appCodeName}/role-permission/{role}/permission", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppOrgRolePermissions", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse UpdateAppOrgRolePermissions(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionBody? appOrganisationPermissionBody = default(AppOrganisationPermissionBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = UpdateAppOrgRolePermissionsWithHttpInfo(orgCodeName, appCodeName, role, appOrganisationPermissionBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> UpdateAppOrgRolePermissionsWithHttpInfo(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionBody? appOrganisationPermissionBody = default(AppOrganisationPermissionBody?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling RolePermissionApi->UpdateAppOrgRolePermissions");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling RolePermissionApi->UpdateAppOrgRolePermissions");

            // verify the required parameter 'role' is set
            if (role == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'role' when calling RolePermissionApi->UpdateAppOrgRolePermissions");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("role", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(role)); // path parameter
            localVarRequestOptions.Data = appOrganisationPermissionBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<CommandResponse>("/v1/Organisation/{orgCodeName}/app/{appCodeName}/role-permission/{role}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppOrgRolePermissions", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> UpdateAppOrgRolePermissionsAsync(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionBody? appOrganisationPermissionBody = default(AppOrganisationPermissionBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await UpdateAppOrgRolePermissionsWithHttpInfoAsync(orgCodeName, appCodeName, role, appOrganisationPermissionBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> UpdateAppOrgRolePermissionsWithHttpInfoAsync(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionBody? appOrganisationPermissionBody = default(AppOrganisationPermissionBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling RolePermissionApi->UpdateAppOrgRolePermissions");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling RolePermissionApi->UpdateAppOrgRolePermissions");

            // verify the required parameter 'role' is set
            if (role == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'role' when calling RolePermissionApi->UpdateAppOrgRolePermissions");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("role", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(role)); // path parameter
            localVarRequestOptions.Data = appOrganisationPermissionBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<CommandResponse>("/v1/Organisation/{orgCodeName}/app/{appCodeName}/role-permission/{role}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppOrgRolePermissions", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionListBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse UpdateAppOrgRolePermissionsList(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionListBody? appOrganisationPermissionListBody = default(AppOrganisationPermissionListBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = UpdateAppOrgRolePermissionsListWithHttpInfo(orgCodeName, appCodeName, role, appOrganisationPermissionListBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionListBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> UpdateAppOrgRolePermissionsListWithHttpInfo(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionListBody? appOrganisationPermissionListBody = default(AppOrganisationPermissionListBody?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling RolePermissionApi->UpdateAppOrgRolePermissionsList");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling RolePermissionApi->UpdateAppOrgRolePermissionsList");

            // verify the required parameter 'role' is set
            if (role == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'role' when calling RolePermissionApi->UpdateAppOrgRolePermissionsList");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("role", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(role)); // path parameter
            localVarRequestOptions.Data = appOrganisationPermissionListBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<CommandResponse>("/v1/Organisation/{orgCodeName}/app/{appCodeName}/role-permission/{role}/permission", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppOrgRolePermissionsList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionListBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> UpdateAppOrgRolePermissionsListAsync(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionListBody? appOrganisationPermissionListBody = default(AppOrganisationPermissionListBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await UpdateAppOrgRolePermissionsListWithHttpInfoAsync(orgCodeName, appCodeName, role, appOrganisationPermissionListBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="role"></param>
        /// <param name="appOrganisationPermissionListBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> UpdateAppOrgRolePermissionsListWithHttpInfoAsync(string orgCodeName, string appCodeName, string role, AppOrganisationPermissionListBody? appOrganisationPermissionListBody = default(AppOrganisationPermissionListBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling RolePermissionApi->UpdateAppOrgRolePermissionsList");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling RolePermissionApi->UpdateAppOrgRolePermissionsList");

            // verify the required parameter 'role' is set
            if (role == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'role' when calling RolePermissionApi->UpdateAppOrgRolePermissionsList");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("role", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(role)); // path parameter
            localVarRequestOptions.Data = appOrganisationPermissionListBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<CommandResponse>("/v1/Organisation/{orgCodeName}/app/{appCodeName}/role-permission/{role}/permission", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppOrgRolePermissionsList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
