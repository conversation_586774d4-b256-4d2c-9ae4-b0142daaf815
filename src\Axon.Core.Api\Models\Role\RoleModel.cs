﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.Role
{
    public class RoleModel
    {
        [Required]
        public string Id { get; set; }
        [Required]
        public string AppCodeName { get; set; }
        [Required]
        public string OrganisationCodeName { get; set; }
        [Required]
        public string RoleType { get; set; }
        [Required]
        public string RoleName { get; set; }
        [Required]
        public bool IsEnabled { get; set; }
        public UserGroupScope[] UserGroupScopes { get; set; }
        public List<Permission> Permissions { get; set; }

        public class Permission
        {
            public string Name { get; set; }
            public ScopeResources[] Scopes { get; set; }
            public bool Allow { get; set; }
            public bool IsInherited { get; set; }
        }

        public class ScopeResources
        {
            public string Scope { get; set; }
            public Resource[] Resources { get; set; }
        }

        public class Resource
        {
            public string Id { get; set; }
            public string Name { get; set; }
        }

        public class UserGroupScope
        {
            public string Scope { get; set; }
        }
    }
}
