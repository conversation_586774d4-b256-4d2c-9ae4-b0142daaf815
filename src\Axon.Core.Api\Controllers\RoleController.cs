﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Filters;
using Axon.Core.Api.Models.Role;
using Axon.Core.Api.Queries.Role.GetById;
using Axon.Core.Api.Queries.Role.ListQuery;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Shared.Api;
using FluentValidation;
using JetBrains.Annotations;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("v{version:apiVersion}/organisation/{orgCodeName}/app/{appCodeName}/role")]
[ProducesResponseType(401, Type = typeof(CommandResponse))]
[ProducesResponseType(403, Type = typeof(CommandResponse))]
[ProducesResponseType(500, Type = typeof(CommandResponse))]
[Authorize]
public class RoleController : ApiControllerBase
{
    private readonly IValidator<ListParams> listParamsValidator;
    private readonly IValidator<GetFilteredRoleListQueryRequest> getFilteredRoleListQueryValidator;
    private readonly IValidator<GetAllRoleListQueryRequest> getAllRoleListQueryValidator;
    private readonly IValidator<GetRoleByIdQueryRequest> getRoleByIdQueryValidator;
    private readonly IValidator<DeleteCommandRequest<RoleModel>> deleteCommandRequestRoleModelValidator;

    public RoleController(IMediator mediator, IValidator<ListParams> listParamsValidator, IValidator<GetFilteredRoleListQueryRequest> getFilteredRoleListQueryValidator, IValidator<GetAllRoleListQueryRequest> getAllRoleListQueryValidator, IValidator<GetRoleByIdQueryRequest> getRoleByIdQueryValidator, IValidator<DeleteCommandRequest<RoleModel>> deleteCommandRequestRoleModelValidator) : base(mediator)
    {
        this.listParamsValidator = listParamsValidator;
        this.getFilteredRoleListQueryValidator = getFilteredRoleListQueryValidator;
        this.getAllRoleListQueryValidator = getAllRoleListQueryValidator;
        this.getRoleByIdQueryValidator = getRoleByIdQueryValidator;
        this.deleteCommandRequestRoleModelValidator = deleteCommandRequestRoleModelValidator;
    }

    [HttpGet(Name = "GetFiltered")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<ApiPagedListResult<RoleModel>>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [ListParamsActionFilter]
    public async Task<IActionResult> GetFilteredAsync(string orgCodeName,
                                                      string appCodeName,
                                                      [FromQuery][CanBeNull] string filter = "",
                                                      [FromQuery][CanBeNull] string orderBy = "",
                                                      [FromQuery] int? offset = 0,
                                                      [FromQuery] int? limit = 20)
    {
        var request = new GetFilteredRoleListQueryRequest(orgCodeName, appCodeName, ListParams);
        var validationResult = await listParamsValidator.ValidateAsync(ListParams);
        var requestValidationResult = await getFilteredRoleListQueryValidator.ValidateAsync(request);
        if (!validationResult.IsValid || !requestValidationResult.IsValid)
        {
            var validationResultErrors = validationResult.ToErrorDictionary();
            var requestValidationResultErrors = requestValidationResult.ToErrorDictionary();
            var errors = validationResultErrors.Merge(requestValidationResultErrors);
            return BadRequest(errors);
        }

        return await Send(request);
    }

    [HttpGet("all", Name = "GetAllRoles")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<RoleModel>>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetAllRolesAsync(string orgCodeName, string appCodeName)
    {
        var request = new GetAllRoleListQueryRequest(orgCodeName, appCodeName);
        var validationResult = await listParamsValidator.ValidateAsync(ListParams);
        var requestValidationResult = await getAllRoleListQueryValidator.ValidateAsync(request);
        if (!validationResult.IsValid || !requestValidationResult.IsValid)
        {
            var validationResultErrors = validationResult.ToErrorDictionary();
            var requestValidationResultErrors = requestValidationResult.ToErrorDictionary();
            var errors = validationResultErrors.Merge(requestValidationResultErrors);
            return BadRequest(errors);
        }

        return await Send(request);
    }

    [HttpGet("{id}", Name = "GetById")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<RoleModel>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetRoleByIdAsync([FromRoute] string id, [FromRoute] string orgCodeName, [FromRoute] string appCodeName)
    {
        var request = new GetRoleByIdQueryRequest(orgCodeName, appCodeName, id);
        var validationResult = await getRoleByIdQueryValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpPost(Name = "CreateRole")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(201, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> CreateRoleAsync(CreateRoleBody command)
    {
        return await Send(new CreateCommandRequest<CreateRoleBody>(command));
    }

    [HttpPut("{id}", Name = "UpdateRole")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> UpdateRoleAsync(string id, UpdateRoleBody command)
    {
        return await Send(new UpdateCommandRequest<UpdateRoleBody>(id, command));
    }

    [HttpDelete("{id}", Name = "DeleteRole")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(204)]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteRoleAsync([FromRoute] string id)
    {
        var request = new DeleteCommandRequest<RoleModel>(id);
        var validationResult = await deleteCommandRequestRoleModelValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }
}