﻿using AutoMapper;
using Axon.Core.Api.Extensions;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Domain.Enums;
using static Axon.Core.Domain.Entities.OrganisationEntity;

namespace Axon.Core.Api.Commands.Organisation.AddAppListTo
{
    internal sealed class AddAppListToOrganisationCommandHandler : BaseCommandHandler<OrganisationEntity, AddAppListToOrganisationCommandRequest, CommandResponse>
    {

        private readonly IAppRepository appRepo;

        public AddAppListToOrganisationCommandHandler(IOrganisationRepository orgRepo, IAppRepository appRepo, IMapper mapper, IMessageBus messageBus)
            : base(orgRepo, mapper, messageBus)
        {
            Guard.IsNotNull(appRepo);
            this.appRepo = appRepo;
        }

        public async override Task<CommandResponse> Handle(AddAppListToOrganisationCommandRequest request, CancellationToken cancellationToken)
        {
            if (!await Repo.TryGetItemAsync(request.OrganisationId, out var org)) return CommandResponse.NotFound(nameof(OrganisationEntity), request.OrganisationId);

            var apps = new List<AppConfigEntity>();
            if (org.Apps != null) apps.AddRange(org.Apps);

            foreach (var appId in request.Apps)
            {
                if (!await appRepo.TryGetItemAsync(appId, out var app)) return CommandResponse.NotFound(nameof(AppEntity), appId);

                if (apps.Any(x => x.AppId == app.Id))
                    return CommandResponse.Failed(nameof(appId), $"Application with id  `{appId}` already exists in organisation with id `{request.OrganisationId}`");

                apps.Add(new AppConfigEntity
                {
                    AppId = app.Id,
                    DisplayName = app.DisplayName,
                    Enabled = true,
                    AppCodeName = app.AppCodeName,
                    Status = AppStatus.ConfigComplete
                });

            }

            org.Apps = apps;

            await Repo.UpdateItemAsync(org.Id, org);

            IEnumerable<AppConfigEntity> appConfig = org.Apps.Where(s =>  request.Apps.Contains(s.AppId));
            await SendMessage(org, appConfig);

            return CommandResponse.Success();
        }

        private async Task SendMessage(OrganisationEntity org, IEnumerable<AppConfigEntity> apps)
        {
            var msg = new OrgUpdatedEvent(){
                OrgId = org.Id,
                OrgCodeName = org.CodeName,
                AppIds = apps.Select(item => item.AppId).ToArray(),
                Action = EventActionType.Updated
            };

            await MessageBus.PublishAsync(msg);
        }
    }
}