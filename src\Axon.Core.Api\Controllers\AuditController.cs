﻿using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Queries.Audit;
using Axon.Core.Api.Services.Authorisation;
using FluentValidation;
using MassTransit;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Api.Controllers
{
    [ApiController]
    [Produces("application/json", "application/xml")]
    [ProducesResponseType(401, Type = typeof(CommandResponse))]
    [ProducesResponseType(403, Type = typeof(CommandResponse))]
    [ProducesResponseType(500, Type = typeof(CommandResponse))]
    [Route("v{version:apiVersion}/Organisation/{orgCodeName}/app/{appCodeName}/audit")]
    [Authorize]
    public class AuditController : ApiControllerBase
    {
        private readonly IValidator<GetAuditsQueryRequest> getAuditsQueryValidator;
        private readonly IValidator<GetAuditFiltersQueryRequest> getAuditFiltersQueryValidator;
        private readonly IValidator<GetAuditFiltersUrlQueryRequest> getAuditFiltersUrlValidator;
        private readonly IValidator<GetAuditsUrlQueryRequest> getAuditUrlValidator;

        public AuditController(IMediator mediator, 
                               IValidator<GetAuditsQueryRequest> getAuditsQueryValidator, 
                               IValidator<GetAuditFiltersQueryRequest> getAuditFiltersQueryValidator, 
                               IValidator<GetAuditsUrlQueryRequest> getAuditUrlValidator, 
                               IValidator<GetAuditFiltersUrlQueryRequest> getAuditFiltersUrlValidator) : base(mediator)
        {
            this.getAuditsQueryValidator = getAuditsQueryValidator;
            this.getAuditFiltersQueryValidator = getAuditFiltersQueryValidator;
            this.getAuditUrlValidator = getAuditUrlValidator;
            this.getAuditFiltersUrlValidator = getAuditFiltersUrlValidator;
        }

        [HttpGet(Name = "GetAudits")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<string>>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [HasOrganisationPermissions(permissionsKey: nameof(CorePermissions.ViewAudit), useTargetApplicationPermissions: true)]
        public async Task<IActionResult> GetAuditsAsync(string orgCodeName, string appCodeName)
        {
            var request = new GetAuditsQueryRequest(orgCodeName, appCodeName, HttpContext.Request.Host.Host, HttpContext.Request.QueryString);
            var validationResult = await getAuditsQueryValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpGet("filters", Name = "GetAuditFilters")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<string>>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [HasOrganisationPermissions(permissionsKey: nameof(CorePermissions.ViewAudit), useTargetApplicationPermissions: true)]
        public async Task<IActionResult> GetAuditFiltersAsync(string orgCodeName, string appCodeName)
        {
            var request = new GetAuditFiltersQueryRequest(orgCodeName, appCodeName, HttpContext.Request.Host.Host);
            var validationResult = await getAuditFiltersQueryValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpGet("url",Name = "GetAuditsUrl")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<string>>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [HasOrganisationPermissions(permissionsKey: nameof(CorePermissions.ViewAudit), useTargetApplicationPermissions: true)]
        public async Task<IActionResult> GetAuditsUrlAsync(string orgCodeName, string appCodeName)
        {
            var request = new GetAuditsUrlQueryRequest(orgCodeName, appCodeName, HttpContext.Request.Host.Host);
            var validationResult = await getAuditUrlValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpGet("url/filters", Name = "GetAuditFiltersUrl")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<string>>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [HasOrganisationPermissions(permissionsKey: nameof(CorePermissions.ViewAudit), useTargetApplicationPermissions: true)]
        public async Task<IActionResult> GetAuditFiltersUrlAsync(string orgCodeName, string appCodeName)
        {
            var request = new GetAuditFiltersUrlQueryRequest(orgCodeName, appCodeName, HttpContext.Request.Host.Host);
            var validationResult = await getAuditFiltersUrlValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }
    }
}
