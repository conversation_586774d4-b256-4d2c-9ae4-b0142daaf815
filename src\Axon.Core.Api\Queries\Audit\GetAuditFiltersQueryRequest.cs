﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Audit;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace Axon.Core.Api.Queries.Audit
{
    public class GetAuditFiltersQueryRequest : IRequest<CommandResponse<AuditFilters>>
    {
        public string OrgCodeName { get; }
        public string AppCodeName { get; }
        public string Host { get; }

        public GetAuditFiltersQueryRequest(string orgCodeName, string appCodeName, string host)
        {
            OrgCodeName = orgCodeName;
            AppCodeName = appCodeName;
            Host = host;
        }
    }
}
