﻿using Axon.Core.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Domain.Interfaces.Persistence
{
    public interface ISessionRepository : IPartitionedRepository<SessionEntity>
    {
        public Task CreateNewSession(Guid sessionId, string userId, DateTime startTime);
        public Task ExpireSession(Guid sessionId, string userId);
        Task<SessionEntity> GetActiveSession(Guid sessionId, string userId);
        Task<IReadOnlyCollection<SessionEntity>> GetActiveSessions(string userId);
    }
}