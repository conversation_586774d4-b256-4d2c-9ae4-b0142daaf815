﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppSetting;
using MediatR;
using System.Collections.Generic;

namespace Axon.Core.Api.Queries.AppOrganisationSettings
{
    public class GetOrganisationAppMasterDataQueryRequest : IRequest<CommandResponse<IReadOnlyCollection<MasterDataValueModel>>>
    {
        public string OrgCodeName { get; }
        public string AppCodeName { get; }
        public GetOrganisationAppMasterDataQueryRequest(string orgCodeName, string appCodeName)
        {
            OrgCodeName = orgCodeName;
            AppCodeName = appCodeName;
        }
    }
}
