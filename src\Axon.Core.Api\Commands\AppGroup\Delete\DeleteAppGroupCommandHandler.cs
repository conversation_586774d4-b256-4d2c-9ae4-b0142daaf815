﻿using AutoMapper;
using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Api.Services.AppGroup;
using Axon.Core.Contracts;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Exceptions;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Infrastructure.Cosmos.Repository;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Google.Protobuf.WellKnownTypes;
using Phlex.Core.FunctionalExtensions.Results;
using Phlex.Core.MessageBus;
using System;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Domain.Extensions;
using static Grpc.Core.Metadata;

namespace Axon.Core.Api.Commands.AppGroup.Delete
{
    internal class DeleteAppGroupCommandHandler : DeleteCommandHandler<AppGroupEntity, AppGroupModel, AppGroupUpdatedEvent>
    {
        private readonly ICorrelationIdProvider correlationIdProvider;
        private readonly IClientDetailsProvider clientDetailsProvider;
        private readonly IAppGroupManager appGroupManager;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAppRepository appRepository;

        public DeleteAppGroupCommandHandler(
            IAppGroupRepository repo,
            IAppGroupManager appGroupManager,
            IMapper mapper,
            IMessageBus messageBus,
            IClientDetailsProvider clientDetailsProvider,
            ICorrelationIdProvider correlationIdProvider,
            IOrganisationRepository organisationRepository,
            IAppRepository appRepository)
            : base(repo, mapper, messageBus)
        {
            this.appGroupManager = appGroupManager;
            this.clientDetailsProvider = clientDetailsProvider;
            this.correlationIdProvider = correlationIdProvider;
            this.organisationRepository = organisationRepository;
            this.appRepository = appRepository;
        }

        public override async Task<Result> Handle(DeleteCommandRequest<AppGroupModel> request, CancellationToken cancellationToken)
        {
            var appGroupRepository = (IAppGroupRepository)Repo;
            var existingEntity = await appGroupRepository.GetItemAsync(request.Id);
            if (existingEntity == null)
            {
                throw new EntityNotFoundException(nameof(AppGroupEntity), request.Id);
            }

            var org = await organisationRepository.GetItemByCodeNameAsync(existingEntity.OrganisationCodeName);
            if (org == default(OrganisationEntity))
            {
                return Result.Failure($"Unable to delete, Organisation `{existingEntity.OrganisationCodeName}` does not exist");
            }
            var appId = await appRepository.GetAppIdByAppCode(existingEntity.AppCodeName);
            if (appId == default(string))
            {
                return Result.Failure($"Unable to delete, App `{existingEntity.AppCodeName}` does not exist");
            }

            var correlationId = correlationIdProvider.Provide();
            var clientDetails = clientDetailsProvider.Provide();

            await appGroupManager.RemoveAppGroupAndAccessEntities(existingEntity, correlationId, clientDetails);

            AppGroupUpdatedEvent appGroupEvent = new AppGroupUpdatedEvent
            {
                OrgId = org.Id,
                OrgCodeName = org.CodeName,
                AppCodeName = existingEntity.AppCodeName,
                AppId = appId,
                GroupId = existingEntity.GroupId,
                Action = EventActionType.Deleted
            };

            await MessageBus.PublishAsync(appGroupEvent, cancellationToken: cancellationToken);

            return Result.Success();
        }
    }
}
