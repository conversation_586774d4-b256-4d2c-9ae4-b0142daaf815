﻿using Axon.Core.Domain.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Domain.Interfaces.Persistence
{
    public interface IScopeResourceRepository : IRepository<ScopeResourceEntity>
    {
        Task<IList<ScopeResourceEntity>> GetScopeResourcesAsync(string appCodeName);
        Task<IList<ScopeResourceEntity>> GetScopeResourcesAsync(string appCodeName, string orgCodeName);
        Task<IList<ScopeResourceEntity>> GetScopeResourcesAsync(string appCodeName, string orgCodeName, string scope);
        Task<IList<ScopeResourceEntity>> GetScopeResourcesAsync(string appCodeName, string orgCodeName, IReadOnlyCollection<string> scopes);
    }
}