﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.UserPreference;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using MediatR;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.UserPreferences.NameQuery
{
    internal class GetPreferenceByKeyQueryHandler : IRequestHandler<GetPreferenceByKeyQueryRequest, CommandResponse<UserPreferencesModel>>
    {
        private readonly IUserPreferenceRepository repo;
        private readonly ICurrentUserProvider currentUserProvider;

        public GetPreferenceByKeyQueryHandler(IUserPreferenceRepository repo, ICurrentUserProvider currentUserProvider)
        {
            Guard.IsNotNull(repo);
            this.repo = repo;
            Guard.IsNotNull(currentUserProvider);
            this.currentUserProvider = currentUserProvider;
        }

        public async Task<CommandResponse<UserPreferencesModel>> Handle(GetPreferenceByKeyQueryRequest request, CancellationToken cancellationToken)
        {
            var userContext = currentUserProvider.GetUserContext();
            var preferences = await repo.GetItemAsync(userContext.ObjectId);
            if(preferences==null || !preferences.UserPreferences.TryGetValue(request.Key, out var value))
                return CommandResponse<UserPreferencesModel>.Data(new UserPreferencesModel(new Dictionary<string, string>()));

            return CommandResponse<UserPreferencesModel>.Data(new UserPreferencesModel(new Dictionary<string, string> { { request.Key, value } }));
        }
    }
}
