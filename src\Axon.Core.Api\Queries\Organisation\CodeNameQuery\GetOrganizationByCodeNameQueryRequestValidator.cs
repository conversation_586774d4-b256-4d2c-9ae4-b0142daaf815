﻿using Axon.Core.Api.Validators;
using FluentValidation;

namespace Axon.Core.Api.Queries.Organisation.CodeNameQuery
{
    public class GetOrganizationByCodeNameQueryRequestValidator : AbstractValidator<GetOrganisationByCodeNameRequest>
    {
        public GetOrganizationByCodeNameQueryRequestValidator()
        {
            RuleFor(x => x.CodeName)
                .NotEmpty()
                .MustBeAValidCodeName();
        }
    }
}
