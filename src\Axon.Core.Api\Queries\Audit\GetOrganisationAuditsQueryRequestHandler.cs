﻿using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Providers;
using CommunityToolkit.Diagnostics;
using JetBrains.Annotations;
using MediatR;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Query.Validator;
using Microsoft.Extensions.Logging;
using Microsoft.OData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CommandResponse = Axon.Core.Api.Commands.CommandResponse;

namespace Axon.Core.Api.Queries.Audit
{
    [UsedImplicitly]
    internal sealed class GetOrganisationAuditsQueryRequestHandler : IRequestHandler<GetOrganisationAuditsQueryRequest, CommandResponse>
    {
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAuditProvider<TenantAudit> auditProvider;
        private readonly ILogger<GetOrganisationAuditsQueryRequestHandler> logger;

        public GetOrganisationAuditsQueryRequestHandler(IOrganisationRepository organisationRepository,
            IAuditProvider<TenantAudit> dataProvider,
            ILogger<GetOrganisationAuditsQueryRequestHandler> logger)
        {
            Guard.IsNotNull(organisationRepository);
            this.organisationRepository = organisationRepository;
            Guard.IsNotNull(dataProvider);
            this.auditProvider = dataProvider;
            Guard.IsNotNull(logger);
            this.logger = logger;
        }

        public async Task<CommandResponse> Handle(GetOrganisationAuditsQueryRequest request, CancellationToken cancellationToken)
        {
            var org = await organisationRepository.GetItemByCodeNameAsync(request.OrgCodeName);

            if (org == null)
            {
                return CommandResponse.Forbidden("organisation", request.OrgCodeName);
            }

            try
            {
                request.OdataOptions.Validate(new ODataValidationSettings() { MaxTop = request.OdataOptions.Context.DefaultQueryConfigurations.MaxTop });
            }
            catch (ODataException odataException)
            {
                logger.LogError(odataException, "OData options are not valid");
                return CommandResponse.Failed(new Dictionary<string, string[]>() { { "ODataOptions", new[] { "Error: Provided OData options are not supported" } } });
            }

            var auditQuery = auditProvider.QueryEvents();

            if (request.OdataOptions.Filter != null)
            {
                auditQuery = request.OdataOptions.Filter?.ApplyTo(auditQuery, new ODataQuerySettings()) as IQueryable<TenantAudit>;
            }

            //apply query filter to lock callers to tenant provided
            //to prevent them seeing audits that they shouldn't have access too
            auditQuery = auditQuery.Where(x => x.Tenant.Equals(org.CodeName, StringComparison.InvariantCulture) &&
            x.AppCodeName.Equals("axon-core", StringComparison.InvariantCultureIgnoreCase));

            if (request.OdataOptions.OrderBy != null)
            {
                auditQuery = request.OdataOptions.OrderBy.ApplyTo<TenantAudit>(auditQuery, new ODataQuerySettings());
            }

            if(request.OdataOptions.Skip != null)
            {
                auditQuery = request.OdataOptions.Skip.ApplyTo<TenantAudit>(auditQuery, new ODataQuerySettings());
            }

            //If top hasn't been provided on the request, then limit return to the maximum
            if (request.OdataOptions.Top == null)
            {
                auditQuery = auditQuery.Take(request.OdataOptions.Context.DefaultQueryConfigurations.MaxTop.Value);
            }
            else
            {
                auditQuery = request.OdataOptions.Top?.ApplyTo<TenantAudit>(auditQuery, new ODataQuerySettings());
            }

            //Materialize query here rather than having it flow through the odata controller
            var materializedQuery = auditQuery.ToList();

            return CommandResponse.Data(materializedQuery);
        }
    }
}
