﻿using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Axon.Core.Domain.Entities.AppOrganisationSettingsEntity;

namespace Axon.Core.Api.Validators.AppOrganisationSettings
{
    public class MasterDataSelectionValidator : IMasterDataSelectionValidator
    {
        private readonly IMasterDataRepository masterDataRepository;
        private readonly IRoleDefinitionRepository roleDefinitionRepository;
        private readonly IRoleRepository roleRepository;
        private readonly IAppGroupRepository appGroupRepository;
        private readonly IAccessRepository accessRepository;

        public MasterDataSelectionValidator(IMasterDataRepository masterDataRepository, 
                                            IRoleDefinitionRepository roleDefinitionRepository, 
                                            IRoleRepository roleRepository, 
                                            IAppGroupRepository appGroupRepository, 
                                            IAccessRepository accessRepository)
        {
            this.masterDataRepository = masterDataRepository;
            this.roleDefinitionRepository = roleDefinitionRepository;
            this.roleRepository = roleRepository;
            this.appGroupRepository = appGroupRepository;
            this.accessRepository = accessRepository;
        }

        public async Task<(bool isValid, string errorMessage)> Validate(string masterDataType,
                                                                   IReadOnlyCollection<AppOrganisationSelectedMasterData> selectedMasterData)
        {
            //Having nothing selected is completely valid
            //return straight away to prevent pointless db call
            if (!selectedMasterData.Any())
            {
                return (true, null);
            }

            var matching = await masterDataRepository.GetMasterDataAsync(masterDataType, selectedMasterData.Select(x => x.Id).ToList());

            foreach (var item in selectedMasterData)
            {
                if (!matching.Any(m => m.Id.Equals(item.Id, System.StringComparison.Ordinal) &&
                                      m.Name.Equals(item.Name, System.StringComparison.Ordinal)))
                {
                    return (false, $"Can find no availble master data of type {masterDataType} referenced by id: {item.Id} and name: {item.Name}");
                }
            }

            return (true, null);
        }

        public async Task<(bool isValid, string errorMessage)> ValidateUpdate(string appCodeName,
                                                                              string orgCodeName,
                                                                              string masterDataType,
                                                                              IReadOnlyCollection<AppOrganisationSelectedMasterData> currentSelectedMasterData,
                                                                              IReadOnlyCollection<AppOrganisationSelectedMasterData> newSelectedMasterData)
        {
            var missingInNewSet = currentSelectedMasterData.ExceptBy(newSelectedMasterData.Select(x=> x.Id), x => x.Id).ToList();

            if (!missingInNewSet.Any())
            {
                return (true, null);
            }
            
            var definition = await roleDefinitionRepository.GetItemByAppCodeAsync(appCodeName);
            if(definition == null)
            {
                //If an app has no role definition, then it can't be using any master data as scopes
                return (true, null);
            }

            if(!definition.Permissions?.Exists(p => p.ValidScopes?.Exists(s => s.ScopeType == ScopeType.MasterData && 
                                                                         s.Scope.Equals(masterDataType, StringComparison.OrdinalIgnoreCase)) == true) == true)
            {
                //If no permission on the definition references the master data type then it isn't in use as scopes
                return (true, null );
            }

            var convertedResources = missingInNewSet.Select(sr => new RoleEntity.Resource { Id = sr.Id, Name = sr.Name }).ToList();

            var inUserOnRoles = await roleRepository.GetInUseResources(orgCodeName, appCodeName, masterDataType, convertedResources);

            if (inUserOnRoles.Any())
            {
                return (false, $"The resource(s) {string.Join(",", inUserOnRoles.Select(x=>x.Name))} are in use on role(s) so cannot be deselected");
            }

            var inUseOnAppGroup = await appGroupRepository.GetInUseResources(orgCodeName, appCodeName, masterDataType, convertedResources);

            if (inUseOnAppGroup.Any())
            {
                return (false, $"The resource(s) {string.Join(",", inUserOnRoles.Select(x => x.Name))} are in use on group(s) so cannot be deselected");
            }


            var inUseOnAccess = await accessRepository.GetInUseResources(orgCodeName, appCodeName, masterDataType, convertedResources);

            if (inUseOnAccess.Any())
            {
                return (false, $"The resource(s) {string.Join(",", inUseOnAccess.Select(x => x.Name))} are in use on user(s) so cannot be deselected");
            }


            return (true, null);
        } 
    }
}
