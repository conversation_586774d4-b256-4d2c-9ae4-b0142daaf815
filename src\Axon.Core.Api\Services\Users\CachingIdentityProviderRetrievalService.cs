﻿using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Shared.Config;
using Axon.Core.Shared.Services.Users;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Phlex.Core.Caching.Abstractions;
using Phlex.Core.Caching.Memory;
using System;
using System.Threading.Tasks;

namespace Axon.Core.Api.Services.Users
{
    public readonly record struct IdentityProviderCacheKey(string Issuer) : ICacheKey
    {
        public string Name => $"identity_provider|{Issuer}";
    }

    internal class CachingIdentityProviderRetrievalService : IIdentityProviderRetrievalService
    {
        private readonly IMemoryCacheStore memoryCacheStore;
        private readonly IIdentityProviderRepository identityProviderRepository;
        private readonly TimeSpan cacheItemDuration;
        private readonly CacheItemPriority cacheItemPriority;

        public CachingIdentityProviderRetrievalService(IMemoryCacheStore memoryCacheStore,
                                                       IIdentityProviderRepository identityProviderRepository,
                                                       IOptions<CachingOptions> config)
        {
            this.memoryCacheStore = memoryCacheStore;
            this.identityProviderRepository = identityProviderRepository;

            if (config.Value.Caches.TryGetValue(CachingOptions.IdentityProviderCacheKey, out var foundOptions))
            {
                cacheItemDuration = foundOptions.CacheItemDuration;
                cacheItemPriority = foundOptions.CacheItemPriority;
            }
            else
            {
                cacheItemDuration = config.Value.DefaultCacheItemDuration;
                cacheItemPriority = config.Value.DefaultCacheItemPriority;
            }
        }

        public Task<IdentityProviderDetails> GetAsync(string issuer)
        {
            var key = new IdentityProviderCacheKey(issuer);

            return memoryCacheStore.GetOrCreateAsync(key, entry => CacheMiss(key), cacheItemDuration, cacheItemPriority);
        }

        private async Task<IdentityProviderDetails> CacheMiss(IdentityProviderCacheKey cacheKey)
        {
            var identityProvider = await identityProviderRepository.GetByIssuer(cacheKey.Issuer);

            if (identityProvider == null)
            {
                return null;
            }


            var sessionDuration = identityProvider.AutoLogoutTime.HasValue ? TimeSpan.FromMinutes(identityProvider.AutoLogoutTime.Value) : TimeSpan.FromMinutes(20);

            //If there are no stored details, then use defaults instead
            //The warning time should be > half way through the auto logout time so that the cookie sliding duration will occur
            var sessionDetails = new IdentityProviderSessionDetails(sessionDuration,
                                                                    identityProvider.AutoLogoutWarningTime.HasValue ? TimeSpan.FromMinutes(identityProvider.AutoLogoutWarningTime.Value): (sessionDuration / 2) + TimeSpan.FromMinutes(2));

            return new IdentityProviderDetails(identityProvider.Id, identityProvider.Name, identityProvider.Issuer, identityProvider.OidcConfigUrl, identityProvider.Type, sessionDetails);
        }
    }
}
