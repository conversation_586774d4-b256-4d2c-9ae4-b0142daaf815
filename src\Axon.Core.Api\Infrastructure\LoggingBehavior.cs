﻿using System.Threading;
using System.Threading.Tasks;
using CommunityToolkit.Diagnostics;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.Core.Api.Infrastructure
{
    public class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse> where TRequest : IRequest<TResponse>
    {
        private readonly ILogger<LoggingBehavior<TRequest, TResponse>> logger;

        public LoggingBehavior(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
        {
            Guard.IsNotNull(logger);
            this.logger = logger;
        }

        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            logger.LogDebug($"Execution of {typeof(TRequest).Name} started");
            var response = await next();
            logger.LogDebug($"Execution of {typeof(TRequest).Name} completed returning: {typeof(TResponse).Name}");
            return response;
        }
    }
}