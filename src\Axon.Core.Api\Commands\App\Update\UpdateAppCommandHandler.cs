using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.App.Update;

internal class UpdateAppCommandHandler : BaseUpdateCommandHandler<AppBody, AppEntity, AppUpdated>
{
    public UpdateAppCommandHandler(IAppRepository repo, IMapper mapper, IMessageBus messageBus) :
        base(repo, mapper, messageBus)
    {
    }

    public override async Task<CommandResponse> Handle(UpdateCommandRequest<AppBody> request, CancellationToken cancellationToken)
    {
        var appRepository = (IAppRepository) Repo;
        var existing = (await appRepository.GetItemsByDisplayNameAsync(request.Model.DisplayName)).AsEnumerable();

        if (existing.Any(e => e.Id != request.Id)) return CommandResponse.Failed(nameof(request.Model.DisplayName), $"App `{request.Model.DisplayName}` already exists");

        existing = (await appRepository.GetItemsByAppCodeNameAsync(request.Model.AppCodeName)).AsEnumerable();
        if (existing.Any(e => e.Id != request.Id)) return CommandResponse.Failed(nameof(request.Model.AppCodeName), $"App with application CodeName `{request.Model.AppCodeName}` already exists.");


        return await base.Handle(request, cancellationToken);
    }
}