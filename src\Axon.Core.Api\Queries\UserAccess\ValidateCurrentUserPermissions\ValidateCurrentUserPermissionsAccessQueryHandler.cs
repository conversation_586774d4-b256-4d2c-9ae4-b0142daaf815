﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Interfaces.Auth;
using MediatR;

namespace Axon.Core.Api.Queries.UserAccess.ValidateCurrentUserPermissions;

internal class ValidateCurrentUserPermissionsAccessQueryHandler : IRequestHandler<ValidateCurrentUserPermissionsAccessQuery, CommandResponse<ValidateCurrentUserPermissionsAccessQueryResponse>>
{
    private readonly IAccessService accessService;
    private readonly IUserRequestContext userRequestContext;

    public ValidateCurrentUserPermissionsAccessQueryHandler(IAccessService accessService, IUserRequestContext userRequestContext)
    {
        this.accessService = accessService;
        this.userRequestContext = userRequestContext;
    }

    public async Task<CommandResponse<ValidateCurrentUserPermissionsAccessQueryResponse>> Handle(ValidateCurrentUserPermissionsAccessQuery request, CancellationToken cancellationToken)
    {
        var email = userRequestContext.GetEmailAddress();

        var appCodeName = request.AppCodeName ?? AppNameConstants.AxonCoreCodeName;

        var result = new Dictionary<string, bool>();

        var permissions = request.OrgCodeName != null ? [await accessService.GetEffectivePermissions(email, appCodeName, request.OrgCodeName)] :
            await accessService.GetUsersDirectEffectivePermissionsForApp(email, appCodeName);

        var permissionList = request.Permissions.GroupJoin(permissions.SelectMany(x => x.EffectivePermissions.Permissions),
            req => req,
            perm => perm.Name, (req, perm) => new { Name = req, Permissions = perm }).ToList();
        
        foreach (var item in permissionList)
        {
            
            if (item.Permissions.Any(x => x.Allow))
            {
                result.Add(item.Name, item.Permissions.Any(x => x.Allow));
            }
        }

        var response = new ValidateCurrentUserPermissionsAccessQueryResponse(result)
        {
            AllAccess = permissionList.All(x => x.Permissions.Any() && x.Permissions.All(item => item.Allow)),
            AnyAccess = result.Any(x => x.Value)
        };
        return CommandResponse<ValidateCurrentUserPermissionsAccessQueryResponse>.Data(response);
    }
}