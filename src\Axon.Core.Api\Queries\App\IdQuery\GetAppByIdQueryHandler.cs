﻿using AutoMapper;
using Axon.Core.Api.Models.App;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.App.IdQuery
{
    [UsedImplicitly]
    internal class GetAppByIdQueryHandler : IdQueryHandler<AppEntity,AppModel>
    {
        public GetAppByIdQueryHandler(IAppRepository repo, IMapper mapper) : base(repo, mapper) { }
    }
}