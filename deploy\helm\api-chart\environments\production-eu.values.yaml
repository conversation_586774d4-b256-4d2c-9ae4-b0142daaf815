api:
  tag: latest

grpc:
  tag: latest

replicas: 3

ingress:
  tls:
    - tlsSecretName: tls-app-eu-smartphlex-com
      hosts:
        - app-eu.smartphlex.com
        - app.smartphlex.com
  hosts:
    - host: app-eu.smartphlex.com
      paths:
        - path: /api/core/(.*)
          pathType: ImplementationSpecific
    - host: app.smartphlex.com
      paths:
        - path: /api/core/(.*)
          pathType: ImplementationSpecific

scaledObject:
  # -- Minimum number of replicas that the scaled object will create
  minReplicas: 3
  # -- Maximum number of replicas that the scaled object will create
  maxReplicas: 10
  # -- This is the polling interval to check each trigger on for the Scaled object type. By default its 30 seconds.
  pollingInterval: 30
  # -- The period to wait after the last trigger reported active before scaling the deployment back to 0. By default it’s 5 minutes (300 seconds).
  cooldownPeriod: 300
  # -- The CPU percentage value to scale up on
  utilisation: 70

newrelic_api_app_name: axon-core-prod-api
newrelic_grpc_app_name: axon-core-prod-grpc
keyVaultName: axn-prod-kv-eun
clientId: b07bf619-7ce5-4dd5-8fa6-14fb944f8433
azureIssuer: https://login.microsoftonline.com/common/v2.0
azureUseCustomRefresh: false

gigyaClientId: rjYASuMfKHsbId9RgH9Qurhf
gigyaIssuer: https://aaas.cencora.com/oidc/op/v1.0/4_PGj6CfqhMMUKzG9BHAEdwA/authorize
gigyaUseCustomRefresh: true

corsOriginUrl0: https://app-eu.smartphlex.com
corsOriginUrl1: https://app.smartphlex.com

BlobStorageConnectionString: https://phcgvsharedstaticeun.blob.core.windows.net/

NamespaceName: "axn-prod-servicebus-eun"
cosmosdbName: Axon-Core-ApiDb
cosmosdbUrl: 'https://axn-prod-cosmos-eun.documents.azure.com:443/'

managedIdentityClientId: 79c19843-4646-4df7-afbe-885ba99b443d
azureWorkload:
  clientId: 79c19843-4646-4df7-afbe-885ba99b443d
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AzureBlobStorageContainersAppAvatarsFolderPrefix: prodeu/axon-avatar
AzureBlobStorageContainersOrganisationAvatarsFolderPrefix: prodeu/organisations
AzureBlobStorageContainersThemesFolderPrefix: prodeu/axon-theme

DataProtectionBlobStorageUri: 'https://axnprodstorageeun.blob.core.windows.net/'
DataProtectionKeyVaultKey: 'https://axn-prod-kv-eun.vault.azure.net/keys/AxonDataProtection'

GoodDataBaseUri: 'https://phlexglobal-embedded.cloud.gooddata.com/'
GoodDataEnvironment: 'prod'