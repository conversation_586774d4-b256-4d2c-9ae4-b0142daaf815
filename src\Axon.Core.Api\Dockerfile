ARG versionNo=0.0.0.0
ARG nugetSource=''
ARG nugetPassword=''
ARG projectName=Axon.Core
ARG configuration=Release
ARG waitScript='https://pxgstaticstoprodneu.blob.core.windows.net/scripts/wait'
ARG newRelicVersion=10.34.1
ARG newRelicFile=newrelic-dotnet-agent_${newRelicVersion}_amd64.tar.gz

# Use Alpine Base Image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
ARG newRelicVersion
ARG newRelicFile
ARG versionNo
EXPOSE 80
EXPOSE 443
# Add the NewRelic Agent
RUN apt-get update && apt-get install -y wget && rm -rf /var/lib/apt/lists/*
RUN cd /usr/local && \
    wget https://download.newrelic.com/dot_net_agent/previous_releases/${newRelicVersion}/${newRelicFile} &&\
    tar -xf ${newRelicFile} &&\
    rm ${newRelicFile}
# NewRelic settings
ENV CORECLR_ENABLE_PROFILING=1 \
CORECLR_PROFILER={36032161-FFC0-4B61-B559-F6C5D41BAE5A} \
CORECLR_NEWRELIC_HOME=/usr/local/newrelic-dotnet-agent \
CORECLR_PROFILER_PATH=/usr/local/newrelic-dotnet-agent/libNewRelicProfiler.so \
NEW_RELIC_DISTRIBUTED_TRACING_ENABLED=true

# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS publish
ARG nugetSource
ARG nugetPassword
ARG projectName
ARG versionNo
ARG configuration
WORKDIR /
COPY . .

## Optimize dotnet publish
RUN dotnet nuget add source ${nugetSource} -n Phlexglobal -u ignore -p ${nugetPassword} --store-password-in-clear-text \
 && dotnet restore src/${projectName}.Shared/${projectName}.Shared.csproj \
 && dotnet restore src/${projectName}.Authorisation.gRPC/${projectName}.Authorisation.gRPC.csproj \
 && dotnet restore src/${projectName}.gRPC/${projectName}.gRPC.csproj \
 && dotnet restore src/${projectName}.Api/${projectName}.Api.csproj \
 && dotnet restore test/${projectName}.Tests/${projectName}.Tests.csproj \
 && dotnet restore test/${projectName}.gRPC.Tests/${projectName}.gRPC.Tests.csproj \
 && dotnet restore test/${projectName}.Shared.Tests/${projectName}.Shared.Tests.csproj \
 && dotnet restore test/${projectName}.ArchTests/${projectName}.ArchTests.csproj \
 && dotnet restore test/${projectName}.gRPC.IntegrationTests/${projectName}.gRPC.IntegrationTests.csproj \
 && dotnet restore test/${projectName}.IntegrationTests/${projectName}.IntegrationTests.csproj \
 && dotnet publish src/${projectName}.Api/${projectName}.Api.csproj -c ${configuration} /property:Version=${versionNo} -o /app/publish \
 && dotnet nuget remove source Phlexglobal

# Stage 1, Test application
FROM publish AS testrunner
WORKDIR /
ARG projectName
ARG configuration
ARG waitScript
ENV configuration_env=${configuration}
ENV testProjectName=test/${projectName}.Tests/${projectName}.Tests.csproj
ENV grpcTestProjectName=test/${projectName}.gRPC.Tests/${projectName}.gRPC.Tests.csproj
ENV testAuthProjectName=test/${projectName}.Shared.Tests/${projectName}.Shared.Tests.csproj
ENV archTestProjectName=test/${projectName}.ArchTests/${projectName}.ArchTests.csproj
ENV intTestProjectName=test/${projectName}.IntegrationTests/${projectName}.IntegrationTests.csproj
ENV gRPCIntTestProjectName=test/${projectName}.gRPC.IntegrationTests/${projectName}.gRPC.IntegrationTests.csproj

## Add the wait script to the image
ADD ${waitScript} /wait
RUN chmod +x /wait

ENTRYPOINT /wait && dotnet test ${testProjectName} -c ${configuration_env} --logger "xunit;LogFilePath=/tests/output/TestResults/UnitTestResults.xml" --collect "XPlat Code coverage" --results-directory "/tests/output/TestResults" \
                 && dotnet test ${grpcTestProjectName} -c ${configuration_env} --logger "xunit;LogFilePath=/tests/output/TestResults/gRPCUnitTestResults.xml" --collect "XPlat Code coverage" --results-directory "/tests/output/TestResults" \
                 && dotnet test ${testAuthProjectName} -c ${configuration_env} --logger "xunit;LogFilePath=/tests/output/TestResults/UnitTestAuthResults.xml" --collect "XPlat Code coverage" --results-directory "/tests/output/TestResults" \
                 && dotnet test ${archTestProjectName} -c ${configuration_env} --logger "xunit;LogFilePath=/tests/output/TestResults/ArchTestResults.xml" --collect "XPlat Code coverage" --results-directory "/tests/output/TestResults" \
                 && dotnet test ${intTestProjectName} -c ${configuration_env} --logger "xunit;LogFilePath=/tests/output/TestResults/IntegrationTestResults.xml" --collect "XPlat Code coverage" --results-directory "/tests/output/TestResults" \
                 && dotnet test ${gRPCIntTestProjectName} -c ${configuration_env} --logger "xunit;LogFilePath=/tests/output/TestResults/gRPCIntegrationTestResults.xml" --collect "XPlat Code coverage" --results-directory "/tests/output/TestResults" --filter Environment!=Full

## Stage 2, Run application
FROM base AS final
ARG projectName
WORKDIR /app
COPY --from=publish /app/publish .

# Create a new user and change directory ownership
RUN adduser --disabled-password \
  --home /app \
  --gecos '' dotnetuser && chown -R dotnetuser /app

# Impersonate
USER dotnetuser
ENTRYPOINT ["dotnet", "Axon.Core.Api.dll"]