﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Services.Audit;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using JetBrains.Annotations;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.Audit
{
    [UsedImplicitly]
    internal class GetAuditsQueryRequestHandler : IRequestHandler<GetAuditsQueryRequest, CommandResponse>
    {
        public IAppRepository AppRepository;
        public IOrganisationRepository OrganisationRepository;
        public IConfiguration Config;
        public IAuditEndpointService AuditODataEndpointService;
        private readonly ILogger<GetAuditsQueryRequestHandler> Logger;

        public GetAuditsQueryRequestHandler(IOrganisationRepository organisationRepository,
            IAppRepository appRepository,
            IConfiguration config,
            IAuditEndpointService auditODataEndpointService,
            ILogger<GetAuditsQueryRequestHandler> logger)
        {
            Guard.IsNotNull(organisationRepository);
            OrganisationRepository = organisationRepository;
            Guard.IsNotNull(appRepository);
            AppRepository = appRepository;
            Guard.IsNotNull(auditODataEndpointService);
            AuditODataEndpointService = auditODataEndpointService;
            Guard.IsNotNull(logger);
            Logger = logger;
            Guard.IsNotNull(config);
            Config = config;
        }

        public async Task<CommandResponse> Handle(GetAuditsQueryRequest request, CancellationToken cancellationToken)
        {
            Logger.LogDebug("Entered GetAuditsQueryRequestHandler, orgCodeName:{OrgCodeName} appCodeName:{AppCodeName}, oDataOptions:{ODataOptions}", request.OrgCodeName,request.AppCodeName, request.ODataOptions);

            var validationResponse = ValidateQueryStringODataOptions(request.ODataOptions);
            if (validationResponse.status != HttpStatusCode.OK)
            {
                return validationResponse;
            }


            //axon core isn't a "real" app so won't have app settings etc
            if (request.AppCodeName != AppNameConstants.AxonCoreCodeName)
            {
                var response = await CheckOrgAndAppSecurity(request);
                if (response.status != HttpStatusCode.OK)
                {
                    return response;
                }

                var appEntity = await AppRepository.GetItemByAppCodeNameAsync(request.AppCodeName);

                if (!appEntity.ShowAudits)
                {
                    Logger.LogError("Auditing is not enabled for this application: {AppCodeName}.", request.AppCodeName);
                    return new CommandResponse() { status = HttpStatusCode.Forbidden, title = "Not available on this application" };
                }
            }

            //check the querystring to make sure it only contains odata items
            
            var oDataResponse = await AuditODataEndpointService.GetOData(request);
            if (oDataResponse.status != HttpStatusCode.OK)
                return oDataResponse;

            Logger.LogDebug("Exiting GetAuditsQueryRequestHandler, orgCodeName:{OrgCodeName} appCodeName:{AppCodeName}, oDataOptions:{ODataOptions}", request.OrgCodeName, request.AppCodeName, request.ODataOptions);
            return CommandResponse.Data(data: oDataResponse.data);

        }

        private async Task<CommandResponse> CheckOrgAndAppSecurity(GetAuditsQueryRequest request)
        {
            var app = await AppRepository.GetItemByAppCodeNameAsync(request.AppCodeName);
            if (app == null)
            {
                return CommandResponse.NotFound("app", request.AppCodeName);
            }

            var org = await OrganisationRepository.GetItemByCodeNameAsync(request.OrgCodeName);
            if (org == null)
            {
                return CommandResponse.NotFound("organisation", request.OrgCodeName);
            }

            var appExistsForOrg = org.Apps.Any(item => item.AppCodeName.Equals(request.AppCodeName, StringComparison.InvariantCulture));
            if (!appExistsForOrg)
            {
                return CommandResponse.Failed("AppCodeName", "Requested Application doesn't belong to organisation.",status:HttpStatusCode.BadRequest);
            }

            return CommandResponse.Success();
        }

        private CommandResponse ValidateQueryStringODataOptions(QueryString oDataOptions)
        {
            if(!oDataOptions.HasValue)
            {
                return CommandResponse.Success();
            }

            var options = oDataOptions.Value.Remove(0,1);

            foreach (string option in options.Split('&'))
            {
                var optionSplit = option.Split("=");
                switch (optionSplit[0])
                {
                    case "$filter":
                    case "$orderby":
                    case "$top":
                    case "$skip":
                    case "$count":
                    case "$expand":
                    case "$select":
                    case "$search": break;
                    default:
                        return CommandResponse.Failed("Query option", "Unrecognized OData Query Option supplied");
                }
            }
            return CommandResponse.Success();
        }
    }
}
