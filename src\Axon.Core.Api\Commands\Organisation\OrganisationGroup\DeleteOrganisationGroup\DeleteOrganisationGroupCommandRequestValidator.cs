﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.DeleteOrganisationGroup;

[UsedImplicitly]
public class DeleteOrganisationGroupCommandRequestValidator : AbstractValidator<DeleteOrganisationGroupCommandRequest>
{
    public DeleteOrganisationGroupCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName()
            .NotEmpty()
            .WithMessage("'OrganisationCodeName' must not be empty");
        RuleFor(x => x.GroupId)
            .MustBeAValidGuid()
            .NotEmpty()
            .WithMessage("'GroupId' must not be empty");
    }
}