﻿using Axon.Core.Api.Services.Users;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Infrastructure.IoC;
using Axon.Core.Shared.Extensions;
using Axon.Core.Shared.Services.Users;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http.Json;
using System.Security.Claims;
using System.Threading.Tasks;
using Axon.Core.Domain.Entities;

namespace Axon.Core.Api.Services.Authorisation
{
    internal record OidcUserIdentity(string Sub, string Email, string Name);

    internal interface IOidcUserIdentityService
    {
        Task<UserIdentityDetails> GetAsync(IEnumerable<Claim> claims, string accessToken);
    }

    internal sealed class OidcUserIdentityService : IOidcUserIdentityService
    {
        private const string authFailed = "AUTHORIZATION_FAILED";
        private readonly IIdentityProviderRetrievalService identityProviderRetrievalService;
        private readonly IUserRepository userRepository;
        private readonly IOidcEndpointHttpClient client;
        private readonly IOidcConfigAccessor oidcConfigAccessor;
        private readonly ILogger<OidcUserIdentityService> logger;

        public OidcUserIdentityService(IIdentityProviderRetrievalService identityProviderRetrievalService,
                                       IUserRepository userRepository,
                                       IOidcEndpointHttpClient client, 
                                       IOidcConfigAccessor oidcConfigAccessor, 
                                       ILogger<OidcUserIdentityService> logger)
        {
            this.identityProviderRetrievalService = identityProviderRetrievalService;
            this.userRepository = userRepository;
            this.client = client;
            this.oidcConfigAccessor = oidcConfigAccessor;
            this.logger = logger;
        }

        public async Task<UserIdentityDetails> GetAsync(IEnumerable<Claim> claims, string accessToken)
        {
            var idProvider = await identityProviderRetrievalService.GetAsync(claims.Issuer());

            if (idProvider == null)
            {
                logger.LogWarning("User authorisation failed as no identity provider found for issuer: {Issuer}", claims.Issuer());
                throw new UnauthorizedAccessException(authFailed);
            }

            var oidcClient = await oidcConfigAccessor.GetAsync(idProvider.OidcConfig);

            var userUrl = oidcClient.UserInfoEndpoint;

            var result = await client.SendRequestAsync(userUrl, accessToken);

            if(result.StatusCode != HttpStatusCode.OK)
            {
                logger.LogError("User authorisation failed, non success status code when calling: {UserUrl}, status code: {StatusCode}", userUrl, result.StatusCode);
                throw new UnauthorizedAccessException(authFailed);
            }

            var oidcUserDetails = await result.Content.ReadFromJsonAsync<OidcUserIdentity>();

            if(string.IsNullOrWhiteSpace(oidcUserDetails.Sub) || string.IsNullOrWhiteSpace(oidcUserDetails.Email))
            {
                logger.LogWarning("User authorisation failed, did not receive sub or email from oidc user endpoint: {UserUrl}", userUrl);
                throw new UnauthorizedAccessException(authFailed);
            }

            if (!oidcUserDetails.Sub.Equals(claims.ObjectId()))
            {
                logger.LogWarning("User authorisation failed, sub from user info endpoint: {UserInfoSub} does not match sub from claims {ClaimsSub}", oidcUserDetails.Sub, claims.ObjectId());
                throw new UnauthorizedAccessException(authFailed);
            }

            UserEntity foundUser = null;

            try
            {
                foundUser = await userRepository.GetUserByEmailAsync(oidcUserDetails.Email);
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex,"User authorisation failed for user {Email} unable to retrieve user with this email", oidcUserDetails.Email);
                throw new UnauthorizedAccessException(authFailed, ex);
            }

            if (foundUser == null)
            {
                logger.LogWarning("User authorisation failed for user {Email} no user exists with this email", oidcUserDetails.Email);
                throw new UnauthorizedAccessException(authFailed);
            }

            if (foundUser.Status != Domain.Enums.UserStatus.Active)
            {
                logger.LogWarning("User authorisation failed for user {Email}, user is not active", oidcUserDetails.Email);
                throw new UnauthorizedAccessException(authFailed);
            }
   
            if (!foundUser.IdentityProviderId.Equals(idProvider.Id, StringComparison.OrdinalIgnoreCase))
            {
                logger.LogWarning("User authorisation failed for user {UserId} as their identity provider does not match that of the issuer: {Issuer}", foundUser.Id, claims.Issuer());
                throw new UnauthorizedAccessException(authFailed);
            }

            return new UserIdentityDetails(foundUser.Id, foundUser.Email, foundUser.Name, new UserIdentityProviderDetails(idProvider.Id,
                                                                                                                          idProvider.Name,
                                                                                                                          idProvider.Issuer,
                                                                                                                          idProvider.Type,
                                                                                                                          idProvider.SessionDetails));
        }
    }
}
