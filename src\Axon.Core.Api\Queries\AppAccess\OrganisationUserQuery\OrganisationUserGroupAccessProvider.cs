﻿using Axon.Core.Api.Models.Organisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.AppAccess.OrganisationUserQuery;

public interface IOrganisationUserGroupAccessProvider
{
    Task<(OrganisationUserModel.GroupModel[], OrganisationUserModel.ChildOrganisationModel[])> ProvideAsync(string organisationId, string userId, bool includeGroups, bool includeChildOrganisations);
}

internal sealed class OrganisationUserGroupAccessProvider : IOrganisationUserGroupAccessProvider
{
    private readonly IAccessRepository accessRepository;
    private readonly IGroupRepository groupRepository;
    private readonly IOrganisationRepository organisationRepository;

    public OrganisationUserGroupAccessProvider(IAccessRepository accessRepository, IGroupRepository groupRepository, IOrganisationRepository organisationRepository)
    {
        this.accessRepository = accessRepository;
        this.groupRepository = groupRepository;
        this.organisationRepository = organisationRepository;
    }

    public async Task<(OrganisationUserModel.GroupModel[], OrganisationUserModel.ChildOrganisationModel[])> ProvideAsync(string organisationId, string userId, bool includeGroups, bool includeChildOrganisations)
    {
        var groups = Array.Empty<OrganisationUserModel.GroupModel>();
        var childOrganisations = Array.Empty<OrganisationUserModel.ChildOrganisationModel>();

        var groupAccessEntities = await accessRepository.GetAccessItemsForUserAsync(userId, AccessType.GroupAccess);

        if (includeGroups)
        {
            var groupAccessEntitiesOrganisation = groupAccessEntities
                .Where(accessEntity => string.Equals(accessEntity.OrganisationId, organisationId, StringComparison.OrdinalIgnoreCase))
                .ToList();

            var groupEntities = new List<GroupEntity>();
            var groupIds = groupAccessEntitiesOrganisation.Select(accessEntity => accessEntity.GroupId).Distinct();
            foreach (var groupId in groupIds)
            {
                var groupEntity = await groupRepository.GetItemAsync(groupId);
                if (groupEntity != null)
                    groupEntities.Add(groupEntity);
            }

            groups = groupEntities
                .Select(groupEntity => new OrganisationUserModel.GroupModel
                {
                    Id = groupEntity.Id,
                    Name = groupEntity.Name
                })
                .OrderBy(group => group.Name)
                .ToArray();
        }

        if (includeChildOrganisations)
        {
            var childOrganisationModels = new List<OrganisationUserModel.ChildOrganisationModel>();
            var organisationAccessEntities = await accessRepository.GetAccessItemsForUserAsync(userId, AccessType.OrganisationAccess);
            var userOrganisationIds = organisationAccessEntities.Select(accessEntity => accessEntity.OrganisationId).Distinct();
            foreach (var userOrganisationId in userOrganisationIds)
            {
                var organisation = await organisationRepository.GetItemAsync(userOrganisationId);
                if (organisation == null || !string.Equals(organisation.ParentOrganisationId, organisationId, StringComparison.OrdinalIgnoreCase))
                    continue;

                var groupEntities = new List<GroupEntity>();
                var groupIds = groupAccessEntities
                    .Where(accessEntity => string.Equals(accessEntity.OrganisationId, organisation.Id, StringComparison.OrdinalIgnoreCase))
                    .Select(accessEntity => accessEntity.GroupId)
                    .ToList();
                foreach (var groupId in groupIds)
                {
                    var groupEntity = await groupRepository.GetItemAsync(groupId);
                    if (groupEntity != null)
                        groupEntities.Add(groupEntity);
                }

                childOrganisationModels.Add(new OrganisationUserModel.ChildOrganisationModel
                {
                    OrganisationCodeName = organisation.CodeName,
                    OrganisationName = organisation.DisplayName,
                    Groups = groupEntities
                        .Select(groupEntity => new OrganisationUserModel.GroupModel
                        {
                            Id = groupEntity.Id,
                            Name = groupEntity.Name
                        })
                        .OrderBy(group => group.Name)
                        .ToArray()
                });
            }
            childOrganisations = childOrganisationModels
                .OrderBy(model => model.OrganisationName)
                .ToArray();
        }

        return (groups, childOrganisations);
    }
}