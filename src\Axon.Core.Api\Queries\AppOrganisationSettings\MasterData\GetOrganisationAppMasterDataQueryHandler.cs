﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppSetting;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using MediatR;

namespace Axon.Core.Api.Queries.AppOrganisationSettings;

internal class GetOrganisationAppMasterDataQueryHandler : IRequestHandler<GetOrganisationAppMasterDataQueryRequest, CommandResponse<IReadOnlyCollection<MasterDataValueModel>>>
{
    private readonly IAppOrganisationSettingsRepository appOrganisationSettingsRepository;
    private readonly IAppSettingsRepository appSettingsRepository;

    public GetOrganisationAppMasterDataQueryHandler(IAppSettingsRepository appSettingsRepository,
                                                    IAppOrganisationSettingsRepository appOrganisationSettingsRepository)
    {
        this.appSettingsRepository = appSettingsRepository;
        this.appOrganisationSettingsRepository = appOrganisationSettingsRepository;
    }

    public async Task<CommandResponse<IReadOnlyCollection<MasterDataValueModel>>> Handle(GetOrganisationAppMasterDataQueryRequest request, 
                                                                                         CancellationToken cancellationToken)
    {
        var appSettings = await appSettingsRepository.GetByAppCodeNameAsync(request.AppCodeName);

        if (appSettings == null)
        {
            return CommandResponse<IReadOnlyCollection<MasterDataValueModel>>.Data(new List<MasterDataValueModel>());
        }
            
        var appSettingsValues = await appOrganisationSettingsRepository.GetAppOrganisationSettingsAsync(request.OrgCodeName, request.AppCodeName);

        var result = appSettings.MasterData
            ?.Select(appSet => appSettingsValues?.MasterData != null && appSettingsValues.MasterData.TryGetValue(appSet.Key, out var matchingData) ? Convert(appSet.Key, appSet.Value, matchingData) :
                                                                                                                                                     Convert(appSet.Key, appSet.Value, []))
        .ToList() ?? [];

        return CommandResponse<IReadOnlyCollection<MasterDataValueModel>>.Data(result);
    }

    private static MasterDataValueModel Convert(string masterDataType,
                                                AppSettingsEntity.MasterDataSetting masterDataSetting,
                                                IEnumerable<AppOrganisationSettingsEntity.AppOrganisationSelectedMasterData> selectedValues)
    {
        return new MasterDataValueModel(masterDataSetting.Category,
                                        masterDataSetting.DisplayName,
                                        masterDataSetting.Description,
                                        masterDataType,
                                        selectedValues.Select(s => new MasterDataSelectedValue(s.Id, s.Name)).ToList());
    }
}