/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// OrgColour
    /// </summary>
    [DataContract(Name = "OrgColour")]
    public partial class OrgColour : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="OrgColour" /> class.
        /// </summary>
        /// <param name="highlights">highlights.</param>
        /// <param name="buttons">buttons.</param>
        /// <param name="hover">hover.</param>
        public OrgColour(string highlights = default(string), string buttons = default(string), string hover = default(string))
        {
            this.Highlights = highlights;
            this.Buttons = buttons;
            this.Hover = hover;
        }

        /// <summary>
        /// Gets or Sets Highlights
        /// </summary>
        [DataMember(Name = "highlights", EmitDefaultValue = true)]
        public string Highlights { get; set; }

        /// <summary>
        /// Gets or Sets Buttons
        /// </summary>
        [DataMember(Name = "buttons", EmitDefaultValue = true)]
        public string Buttons { get; set; }

        /// <summary>
        /// Gets or Sets Hover
        /// </summary>
        [DataMember(Name = "hover", EmitDefaultValue = true)]
        public string Hover { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class OrgColour {\n");
            sb.Append("  Highlights: ").Append(Highlights).Append("\n");
            sb.Append("  Buttons: ").Append(Buttons).Append("\n");
            sb.Append("  Hover: ").Append(Hover).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
