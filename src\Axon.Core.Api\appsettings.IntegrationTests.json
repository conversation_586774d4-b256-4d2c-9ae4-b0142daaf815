{"ConnectionStrings": {"Axon-Core-ApiDb": {"EndpointUrl": "https://localhost:8081/", "PrimaryKey": "C2y6yDjf5/R+ob0N8A7Cgv30VRDJIWEHLM+4QDU5DE2nQ9nDuVTqobD4b8mGGyPMbIZnqyMsEcaGQy67XIw/Jw=="}}, "AzureBlobStorage": {"StorageConnectionString": "UseDevelopmentStorage=true"}, "cors": {"origins": [{"uri": "https://app-dev.smartphlex.com"}, {"uri": "https://localhost:4000"}]}, "Audit": {"TestOdata": {"UseTestEndpoint": true, "TestEndpointOdataUrl": "http://localhost:5200/odata/audits", "TestEndpointFilterUrl": "http://localhost:5200/audits/filterOptions"}, "AxonCoreOdataEndpointUrlTemplate": "https://{env}:5001/v1/odata/organisation/{tenant}/Audits"}, "AzureIdentity": {"ManageIdentityClientId": ""}, "AzureAd": {"ClientId": "167cd45b-7d4f-4b3d-8c05-a87f12c40609", "TenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77"}, "EntraSettings": {"Issuer": "https://login.microsoftonline.com/common/v2.0", "UseCustomRefresh": "false"}, "Gigya": {"ClientId": "cUDSdI53tU5LgmVJH2AkCH-8", "Issuer": "https://tst.aaas.cencora.com/oidc/op/v1.0/4_Pv18t6XTOc51PxyYytQzHA/authorize", "UseCustomRefresh": "true"}}