﻿using Axon.Core.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Axon.Core.Domain.Interfaces.Persistence
{
    public interface IRoleDefinitionRepository : IRepository<RoleDefinitionEntity>
    {
        Task<IList<RoleDefinitionEntity>> GetItemsByAppCodesAsync(IEnumerable<string> appCodeNames);
        Task<RoleDefinitionEntity> GetItemByAppCodeAsync(string appCodeName);
    }
}
