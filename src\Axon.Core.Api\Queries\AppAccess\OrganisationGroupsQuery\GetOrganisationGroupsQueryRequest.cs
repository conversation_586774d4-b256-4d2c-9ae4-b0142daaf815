﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Shared.Api;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.AppAccess.OrganisationGroupsQuery;

public class GetOrganisationGroupsQueryRequest : IRequest<CommandResponse<ApiPagedListResult<OrganisationGroupModel>>>
{
    public string OrgCodeName { get; }
    public ListParams ListParams { get; }

    public GetOrganisationGroupsQueryRequest(string orgCodeName, ListParams listParams)
    {
        OrgCodeName = orgCodeName;
        ListParams = listParams;
    }
}