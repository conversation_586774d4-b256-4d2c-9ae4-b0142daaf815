﻿using MediatR;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.DeleteOrganisationGroupUser;

public class DeleteOrganisationGroupUserCommandRequest : IRequest<CommandResponse>
{
    public string OrganisationCodeName { get; }
    public string GroupId { get; }
    public string UserId { get; }

    public DeleteOrganisationGroupUserCommandRequest(string organisationCodeName, string groupId, string userId)
    {
        OrganisationCodeName = organisationCodeName;
        GroupId = groupId;
        UserId = userId;
    }
}