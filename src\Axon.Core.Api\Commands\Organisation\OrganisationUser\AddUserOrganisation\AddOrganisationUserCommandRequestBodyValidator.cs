﻿using Axon.Core.Api.Validators;
using Axon.Core.Domain.Enums;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser.AddUserOrganisation;

[UsedImplicitly]
public class AddOrganisationUserCommandRequestBodyValidator : AbstractValidator<AddOrganisationUserCommandRequestBody>
{
    public AddOrganisationUserCommandRequestBodyValidator()
    {
        RuleFor(x => x.Name)
            .MustBeAValidUserName();
        RuleFor(x => x.Email)
            .MustBeAValidEmail();
        RuleFor(x => x.Status)
            .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
            .MustBeAValidEnum(typeof(UserStatus));
        RuleForEach(x => x.Groups)
            .MustBeAValidGuid();
        RuleForEach(x => x.ChildOrganisations)
            .SetValidator(new GroupOrganisationValidator());
    }
}