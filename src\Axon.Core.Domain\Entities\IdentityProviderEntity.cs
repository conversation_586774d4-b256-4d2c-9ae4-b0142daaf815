﻿using System;
using Axon.Core.Domain.Entities.Base;
using Axon.Core.Shared.Constants.Auth;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Axon.Core.Domain.Entities
{
    public class IdentityProviderEntity : BaseEntity
    {
        [Obsolete("AccessLevel should be migrated to the OrganisationEntity and then removed from the IdentityProviderEntity.")]
        public string AccessLevel { get; set; }
        public string Name { get; set; }
        public string Issuer { get; set; }
        public string OidcConfigUrl { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public IdentityProviderType Type { get; set; }
        public int? AutoLogoutTime { get; set; }
        public int? AutoLogoutWarningTime { get; set; }
    }
}
