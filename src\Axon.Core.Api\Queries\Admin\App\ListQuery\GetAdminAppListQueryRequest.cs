﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.App;
using Axon.Core.Shared.Api;
using MediatR;
using System.Collections;
using System.Collections.Generic;

namespace Axon.Core.Api.Queries.Admin.App.ListQuery
{
    public class GetAdminAppListQueryRequest : IRequest<CommandResponse<IEnumerable<AppModel>>>
    {
        public ListParams ListParams { get; }

        public GetAdminAppListQueryRequest(ListParams listParams)
        {
            ListParams = listParams;
        }
    }
}
