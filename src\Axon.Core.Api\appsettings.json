{"AllowedHosts": "*", "ConnectionStrings": {"Axon-Core-ApiDb": {"EndpointUrl": "", "DatabaseName": "", "Containers": [{"Name": "Organisation", "PartitionKey": "/id"}, {"Name": "App", "PartitionKey": "/id"}, {"Name": "IdentityProvider", "PartitionKey": "/id"}, {"Name": "User", "PartitionKey": "/id"}, {"Name": "RolePermission", "PartitionKey": "/id"}, {"Name": "UserPreference", "PartitionKey": "/id"}, {"Name": "ScopeResource", "PartitionKey": "/id"}, {"Name": "AppSettings", "PartitionKey": "/id"}, {"Name": "AppOrganisationSettings", "PartitionKey": "/id"}, {"Name": "Audit", "PartitionKey": "/id"}, {"Name": "Role", "PartitionKey": "/id"}, {"Name": "RoleDefinition", "PartitionKey": "/id"}, {"Name": "Access", "PartitionKey": "/id"}, {"Name": "AppGroup", "PartitionKey": "/id"}, {"Name": "Group", "PartitionKey": "/id"}, {"Name": "MasterData", "PartitionKey": "/id"}, {"Name": "Session", "PartitionKey": "/UserId"}]}}, "MessageBus": {"RetryPolicy": {"Count": 10, "Interval": "00:00:05", "Type": "Interval", "MaxInterval": "01:00:00"}, "TransportType": "RabbitMq", "RabbitMq": {"Host": "localhost", "Port": 5672, "VirtualHost": "/", "Username": "test", "Password": "test"}, "AzureServiceBus": {"ConnectionString": "azure.blob://emu=true"}}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "ClientId": "", "TenantId": "organizations"}, "Auth": {"Audience": "Axon.Core.Api"}, "Kestrel": {"EndpointDefaults": {"Protocols": "Http1AndHttp2"}}, "Audit": {"TestOdata": {"UseTestEndpoint": false}, "OdataEndpointUrlTemplate": "https://{env}/axon-{app}-api/{tenant}/v1/odata/audits", "FilterEndpointUrlTemplate": "https://{env}/axon-{app}-api/{tenant}/v1/audits/eventConstants", "AxonCoreOdataEndpointUrlTemplate": "https://{env}/api/core/v1/odata/organisation/{tenant}/Audits"}, "AzureBlobStorage": {"Containers": {"AppAvatars": {"ContainerName": "axon-avatar", "FolderPrefix": "apps"}, "OrganisationAvatars": {"ContainerName": "axon-avatar", "FolderPrefix": "organisations"}, "Themes": {"ContainerName": "axon-theme"}}}, "Caching": {"Caches": {"Users": {"CacheItemDuration": "00:01:00"}, "IdentityProviders": {"CacheItemDuration": "00:10:00"}}}, "AxonCoreShared": {"DataProtection": {"Container": "dataprotection", "Blob": "axon-dataprotection-key-ring"}, "EnableSessionValidityChecks": true, "TokenValidation": {"UserExistsExemptRoutes": ["/v1/Authentication/SignIn"], "EnableAppPrincipleClientChecks": true}}}