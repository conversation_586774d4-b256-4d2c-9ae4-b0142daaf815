﻿using MediatR;

namespace Axon.Core.Api.Commands.AppUser.Delete
{
    public class DeleteAppUserCommand : IRequest<CommandResponse>
    {
        public DeleteAppUserCommand(string emailAddress, string appCodeName, string orgCodeName)
        {
            EmailAddress = emailAddress;
            AppCodeName = appCodeName;
            OrgCodeName = orgCodeName;
        }

        public string EmailAddress { get; }
        public string AppCodeName { get; }
        public string OrgCodeName { get; }
    }
}
