﻿using AutoMapper;
using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Contracts;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Phlex.Core.MessageBus;
using System.Threading.Tasks;
using System.Threading;
using Axon.Core.Api.Services.AppGroup;
using Axon.Core.Api.Validators.Role;
using System;
using Axon.Core.Domain.Extensions;

namespace Axon.Core.Api.Commands.AppGroup.Create
{
    internal class CreateAppGroupCommandHandler : BaseCreateCommandHandler<CreateAppGroupRequest, AppGroupEntity, AppGroupUpdatedEvent>
    {
        private readonly IRoleRepository roleRepository;
        private readonly IGroupRepository groupRepository;
        private readonly IAppGroupMemberSynchroniser appGroupMemberSynchroniser;
        private readonly IClientDetailsProvider clientDetailsProvider;
        private readonly ICorrelationIdProvider correlationIdProvider;
        private readonly IUserAndGroupAccessRoleValidator userAndGroupAccessRoleValidator;
        private readonly IAuditService<TenantAuditExtensions> auditService;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAppRepository appRepository;

        public CreateAppGroupCommandHandler(
            IRoleRepository roleRepository,
            IGroupRepository groupRepository,
            IAppGroupMemberSynchroniser appGroupMemberSynchroniser,
            IAppGroupRepository repo,
            IMapper mapper,
            IMessageBus messageBus,
            IClientDetailsProvider clientDetailsProvider,
            ICorrelationIdProvider correlationIdProvider,
            IUserAndGroupAccessRoleValidator userAndGroupAccessRoleValidator,
            IAuditService<TenantAuditExtensions> auditService,
            IOrganisationRepository organisationRepository,
            IAppRepository appRepository)
            : base(repo, mapper, messageBus)
        {

            this.roleRepository = roleRepository;
            this.groupRepository = groupRepository;
            this.appGroupMemberSynchroniser = appGroupMemberSynchroniser;
            this.clientDetailsProvider = clientDetailsProvider;
            this.correlationIdProvider = correlationIdProvider;
            this.userAndGroupAccessRoleValidator = userAndGroupAccessRoleValidator;
            this.auditService = auditService;
            this.organisationRepository = organisationRepository;
            this.appRepository = appRepository;
        }

        public override async Task<CommandResponse> Handle(CreateCommandRequest<CreateAppGroupRequest> request, CancellationToken cancellationToken)
        {
            var org = await organisationRepository.GetItemByCodeNameAsync(request.Model.OrganisationCodeName);
            if (org == default(OrganisationEntity))
            {
                return CommandResponse.Failed(nameof(request.Model.OrganisationCodeName), $"Organisation `{request.Model.OrganisationCodeName}` does not exist");
            }

            var appId = await appRepository.GetAppIdByAppCode(request.Model.AppCodeName);
            if (appId == default(string))
            {
                return CommandResponse.NotFound(nameof(AppEntity), request.Model.AppCodeName);
            }

            var appGroupRepository = (IAppGroupRepository)Repo;

            var roleExists = await roleRepository.RoleExistsForAppAsync(
                request.Model.OrganisationCodeName,
                request.Model.AppCodeName,
                request.Model.CreateAppGroupBody.RoleName,
                request.Model.CreateAppGroupBody.RoleId);

            if (!roleExists)
            {
                return CommandResponse.Failed(nameof(request.Model.CreateAppGroupBody.RoleId), $"Role `{request.Model.CreateAppGroupBody.RoleName}` with id `{request.Model.CreateAppGroupBody.RoleId}` does not exist within `{request.Model.AppCodeName}` app and `{request.Model.OrganisationCodeName}` organisation");
            }

            var groupExists = await groupRepository.GroupExistsAsync(request.Model.OrganisationCodeName, request.Model.CreateAppGroupBody.GroupId, request.Model.CreateAppGroupBody.GroupName);
            if (!groupExists)
            {
                return CommandResponse.Failed(nameof(request.Model.CreateAppGroupBody.GroupId), $"Group `{request.Model.CreateAppGroupBody.GroupName}` with id `{request.Model.CreateAppGroupBody.GroupId}` does not exist within `{request.Model.OrganisationCodeName}` organisation");
            }

            var existingAppGroup = await appGroupRepository.AppGroupExistsAsync(request.Model.OrganisationCodeName, request.Model.AppCodeName, request.Model.CreateAppGroupBody.GroupId);
            if (existingAppGroup)
            {
                return CommandResponse.Failed(nameof(request), $"App group with group id `{request.Model.CreateAppGroupBody.GroupId}` already exists");
            }
            var roleValidation = await userAndGroupAccessRoleValidator.Validate(request.Model.CreateAppGroupBody.RoleId, request.Model.AppCodeName, request.Model.OrganisationCodeName, request.Model.CreateAppGroupBody.Scopes);
            if (!roleValidation.valid)
            {
                return CommandResponse.BadRequest(nameof(AppGroup), roleValidation.message);
            }

            var entity = new AppGroupEntity();
            var correlationId = correlationIdProvider.Provide();
            var clientDetails = clientDetailsProvider.Provide();

            var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.AppGroup, AuditEventDescriptions.AppGroupCreated, correlationId, clientDetails, request.Model.OrganisationCodeName);

            await auditService.LogAsync(AuditEventTypes.AppGroupCreated, tenantAuditExtensions, entity,
                async () =>
                {
                    Mapper.Map(request.Model, entity);

                    await Repo.AddItemAsync(entity);
                });

            await appGroupMemberSynchroniser.SynchroniseNewGroupRoleWithUsers(entity, correlationId, clientDetails);

            AppGroupUpdatedEvent appGroupEvent = new AppGroupUpdatedEvent
            {
                OrgId = org.Id,
                OrgCodeName = org.CodeName,
                AppCodeName = entity.AppCodeName,
                AppId = appId,
                GroupId = entity.GroupId,
                Action = EventActionType.Created
            };

            await MessageBus.PublishAsync(appGroupEvent,cancellationToken: cancellationToken);

            return CommandResponse.Created(nameof(AppGroupEntity), entity.Id);
        }
    }
}
