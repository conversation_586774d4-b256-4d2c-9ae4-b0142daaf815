﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Audit;
using Axon.Core.Api.Services.Audit;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using JetBrains.Annotations;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.Audit
{
    [UsedImplicitly]
    internal class GetAuditFiltersQueryRequestHandler : IRequestHandler<GetAuditFiltersQueryRequest, CommandResponse<AuditFilters>>
    {
        public IAppRepository appRepository;
        public IAuditEndpointService auditODataEndpointService;
        private readonly ILogger<GetAuditFiltersQueryRequestHandler> logger;

        //For now as there are so few this is a hard coded list, in future if there are many then querying these out of the db might make more sense
        private static readonly AuditFilters axonCoreAuditFilters = new AuditFilters
        {
            {
                "eventTypes",
                new List<string>
                {
                    AuditEventTypes.UserLogin,
                    AuditEventTypes.UserPrincipalLogin,
                    AuditEventTypes.UserLogout,
                    AuditEventTypes.UserAutoLogout,
                    AuditEventTypes.UserCreated,
                    AuditEventTypes.UserUpdated,
                    AuditEventTypes.UserAssigned,
                    AuditEventTypes.UserUnassigned,
                    AuditEventTypes.OrganisationCreated,
                    AuditEventTypes.OrganisationUpdated,
                    AuditEventTypes.RoleCreated,
                    AuditEventTypes.RoleUpdated,
                    AuditEventTypes.RoleDeleted,
                    AuditEventTypes.RoleDefinitionUpdated,
                    AuditEventTypes.GroupCreated,
                    AuditEventTypes.GroupUpdated,
                    AuditEventTypes.GroupDeleted,
                    AuditEventTypes.AppGroupCreated,
                    AuditEventTypes.AppGroupUpdated,
                    AuditEventTypes.AppGroupDeleted,
                    AuditEventTypes.AccessCreated,
                    AuditEventTypes.AccessUpdated,
                    AuditEventTypes.AccessDeleted,
                }
            },
            {
                "eventCategories",
                new List<string>
                {
                    AuditEventCategories.User,
                    AuditEventCategories.UserPrinciple,
                    AuditEventCategories.Organisation,
                    AuditEventCategories.Role,
                    AuditEventCategories.Group,
                    AuditEventCategories.AppGroup,
                    AuditEventCategories.Access
                }
            }
        };

        public GetAuditFiltersQueryRequestHandler(IAppRepository appRepository,
            IAuditEndpointService auditODataEndpointService,
            ILogger<GetAuditFiltersQueryRequestHandler> logger)
        {
            Guard.IsNotNull(appRepository);
            this.appRepository = appRepository;
            Guard.IsNotNull(auditODataEndpointService);
            this.auditODataEndpointService = auditODataEndpointService;
            Guard.IsNotNull(logger);
            this.logger = logger;
        }

        public async Task<CommandResponse<AuditFilters>> Handle(GetAuditFiltersQueryRequest request, CancellationToken cancellationToken)
        {
            if (request.AppCodeName == AppNameConstants.AxonCoreCodeName)
            {
                return CommandResponse<AuditFilters>.Data(axonCoreAuditFilters);
            }

            var appEntity = await appRepository.GetItemByAppCodeNameAsync(request.AppCodeName);

            if (!appEntity.ShowAudits)
            {
                logger.LogError("Auditing is not enabled for this application: {AppCodeName}.", request.AppCodeName);
                return new CommandResponse<AuditFilters>() { status = HttpStatusCode.Forbidden, title = "Not available on this application" };
            }

            return await auditODataEndpointService.GetFilters(request);
        }
    }
}
