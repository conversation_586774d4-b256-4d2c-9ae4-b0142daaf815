﻿using System;
using System.Collections.Generic;
using System.IO;
using Axon.Core.Api.Commands;
using SkiaSharp;

namespace Axon.Core.Api.Services.FileUpload
{
    public class ImageResizer : IImageResizer
    {
        private static readonly Dictionary<string, SKEncodedImageFormat> ImageFormatMapping = new(StringComparer.InvariantCultureIgnoreCase)
        {
            {".png", SKEncodedImageFormat.Png },
            {".jpg", SKEncodedImageFormat.Jpeg },
            {".jpeg", SKEncodedImageFormat.Jpeg }
        };

        public (Stream resizedImageStream, int? width, int? height) Resize(Stream imageStream, string fileName, EntityImageRequirements imageRequirements)
        {
            if (imageStream != null && imageRequirements.TargetWidthInPixels.HasValue && imageRequirements.TargetHeightInPixels.HasValue)
            {
                imageStream.Seek(0, SeekOrigin.Begin);
                using (var skImage = SKBitmap.Decode(imageStream))
                {
                    using (var scaledBitmap = skImage.Resize(new SKImageInfo(imageRequirements.TargetWidthInPixels.Value, imageRequirements.TargetHeightInPixels.Value), SKFilterQuality.High))
                    {
                        using (var image = SKImage.FromBitmap(scaledBitmap))
                        {
                            var extension = Path.GetExtension(fileName);
                            var format = ImageFormatMapping.TryGetValue(extension, out var imageFormat) ? imageFormat : SKEncodedImageFormat.Png;

                            using (var encodedImage = image.Encode(format, 90))
                            {
                                var stream = new MemoryStream();
                                encodedImage.SaveTo(stream);
                                stream.Seek(0, SeekOrigin.Begin);
                                return (stream, image.Width, image.Height);
                            }
                        }
                    }
                }
            }

            return (imageStream, null, null);
        }
    }
}
