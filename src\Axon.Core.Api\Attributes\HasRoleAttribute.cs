﻿using Axon.Core.Domain.Interfaces.Access;
using CommunityToolkit.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Infrastructure.Auth;
using System.Collections.Generic;
using System.Security.Claims;

namespace Axon.Core.Api.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
    public class HasRoleAttribute : HasAuthorizationBaseAttribute
    {
        private readonly string orgPathKey;
        public HasRoleAttribute(string orgCodeNameUrlIdentifier = "orgCodeName")
        {
            this.orgPathKey = orgCodeNameUrlIdentifier;
        }
        protected override async Task Authorise(AuthorizationFilterContext context)
        {
            (var _, var accessGate, var authenticationSchemeProvider, var logger) = GetStandardServices<HasPermissionsAttribute>(context);
            (var validatedClaims, var errorResponse) = TryGetStandardClaims(context, logger);

            var orgCodeName = context.RouteData.Values[orgPathKey] as string;

            if (string.IsNullOrEmpty(orgCodeName))
            {
                logger.LogDebug("Auth failed: cannot find the organisation code in the route");
                context.Result = new UnauthorizedResult();
                return;
            }

            if (validatedClaims == null)
            {
                context.Result = errorResponse;
                return;
            }

            if (!await accessGate.IsAuthorisedByRoles(ClaimsExtensions.AxonCoreAppName, orgCodeName))
            {
                logger.LogDebug("Auth failed: For user with sub/nameidentifier `{OidClaim}`, User doesn't have any role for {AppName}", validatedClaims.OidClaim, ClaimsExtensions.AxonCoreAppName);
                context.Result = new ForbidResult(authenticationSchemeProvider.AllSchemes());
            }
        }
    }
}
