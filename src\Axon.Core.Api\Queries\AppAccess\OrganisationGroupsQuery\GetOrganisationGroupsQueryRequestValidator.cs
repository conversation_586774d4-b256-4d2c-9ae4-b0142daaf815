﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.AppAccess.OrganisationGroupsQuery;

[UsedImplicitly]
public class GetOrganisationGroupsQueryRequestValidator : AbstractValidator<GetOrganisationGroupsQueryRequest>
{
    public GetOrganisationGroupsQueryRequestValidator()
    {
        RuleFor(x => x.OrgCodeName)
            .MustBeAValidCodeName();
    }
}