api:
  tag: alpha-dev

grpc:
  tag: alpha-dev

ingress:
  tls:
    - tlsSecretName: ********************************
      hosts:
        - app-alpha-dev.smartphlex.com
  hosts:
    - host: app-alpha-dev.smartphlex.com
      paths:
        - path: /api/core/(.*)
          pathType: ImplementationSpecific

newrelic_api_app_name: axon-core-alpha-api
newrelic_grpc_app_name: axon-core-alpha-grpc
azureIssuer: https://login.microsoftonline.com/common/v2.0
azureUseCustomRefresh: false

keyVaultName: axn-dev-kv-eun
gigyaClientId: cUDSdI53tU5LgmVJH2AkCH-8
gigyaIssuer: https://tst.aaas.cencora.com/oidc/op/v1.0/4_Pv18t6XTOc51PxyYytQzHA/authorize
gigyaUseCustomRefresh: true


corsOriginUrl0: https://app-alpha-dev.smartphlex.com
corsOriginUrl1: https://localhost:4000

BlobStorageConnectionString: https://phcgvsharedstaticeun.blob.core.windows.net/

NamespaceName: "axn-dev-servicebus-eun"

managedIdentityClientId: 52860ed6-50ea-4eaf-bd3e-3f96d4b963a5

cosmosdbName: Axon-Core-ApiDb-alpha
cosmosdbUrl: 'https://axn-dev-cosmos-eun.documents.azure.com:443/'

clientId: 167cd45b-7d4f-4b3d-8c05-a87f12c40609

azureWorkload:
  clientId: 52860ed6-50ea-4eaf-bd3e-3f96d4b963a5
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AzureBlobStorageContainersAppAvatarsFolderPrefix: dev/axon-avatar
AzureBlobStorageContainersOrganisationAvatarsFolderPrefix: dev/organisations
AzureBlobStorageContainersThemesFolderPrefix: dev/axon-theme

DataProtectionBlobStorageUri: 'https://axndevstorageeun.blob.core.windows.net/'
DataProtectionKeyVaultKey: 'https://axn-dev-kv-eun.vault.azure.net/keys/AxonDataProtection'

GoodDataBaseUri: 'https://phlexglobal-dev.cloud.gooddata.com/'
GoodDataEnvironment: 'dev'