﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.AddOrganisationGroupUser;

[UsedImplicitly]
public class AddOrganisationGroupUserCommandRequestValidator : AbstractValidator<AddOrganisationGroupUserCommandRequest>
{
    public AddOrganisationGroupUserCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.GroupId)
            .MustBeAValidGuid();
    }
}