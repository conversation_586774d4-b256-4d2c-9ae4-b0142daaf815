/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// SetupThemeBody
    /// </summary>
    [DataContract(Name = "SetupThemeBody")]
    public partial class SetupThemeBody : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SetupThemeBody" /> class.
        /// </summary>
        /// <param name="defaultColours">defaultColours.</param>
        /// <param name="darkColours">darkColours.</param>
        public SetupThemeBody(OrgColour defaultColours = default(OrgColour), OrgColour darkColours = default(OrgColour))
        {
            this.DefaultColours = defaultColours;
            this.DarkColours = darkColours;
        }

        /// <summary>
        /// Gets or Sets DefaultColours
        /// </summary>
        [DataMember(Name = "defaultColours", EmitDefaultValue = false)]
        public OrgColour DefaultColours { get; set; }

        /// <summary>
        /// Gets or Sets DarkColours
        /// </summary>
        [DataMember(Name = "darkColours", EmitDefaultValue = false)]
        public OrgColour DarkColours { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class SetupThemeBody {\n");
            sb.Append("  DefaultColours: ").Append(DefaultColours).Append("\n");
            sb.Append("  DarkColours: ").Append(DarkColours).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
