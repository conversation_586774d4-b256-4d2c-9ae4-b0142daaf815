﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Role;
using Axon.Core.Shared.Api;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.Role.ListQuery
{
    public class GetFilteredRoleListQueryRequest : IRequest<CommandResponse<ApiPagedListResult<RoleModel>>>
    {
        public string OrganisationCodeName { get; }
        public string AppCodeName { get; }
        public ListParams ListParams { get; }

        public GetFilteredRoleListQueryRequest(string organisationCodeName, string appCodeName, ListParams listParams)
        {
            OrganisationCodeName = organisationCodeName;
            AppCodeName = appCodeName;
            ListParams = listParams;
        }
    }
}
