﻿using Autofac;
using Axon.Core.Api.Validators.AppOrganisationSettings;
using Axon.Core.Domain.Enums;
using JetBrains.Annotations;
using Microsoft.Extensions.Configuration;
using System.ComponentModel;
using Axon.Core.Api.Mappers.AppOrganisationSettings;
using Axon.Core.Api.Services.Settings;
using Axon.Core.Api.Validators.ManageOrganisationUser;
using Axon.Core.Api.Services.UserOrganisation;
using Axon.Core.Api.Queries.AppAccess.OrganisationUserQuery;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.App;
using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Api.Models.Role;
using Axon.Core.Api.Queries;
using Axon.Core.Api.Queries.Organisation.CodeNameQuery;
using FluentValidation;
using Axon.Core.Api.Services.AppGroup;

namespace Axon.Core.Api.Infrastructure.IoC
{
    public class ApiModule : Module
    {
        public ApiModule([UsedImplicitly] IConfiguration configuration)
        {
        }

        protected override void Load(ContainerBuilder builder)
        {
            RegisterSettingsDependencies(builder);
            RegisterRequestDependencies(builder);
            RegisterValidationDependencies(builder);
        }

        private static void RegisterSettingsDependencies(ContainerBuilder builder)
        {
            builder.RegisterType<AppOrganisationSettingsValidatorFactory>().As<IAppOrganisationSettingsValidatorFactory>();
            builder.Register<ISettingValidator>((cc, parameter) =>
            {
                var type = parameter.TypedAs<SettingDataType>();
                return type switch
                {
                    SettingDataType.Boolean => new BooleanValidator(cc.Resolve<IAppOrganisationSettingsMapperFactory>()),
                    SettingDataType.Database => new DatabaseValidator(cc.Resolve<IAppOrganisationSettingsMapperFactory>()),
                    SettingDataType.Number => new NumberValidator(cc.Resolve<IAppOrganisationSettingsMapperFactory>()),
                    SettingDataType.Password => new TextValidator(),
                    SettingDataType.Text => new TextValidator(),
                    _ => throw new InvalidEnumArgumentException("Invalid SettingDataType enum argument.")
                };
            }).As<ISettingValidator>();

            builder.RegisterType<AppOrganisationSettingsMapperFactory>().As<IAppOrganisationSettingsMapperFactory>();
            builder.RegisterType<SettingsProvider>().As<ISettingsProvider>();
            
            builder.Register<ISettingMapper>((_, parameter) =>
            {
                var type = parameter.TypedAs<SettingDataType>();
                return type switch
                {
                    SettingDataType.Boolean => new BooleanSettingMapper(),
                    SettingDataType.Database => new DatabaseSettingMapper(),
                    SettingDataType.Number => new NumberSettingMapper(),
                    SettingDataType.Password => new TextSettingMapper(),
                    SettingDataType.Text => new TextSettingMapper(),
                    _ => throw new InvalidEnumArgumentException("Invalid SettingDataType enum argument.")
                };

            }).As<ISettingMapper>();
        }

        private static void RegisterRequestDependencies(ContainerBuilder builder)
        {
            builder.RegisterType<ManageOrganisationUserValidator>().As<IManageOrganisationUserValidator>();
            builder.RegisterType<OrganisationUserManager>().As<IOrganisationUserManager>();
            builder.RegisterType<OrganisationUserGroupAccessProvider>().As<IOrganisationUserGroupAccessProvider>();
            builder.RegisterType<OrganisationUserProvider>().As<IOrganisationUserProvider>();
            builder.RegisterType<AppGroupManager>().As<IAppGroupManager>();
        }

        private static void RegisterValidationDependencies(ContainerBuilder builder)
        {
            builder.RegisterType<IdQueryRequestValidator<AppModel>>().As<IValidator<IdQueryRequest<AppModel>>>();
            builder.RegisterType<IdQueryRequestValidator<OrganisationModel>>().As<IValidator<IdQueryRequest<OrganisationModel>>>();
            builder.RegisterType<GetOrganizationByCodeNameQueryRequestValidator>().As<IValidator<GetOrganisationByCodeNameRequest>>();
            builder.RegisterType<DeleteCommandRequestValidator<AppGroupModel>>().As<IValidator<DeleteCommandRequest<AppGroupModel>>>();
            builder.RegisterType<DeleteCommandRequestValidator<AppModel>>().As<IValidator<DeleteCommandRequest<AppModel>>>();
            builder.RegisterType<DeleteCommandRequestValidator<OrganisationModel>>().As<IValidator<DeleteCommandRequest<OrganisationModel>>>();
            builder.RegisterType<DeleteCommandRequestValidator<RoleModel>>().As<IValidator<DeleteCommandRequest<RoleModel>>>();
        }
    }
}