/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// UnassignedGroupModel
    /// </summary>
    [DataContract(Name = "UnassignedGroupModel")]
    public partial class UnassignedGroupModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UnassignedGroupModel" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected UnassignedGroupModel() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="UnassignedGroupModel" /> class.
        /// </summary>
        /// <param name="id">id (required).</param>
        /// <param name="organisationCodeName">organisationCodeName (required).</param>
        /// <param name="groupName">groupName (required).</param>
        public UnassignedGroupModel(string id = default(string), string organisationCodeName = default(string), string groupName = default(string))
        {
            // to ensure "id" is required (not null)
            if (id == null)
            {
                throw new ArgumentNullException("id is a required property for UnassignedGroupModel and cannot be null");
            }
            this.Id = id;
            // to ensure "organisationCodeName" is required (not null)
            if (organisationCodeName == null)
            {
                throw new ArgumentNullException("organisationCodeName is a required property for UnassignedGroupModel and cannot be null");
            }
            this.OrganisationCodeName = organisationCodeName;
            // to ensure "groupName" is required (not null)
            if (groupName == null)
            {
                throw new ArgumentNullException("groupName is a required property for UnassignedGroupModel and cannot be null");
            }
            this.GroupName = groupName;
        }

        /// <summary>
        /// Gets or Sets Id
        /// </summary>
        [DataMember(Name = "id", IsRequired = true, EmitDefaultValue = true)]
        public string Id { get; set; }

        /// <summary>
        /// Gets or Sets OrganisationCodeName
        /// </summary>
        [DataMember(Name = "organisationCodeName", IsRequired = true, EmitDefaultValue = true)]
        public string OrganisationCodeName { get; set; }

        /// <summary>
        /// Gets or Sets GroupName
        /// </summary>
        [DataMember(Name = "groupName", IsRequired = true, EmitDefaultValue = true)]
        public string GroupName { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class UnassignedGroupModel {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  OrganisationCodeName: ").Append(OrganisationCodeName).Append("\n");
            sb.Append("  GroupName: ").Append(GroupName).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            // Id (string) minLength
            if (this.Id != null && this.Id.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for Id, length must be greater than 1.", new [] { "Id" });
            }

            // OrganisationCodeName (string) minLength
            if (this.OrganisationCodeName != null && this.OrganisationCodeName.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for OrganisationCodeName, length must be greater than 1.", new [] { "OrganisationCodeName" });
            }

            // GroupName (string) minLength
            if (this.GroupName != null && this.GroupName.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for GroupName, length must be greater than 1.", new [] { "GroupName" });
            }

            yield break;
        }
    }

}
