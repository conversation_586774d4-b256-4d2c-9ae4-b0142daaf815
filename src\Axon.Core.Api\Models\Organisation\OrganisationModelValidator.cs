﻿using Axon.Core.Api.Validators;
using Axon.Core.Domain.Services.Access;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Models.Organisation;

[UsedImplicitly]
public class OrganisationModelValidator : AbstractValidator<OrganisationModel>
{
    public OrganisationModelValidator()
    {
        RuleFor(x => x.Id)
            .MustBeAValidGuid();
        RuleFor(x => x.DisplayName)
            .MustBeAValidDisplayName();
        RuleFor(x => x.Description)
            .MustBeAValidDescription();
        RuleFor(x => x.Icon)
            .NotEmpty().WithMessage("Icon cannot be empty.")
            .MaximumLength(500).WithMessage("Icon cannot be longer than 500 characters.");
        RuleFor(x => x.CodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.AccessLevel)
            .NotEmpty().WithMessage("{PropertyName} cannot be empty.")
            .MustBeAValidEnum(typeof(AccessLevel));
        RuleForEach(x => x.AllowedRoles)
            .MustBeAValidRoleName();
    }
}

[UsedImplicitly]
public class AppConfigModelValidator : AbstractValidator<AppConfigModel>
{
    public AppConfigModelValidator()
    {
        RuleFor(x => x.AppId)
            .MustBeAValidGuid();
    }
}