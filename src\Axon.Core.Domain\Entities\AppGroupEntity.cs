﻿using Axon.Core.Domain.Entities.Base;
using System;

namespace Axon.Core.Domain.Entities
{
    public sealed class AppGroupEntity : BaseEntity
    {
        public string AppCodeName { get; set; }
        public string OrganisationCodeName { get; set; }
        public string GroupId { get; set; }
        public string GroupName { get; set; }
        public string RoleId { get; set; }
        public string Role { get; set; }
        public RoleEntity.ScopeResources[] Scopes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastUpdatedDate { get; set; }
    }
}
