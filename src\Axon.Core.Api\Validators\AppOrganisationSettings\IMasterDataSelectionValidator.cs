﻿using Axon.Core.Domain.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Axon.Core.Domain.Entities.AppOrganisationSettingsEntity;

namespace Axon.Core.Api.Validators.AppOrganisationSettings
{
    public interface IMasterDataSelectionValidator
    {
        public Task<(bool isValid, string errorMessage)> Validate(string masterDataType,
                                                             IReadOnlyCollection<AppOrganisationSettingsEntity.AppOrganisationSelectedMasterData> selectedMasterData);


        Task<(bool isValid, string errorMessage)> ValidateUpdate(string appCodeName,
                                                                 string orgCodeName,
                                                                 string masterDataType,
                                                                 IReadOnlyCollection<AppOrganisationSelectedMasterData> currentSelectedMasterData,
                                                                 IReadOnlyCollection<AppOrganisationSelectedMasterData> newSelectedMasterData);
    }
}
