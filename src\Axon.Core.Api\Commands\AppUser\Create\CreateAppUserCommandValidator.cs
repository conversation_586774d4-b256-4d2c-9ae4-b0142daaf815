﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.AppUser.Create
{
    [UsedImplicitly]
    public class CreateAppUserCommandValidator : AbstractValidator<CreateAppUserCommand>
    {
        public CreateAppUserCommandValidator()
        {
            RuleFor(x => x.OrgCodeName)
                .MustBeAValidCodeName();
            RuleFor(x => x.AppCodeName)
                .MustBeAValidCodeName();
            RuleFor(x => x.EmailAddress)
                .MustBeAValidEmail();
            RuleFor(x => x.RoleId)
                .MustBeAValidGuid();
            RuleFor(x => x.Scopes)
                .Must(x => x == null || x.Count == 1)
                .WithMessage("Scopes can either be null, or contain one singular scope");
        }
    }
}
