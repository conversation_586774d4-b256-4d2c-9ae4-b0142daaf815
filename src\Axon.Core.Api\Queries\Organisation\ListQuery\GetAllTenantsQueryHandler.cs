﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Extensions;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;
using MediatR;

namespace Axon.Core.Api.Queries.Organisation.ListQuery
{
    [UsedImplicitly]
    internal class GetAllTenantsQueryHandler : IRequestHandler<GetAllTenantsQueryRequest, CommandResponse<IEnumerable<string>>>
    {
        private readonly IAccessService accessService;
        private readonly IUserRequestContext userRequestContext;
        private readonly IOrganisationRepository organistionRepository;

        public GetAllTenantsQueryHandler(IAccessService accessService, IUserRequestContext userRequestContext, IOrganisationRepository organistionRepository)
        {
            this.accessService = accessService;
            this.userRequestContext = userRequestContext;
            this.organistionRepository = organistionRepository;
        }

        public async Task<CommandResponse<IEnumerable<string>>> Handle(GetAllTenantsQueryRequest request, CancellationToken cancellationToken)
        {
            //Applications are able to see all tenants
            if (userRequestContext.IsApplication())
            {
                return CommandResponse<IEnumerable<string>>.Data(organistionRepository.GetAllLinqQueryable().Select(x => x.CodeName));
            }

            var tenants = await accessService.GetUsersAccessibleOrganisationsAndEffectivePermissions(userRequestContext.GetEmailAddress(), AppNameConstants.AxonCoreCodeName);
            var codeNames = tenants.Where(x=> x.EffectivePermissions.EffectivePermissions.HasPermission(nameof(CorePermissions.ViewOrganisation)))
                                   .Select(o => o.Organistaion.CodeName);

            return CommandResponse<IEnumerable<string>>.Data(codeNames);
        }
    }
}