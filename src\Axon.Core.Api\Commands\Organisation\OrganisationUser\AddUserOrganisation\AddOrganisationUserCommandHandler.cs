﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Services.UserOrganisation;
using Axon.Core.Api.Validators.ManageOrganisationUser;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using CommunityToolkit.Diagnostics;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser.AddUserOrganisation;

internal class AddOrganisationUserCommandHandler : BaseCommandHandler<AccessEntity, AddOrganisationUserCommandRequest, CommandResponse>
{
    private readonly IOrganisationRepository organisationRepository;
    private readonly IUserRepository userRepository;
    private readonly IClientDetailsProvider clientDetailsProvider;
    private readonly ICorrelationIdProvider correlationIdProvider;
    private readonly IManageOrganisationUserValidator manageOrganisationUserValidator;
    private readonly IOrganisationUserManager organisationUserManager;
    private readonly ILogger<AddOrganisationUserCommandHandler> logger;

    public AddOrganisationUserCommandHandler(IAccessRepository accessRepository,
        IMapper mapper,
        IMessageBus messageBus,
        IOrganisationRepository organisationRepository,
        IClientDetailsProvider clientDetailsProvider,
        ICorrelationIdProvider correlationIdProvider,
        IUserRepository userRepository,
        IManageOrganisationUserValidator manageOrganisationUserValidator,
        IOrganisationUserManager organisationUserManager,
        ILogger<AddOrganisationUserCommandHandler> logger)
        : base(accessRepository, mapper, messageBus)
    {
        Guard.IsNotNull(manageOrganisationUserValidator);
        this.manageOrganisationUserValidator = manageOrganisationUserValidator;
        Guard.IsNotNull(clientDetailsProvider);
        this.clientDetailsProvider = clientDetailsProvider;
        Guard.IsNotNull(correlationIdProvider);
        this.correlationIdProvider = correlationIdProvider;
        Guard.IsNotNull(userRepository);
        this.userRepository = userRepository;
        Guard.IsNotNull(organisationRepository);
        this.organisationRepository = organisationRepository;
        Guard.IsNotNull(organisationUserManager);
        this.organisationUserManager = organisationUserManager;
        Guard.IsNotNull(logger);
        this.logger = logger;
    }

    public override async Task<CommandResponse> Handle(AddOrganisationUserCommandRequest request, CancellationToken cancellationToken)
    {
        var validateResponse = await Validate(request);
        if (validateResponse != null)
        {
            return validateResponse;
        }

        var correlationId = correlationIdProvider.Provide();
        var clientDetails = clientDetailsProvider.Provide();

        var user = await userRepository.GetUserByEmailAsync(request.AddOrganisationUserCommandRequestBody.Email);
        var organisationEntity = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);
        var isNew = false;

        if (user == null)
        {
            isNew = true;
            logger.LogInformation("User {userEmail} does not exist, creating.", request.AddOrganisationUserCommandRequestBody.Email);

            var userStatus = Enum.Parse<UserStatus>(request.AddOrganisationUserCommandRequestBody.Status, true);
            (user, _, _) = await organisationUserManager.CreateOrUpdateUser(request.AddOrganisationUserCommandRequestBody.Email, request.AddOrganisationUserCommandRequestBody.Name, userStatus,
                organisationEntity.Id, organisationEntity.CodeName, organisationEntity.IdentityProviderId, correlationId, clientDetails);
        }
        else if (string.IsNullOrWhiteSpace(user.OwnerOrganisationId))
        {
            logger.LogInformation("User {userEmail} does not have an owner organisation, setting to {ownerOrganisationId}.", request.AddOrganisationUserCommandRequestBody.Email,
                organisationEntity.Id);
            user.OwnerOrganisationId = organisationEntity.Id;
            await userRepository.UpdateItemAsync(user.Id, user);
        }

        await organisationUserManager.AssignUser(user, request.AddOrganisationUserCommandRequestBody.Groups, correlationId, clientDetails, organisationEntity);

        foreach (var childOrganisation in request.AddOrganisationUserCommandRequestBody.ChildOrganisations ?? [])
        {
            var childOrganisationEntity = await organisationRepository.GetItemByCodeNameAsync(childOrganisation.OrganisationCodeName);
            await organisationUserManager.AssignUser(user, childOrganisation.Groups, correlationId, clientDetails, childOrganisationEntity);
        }

        var childOrganisationCodeNames = (request.AddOrganisationUserCommandRequestBody.ChildOrganisations ?? [])
            .Select(groupOrganisation => groupOrganisation.OrganisationCodeName)
            .ToArray();

        await organisationUserManager.UnlinkUserFromUnspecifiedChildOrganisations(user, correlationId, clientDetails, organisationEntity, childOrganisationCodeNames);

        return isNew ? CommandResponse.Created(nameof(UserEntity), user.Id) : CommandResponse.Success();
    }

    private async Task<CommandResponse> Validate(AddOrganisationUserCommandRequest request)
    {
        var organisationValidationResponse = await manageOrganisationUserValidator.ValidateOrganisation(request.OrganisationCodeName);
        if (organisationValidationResponse != null)
        {
            return organisationValidationResponse;
        }

        var (groupValidationResponse, _) = await manageOrganisationUserValidator.ValidateGroups(request.AddOrganisationUserCommandRequestBody.Groups, request.OrganisationCodeName);
        if (groupValidationResponse != null)
        {
            return groupValidationResponse;
        }

        var (childOrganisationValidationResponse, _) = await manageOrganisationUserValidator.ValidateChildOrganisations(request.AddOrganisationUserCommandRequestBody.ChildOrganisations, request.OrganisationCodeName);
        if (childOrganisationValidationResponse != null)
        {
            return childOrganisationValidationResponse;
        }

        var userValidationResponse = await manageOrganisationUserValidator.ValidateUser(request.AddOrganisationUserCommandRequestBody.Email, request.OrganisationCodeName);
        return userValidationResponse;
    }
}