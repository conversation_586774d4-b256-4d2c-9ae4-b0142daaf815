﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppSetting;
using Axon.Core.Api.Services.Settings;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services.KeyVault;
using MediatR;

namespace Axon.Core.Api.Queries.AppOrganisationSettings;

internal class GetOrganisationAppSettingsQueryHandler : IRequestHandler<GetOrganisationAppSettingsQueryRequest, CommandResponse<SettingsValueModel[]>>
{
    private readonly IAppOrganisationSettingsRepository appOrganisationSettingsRepository;
    private readonly IAppSettingsRepository appSettingsRepository;
    private readonly ISecretClientProvider secretClientProvider;
    private readonly ISettingsProvider settingsProvider;

    public GetOrganisationAppSettingsQueryHandler(
        IAppSettingsRepository appSettingsRepository,
        IAppOrganisationSettingsRepository appOrganisationSettingsRepository,
        ISecretClientProvider secretClientProvider,
        ISettingsProvider settingsProvider)
    {
        this.secretClientProvider = secretClientProvider;
        this.appSettingsRepository = appSettingsRepository;
        this.appOrganisationSettingsRepository = appOrganisationSettingsRepository;
        this.settingsProvider = settingsProvider;
    }

    public async Task<CommandResponse<SettingsValueModel[]>> Handle(GetOrganisationAppSettingsQueryRequest request, CancellationToken cancellationToken)
    {
        var appSettings = await appSettingsRepository.GetByAppCodeNameAsync(request.AppCodeName);
        if (appSettings == null)
            return CommandResponse<SettingsValueModel[]>.Data(Array.Empty<SettingsValueModel>());

        var appSettingsValues = await appOrganisationSettingsRepository.GetAppOrganisationSettingsAsync(request.OrgCodeName, request.AppCodeName);

        var isSecretProviderNeeded = appSettings.Settings.Any(x => x.Value.IsKeyVault);
        var secretClient = isSecretProviderNeeded ? secretClientProvider.GetAppSecretClient(appSettings.KeyVault) : null;

        var result = appSettings.Settings
            .Select(x => settingsProvider.GetSetting(x, appSettingsValues, secretClient, request.OrgCodeName))
            .ToArray();

        return CommandResponse<SettingsValueModel[]>.Data(result);
    }
}