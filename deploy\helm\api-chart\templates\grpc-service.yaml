apiVersion: v1
kind: Service
metadata:
  name: {{ include "axon-core-api.fullname" . }}-grpc
  labels:
    {{- include "axon-core-api.labels" . | nindent 4 }}
  annotations:
    checkov.io/skip1: CKV_K8S_21=The namespace is being defined as helm argument
spec:
  ports:
    - name: grpc
      port: 9090
      targetPort: grpc
  selector:
    {{- include "axon-core-grpc.selectorLabels" . | nindent 8 }}
