﻿using System.Threading.Tasks;
using System.Threading;
using System.IO;
using Axon.Core.Api.Commands;

namespace Axon.Core.Api.Validators.FileUploads
{
    public interface IUpdateImageValidator
    {
        Task<(bool isUploadedFileValid, string newFileName, Stream fileStream, string errorMessage)> Validate(
            EntityImageRequirements imageRequirements,
            CancellationToken cancellationToken);
    }
}
