﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.AddOrganisationGroup;

[UsedImplicitly]
public class AddOrganisationGroupCommandRequestBodyValidator : AbstractValidator<AddOrganisationGroupCommandRequestBody>
{
    public AddOrganisationGroupCommandRequestBodyValidator()
    {
        RuleFor(x => x.Name)
            .MustBeAValidGroupName()
            .NotEmpty();
    }
}