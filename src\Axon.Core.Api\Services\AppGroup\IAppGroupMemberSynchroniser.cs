﻿using Axon.Core.Domain.Entities;
using Axon.Core.Shared.Audit;
using System;
using System.Threading.Tasks;

namespace Axon.Core.Api.Services.AppGroup
{
    internal interface IAppGroupMemberSynchroniser
    {
        Task SynchroniseNewGroupRoleWithUsers(AppGroupEntity appGroupEntity, Guid correlationId, ClientDetails clientDetails);
        Task SynchroniseUpdatedGroupRoleWithUsers(AppGroupEntity appGroupEntity, Guid correlationId, ClientDetails clientDetails);
        Task SynchroniseNewGroupMemberWithAppGroups(string groupId,
                                                    UserEntity user,
                                                    string organisationId,
                                                    string organisationCodeName,
                                                    Guid correlationId,
                                                    ClientDetails clientDetails);
    }
}