using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Extensions;
using Axon.Core.Domain.Entities.Base;
using Axon.Core.Domain.Interfaces.Persistence;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands
{
#pragma warning disable S2436 // Generics enable reuse across entities of standard CRUD type features from the API down to the Repository
    internal abstract class BaseUpdateCommandHandler<TBody, TEntity, TMessage> : BaseCommandHandler<TEntity, UpdateCommandRequest<TBody>, CommandResponse>
#pragma warning restore S2436 // Types and methods should not have too many generic parameters
        where TEntity : BaseEntity
        where TMessage : class
    {

        protected BaseUpdateCommandHandler(IRepository<TEntity> repo, IMapper mapper, IMessageBus messageBus)
            : base(repo, mapper, messageBus)
        { }

        public override async Task<CommandResponse> Handle(UpdateCommandRequest<TBody> request, CancellationToken cancellationToken)
        {
            if (!await Repo.TryGetItemAsync(request.Id, out var entity))
            {
                return CommandResponse.NotFound(typeof(TEntity).Name, request.Id);
            }

            (var processSuccessful, var response) = await ProcessRequest(request, entity, cancellationToken);
            if (!processSuccessful)
            {
                return response;
            }

            await Repo.UpdateItemAsync(request.Id, entity);

            await SendMessage(entity.Id, entity);

            return CommandResponse.Success();
        }

        /// <summary>
        /// Method that applys required transformations to the provided <see cref="TEntity"/> based on the provided <see cref="UpdateCommandRequest{TBody}"/>.
        /// This can be overriden.
        /// </summary>
        /// <param name="request">The update request</param>
        /// <param name="entity">The entity to transform</param>
        /// <param name="cancellationToken">The cancellation token</param>
        /// <returns>A <see cref="ValueTuple{bool, CommandResponse}"/>, where if success is true the activity was successful and the <see cref="CommandResponse"/> will be null.
        /// If success is false then the activity was not successful and the <see cref="CommandResponse"/> will contain a suitable response.
        /// </returns>
        protected virtual async Task<(bool success, CommandResponse response)> ProcessRequest(UpdateCommandRequest<TBody> request, TEntity entity, CancellationToken cancellationToken)
        {
            Mapper.Map(request.Model, entity);
            return (true, null);
        }

        protected virtual async Task SendMessage(string id, TEntity org)
        {
            org.Id = id;
            var msg = Mapper.Map<TMessage>(org);

            await MessageBus.PublishAsync(msg);
        }
    }
}