﻿[
  {
    "id": "5878f861-1ddb-4185-8fe7-37e40ae0ae6d",
    "AppCodeName": "data-explorer",
    "KeyVault": "axon-dev-kv-eun-explorer",
    "Settings": {
      "Port": {
        "Category": "General",
        "DisplayName": "Port number",
        "Description": "Number of the port",
        "DataType": 4,
        "DefaultValue": 80
      },
      "AuditUser": {
        "Category": "General",
        "DisplayName": "Audit user",
        "Description": "Email address of the audit user",
        "DataType": 1,
        "DefaultValue": "<EMAIL>"
      },
      "AuditUserPassword": {
        "Category": "General",
        "DisplayName": "Audit user password",
        "Description": "Password of the audit user",
        "DataType": 2,
        "DefaultValue": "il0vesoftwaremind"
      },
      "EnhancedDiagnosticLogging": {
        "Category": "General",
        "DisplayName": "Enhanced Diagnostic Logging",
        "Description": "Increase the logging side for more verbose output for debugging purposes",
        "DataType": 0,
        "DefaultValue": true
      },
      "Enabled": {
        "Category": "General",
        "DisplayName": "Enabled",
        "Description": "Enable for non-admin users",
        "DataType": 0,
        "DefaultValue": true
      },
      "DataSources": {
        "Category": "General",
        "DisplayName": "Data Sources",
        "Description": "Enable to specify data sources",
        "DataType": 3,
        "DataTypeOptions": [
          "MySql",
          "CosmosDb",
          "SQL Server"
        ],
        "IsKeyVault": true,
        "IsMultiple": true
      },
      "TestPublicSetting": {
        "Category": "General",
        "DisplayName": "Test Public Setting",
        "Description": "Tests that public settings are supported and handled correctly",
        "DataType": 0,
        "DefaultValue": true,
        "IsPublic": true
      }
    }
  },
  {
    "id": "4565ef91-b806-47a7-8ee2-d0b0a99520be",
    "AppCodeName": "masterdata-integration-test-app",
    "KeyVault": "",
    "Settings": {
      "TestSetting": {
        "Category": "General",
        "DisplayName": "TestSetting",
        "Description": "Setting for testing",
        "DataType": 2,
        "DefaultValue": true
      }
    },
    "MasterData": {
      "Country": {
        "Category": "Country Management",
        "DisplayName": "Country Management",
        "Description": "Enable to specify country data"
      },
      "Cheese": {
        "Category": "Cheese Selection",
        "DisplayName": "Cheese Selection",
        "Description": "The cheeses available for this application"
      }
    }
  }
]