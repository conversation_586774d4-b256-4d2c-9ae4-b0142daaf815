{"ConnectionStrings": {"Axon-Core-ApiDb": {"EndpointUrl": "https://axon-shared-cosmos-euw.documents.azure.com:443", "PrimaryKey": "****************************************************************************************", "DatabaseName": "Axon-Core-ApiDb-tests"}}, "AzureBlobStorage": {"StorageConnectionString": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://azurite:10000/devstoreaccount1;QueueEndpoint=http://azurite:10001/devstoreaccount1;"}, "MessageBus": {"TransportType": "RabbitMq", "RabbitMq": {"Host": "rabbit", "Port": 5672, "VirtualHost": "/", "Username": "test", "Password": "test"}}, "AzureAd": {"ClientId": "167cd45b-7d4f-4b3d-8c05-a87f12c40609", "TenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77"}, "EntraSettings": {"Issuer": "https://login.microsoftonline.com/common/v2.0", "UseCustomRefresh": "false"}, "Gigya": {"ClientId": "cUDSdI53tU5LgmVJH2AkCH-8", "Issuer": "https://tst.aaas.cencora.com/oidc/op/v1.0/4_Pv18t6XTOc51PxyYytQzHA/authorize", "UseCustomRefresh": "true"}}