﻿[
  {
    "Id": "6d0865f7-3fff-4761-98e7-fa0db075e2cf",
    "ClientId": "N/A",
    "AppCodeName": "quality-review",
    "DisplayName": "Quality Review",
    "Description": "quality review description",
    "Icon": null,
    "Url": "https://phlex.com/quality-review",
    "IsDeleted": false,
    "IsEnabled": true,
    "IsDefault": false,
    "IsSystemApp": true,
    "RunAs": 0
  },
  {
    "Id": "7001ac45-2203-4561-81eb-a782518c44d3",
    "ClientId": "8e8f9dd5-f159-4715-a6aa-3e837bd9a05f",
    "AppCodeName": "data-explorer",
    "DisplayName": "Data Explorer",
    "Description": "data explorer description",
    "Icon": null,
    "Url": "https://phlex.com/data-explorer",
    "IsDeleted": false,
    "IsEnabled": true,
    "IsDefault": false,
    "IsSystemApp": false,
    "ShowAudits": true,
    "RunAs": 0
  },
  {
    "ClientId": "N/A",
    "DisplayName": "Ingest - Demo",
    "Description": "Ingest data from us to you",
    "Icon": "string",
    "Url": "https://axon-dev.phlexglobal.com/apps/ingest/remoteEntry.js",
    "AppCodeName": "ingest",
    "IsDeleted": false,
    "IsEnabled": true,
    "IsDefault": false,
    "IsSystemApp": true,
    "id": "9e83ba87-256f-4009-a8f3-cdfca99e6835",
    "RunAs": 0
  },
  {
    "Id": "01b6dd24-4d87-4f1b-b2fe-a668f5368f90",
    "ClientId": "07c393f5-c9a3-4001-ab69-75ed5f95631b",
    "AppCodeName": "permission-test-app",
    "DisplayName": "Permission Test App",
    "Description": "Application for permission testing",
    "Icon": null,
    "Url": "https://phlex.com/permission-test",
    "IsDeleted": false,
    "IsEnabled": true,
    "IsDefault": false,
    "IsSystemApp": false,
    "RunAs": 0
  },
  {
    "id": "6d717343-6f53-46a2-8467-96cfae6b8bda",
    "ClientId": "07c393f5-c9a3-4001-ab69-75ed5f95631b",
    "AppCodeName": "permission-test-app-2",
    "DisplayName": "Permission Test App 2",
    "Description": "Application for permission testing",
    "Icon": null,
    "Url": "https://phlex.com/permission-test-2",
    "IsDeleted": false,
    "IsEnabled": true,
    "IsDefault": false,
    "IsSystemApp": false,
    "RunAs": 0
  },
  {
    "id": "f39d4295-5dec-4871-8b30-ee3859384107",
    "ClientId": "07c393f5-c9a3-4001-ab69-75ed5f95631b",
    "AppCodeName": "query-group-test-app",
    "DisplayName": "Query group test app",
    "Description": "Application for groups testing",
    "Icon": null,
    "Url": "https://phlex.com/query-group-test-app",
    "IsDeleted": false,
    "IsEnabled": true,
    "IsDefault": false,
    "IsSystemApp": false,
    "RunAs": 0
  },
  {
    "id": "c1635334-d054-40fa-9d8f-0c0399066a16",
    "ClientId": "07c393f5-c9a3-4001-ab69-75ed5f95631b",
    "AppCodeName": "access-app",
    "DisplayName": "Access App",
    "Description": "Application for app user role and group testing",
    "Icon": null,
    "Url": "https://phlex.com/access-app",
    "IsDeleted": false,
    "IsEnabled": true,
    "IsDefault": false,
    "IsSystemApp": false,
    "RunAs": 0
  },
  {
    "id": "62f1f1a7-4b9c-469f-8a5a-2cbe5154b22c",
    "ClientId": "2045423d-6ae9-48d6-9d33-7bafa6ec15f1",
    "AppCodeName": "scope-integration-test-app",
    "DisplayName": "Scope Integration Test App",
    "Description": "Application for testing realistic scope scenarios",
    "Icon": null,
    "Url": "https://phlex.com/scope-test-app",
    "IsDeleted": false,
    "IsEnabled": true,
    "IsDefault": false,
    "IsSystemApp": false,
    "RunAs": 0
  },
  {
    "id": "36cec90e-88d7-4514-b468-e0b9bb05fdc4",
    "ClientId": "5ec2e919-2864-49cf-a65e-daee986b6347",
    "AppCodeName": "masterdata-integration-test-app",
    "DisplayName": "masterdata Integration Test App",
    "Description": "Application for testing masterdata scenarios",
    "Icon": null,
    "Url": "https://phlex.com/masterdata-test-app",
    "IsDeleted": false,
    "IsEnabled": true,
    "IsDefault": false,
    "IsSystemApp": false,
    "RunAs": 0
  },
  {
    "ClientId": "bb71e917-9ba3-4867-b2a1-af346630efad",
    "DisplayName": "Trial Match",
    "Description": "Comprehensive and compassionate clinical trials recruitment",
    "Icon": null,
    "Url": "apps/trialmatch",
    "AppCodeName": "trialmatch",
    "IsDeleted": false,
    "IsEnabled": true,
    "IsDefault": false,
    "IsSystemApp": false,
    "RunAs": "Embedded",
    "ShowAudits": true,
    "Theme": {
      "AvatarUrl": "https://axondeveun.blob.core.windows.net/axon-avatar/apps/trialmatch/e9cd2033-de49-4f14-a0db-1e6a08d38834.png"
    },
    "id": "64f15a06-3723-48b2-9241-352056255346"
  }
]