/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// UpdateUserInOrganisationCommandBody
    /// </summary>
    [DataContract(Name = "UpdateUserInOrganisationCommandBody")]
    public partial class UpdateUserInOrganisationCommandBody : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateUserInOrganisationCommandBody" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected UpdateUserInOrganisationCommandBody() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateUserInOrganisationCommandBody" /> class.
        /// </summary>
        /// <param name="name">name (required).</param>
        /// <param name="email">email (required).</param>
        /// <param name="groups">groups.</param>
        /// <param name="childOrganisations">childOrganisations.</param>
        public UpdateUserInOrganisationCommandBody(string name = default(string), string email = default(string), List<string> groups = default(List<string>), List<GroupOrganisation> childOrganisations = default(List<GroupOrganisation>))
        {
            // to ensure "name" is required (not null)
            if (name == null)
            {
                throw new ArgumentNullException("name is a required property for UpdateUserInOrganisationCommandBody and cannot be null");
            }
            this.Name = name;
            // to ensure "email" is required (not null)
            if (email == null)
            {
                throw new ArgumentNullException("email is a required property for UpdateUserInOrganisationCommandBody and cannot be null");
            }
            this.Email = email;
            this.Groups = groups;
            this.ChildOrganisations = childOrganisations;
        }

        /// <summary>
        /// Gets or Sets Name
        /// </summary>
        [DataMember(Name = "name", IsRequired = true, EmitDefaultValue = true)]
        public string Name { get; set; }

        /// <summary>
        /// Gets or Sets Email
        /// </summary>
        [DataMember(Name = "email", IsRequired = true, EmitDefaultValue = true)]
        public string Email { get; set; }

        /// <summary>
        /// Gets or Sets Groups
        /// </summary>
        [DataMember(Name = "groups", EmitDefaultValue = true)]
        public List<string> Groups { get; set; }

        /// <summary>
        /// Gets or Sets ChildOrganisations
        /// </summary>
        [DataMember(Name = "childOrganisations", EmitDefaultValue = true)]
        public List<GroupOrganisation> ChildOrganisations { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class UpdateUserInOrganisationCommandBody {\n");
            sb.Append("  Name: ").Append(Name).Append("\n");
            sb.Append("  Email: ").Append(Email).Append("\n");
            sb.Append("  Groups: ").Append(Groups).Append("\n");
            sb.Append("  ChildOrganisations: ").Append(ChildOrganisations).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            // Name (string) minLength
            if (this.Name != null && this.Name.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for Name, length must be greater than 1.", new [] { "Name" });
            }

            // Email (string) minLength
            if (this.Email != null && this.Email.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for Email, length must be greater than 1.", new [] { "Email" });
            }

            yield break;
        }
    }

}
