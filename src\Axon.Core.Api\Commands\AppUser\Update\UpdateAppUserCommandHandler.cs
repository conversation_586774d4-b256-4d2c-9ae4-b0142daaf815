﻿using System.Threading.Tasks;
using System.Threading;
using MediatR;
using Axon.Core.Domain.Interfaces.Persistence;
using System.Linq;
using Axon.Core.Domain.Entities;
using Axon.Core.Api.Constants;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using System;
using static Grpc.Core.Metadata;
using Axon.Core.Api.Validators.Role;
using System.Collections.Generic;
using Phlex.Core.MessageBus;
using Axon.Core.Contracts;

namespace Axon.Core.Api.Commands.AppUser.Create
{
    internal class UpdateAppUserCommandHandler : IRequestHandler<UpdateAppUserCommand, CommandResponse>
    {

        private readonly IAccessRepository accessRepository;
        private readonly IUserRepository userRepository;
        private readonly IAppRepository appRepository;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IClientDetailsProvider clientDetailsProvider;
        private readonly ICorrelationIdProvider correlationIdProvider;
        private readonly IAuditService<TenantAuditExtensions> auditService;
        private readonly IUserAndGroupAccessRoleValidator userGroupRoleValidator;
        private readonly IMessageBus messageBus;

        public UpdateAppUserCommandHandler(IAccessRepository accessRepository,
                                           IUserRepository userRepository,
                                           IAppRepository appRepository,
                                           IOrganisationRepository organisationRepository,
                                           IClientDetailsProvider clientDetailsProvider,
                                           ICorrelationIdProvider correlationIdProvider,
                                           IAuditService<TenantAuditExtensions> auditService,
                                           IUserAndGroupAccessRoleValidator userGroupRoleValidator,
                                           IMessageBus messageBus)
        {
            this.accessRepository = accessRepository;
            this.userRepository = userRepository;
            this.appRepository = appRepository;
            this.organisationRepository = organisationRepository;
            this.clientDetailsProvider = clientDetailsProvider;
            this.correlationIdProvider = correlationIdProvider;
            this.auditService = auditService;
            this.userGroupRoleValidator = userGroupRoleValidator;
            this.messageBus = messageBus;
        }
        public async Task<CommandResponse> Handle(UpdateAppUserCommand request, CancellationToken cancellationToken)
        {
            var correlationId = correlationIdProvider.Provide();
            var clientDetails = clientDetailsProvider.Provide();

            var userDetail = await userRepository.GetUserByEmailAsync(request.EmailAddress);

            if (userDetail == null)
            {
                return CommandResponse.NotFound(nameof(UserEntity), request.EmailAddress);
            }

            var roleValidation = await userGroupRoleValidator.Validate(request.RoleId, request.AppCodeName, request.OrgCodeName, request.Scopes);
            if (!roleValidation.valid)
            {
                return CommandResponse.BadRequest(nameof(AppUser), roleValidation.message);
            }

            var userAccessList = await accessRepository.GetUserAccessForUserAsync(userDetail.Id);

            if (!userAccessList.Any())
            {
                return await CreateNewUserAccess(request.OrgCodeName, request.AppCodeName, request.RoleId, userDetail, request.EmailAddress, request.Scopes);
            }

            var accessToUpdate = userAccessList.SingleOrDefault(s => s.AppCodeName == request.AppCodeName && s.OrganisationCodeName == request.OrgCodeName);

            if (accessToUpdate == null)
            {
                return await CreateNewUserAccess(request.OrgCodeName, request.AppCodeName, request.RoleId, userDetail, request.EmailAddress, request.Scopes);
            }

            var tenantAuditExtensionsUpdate = new TenantAuditExtensions(AuditEventCategories.Access, AuditEventDescriptions.AccessUpdated, correlationId, clientDetails, request.OrgCodeName);

            await auditService.LogAsync(AuditEventTypes.AccessUpdated, tenantAuditExtensionsUpdate, accessToUpdate,
            async () =>
            {
                accessToUpdate.RoleId = request.RoleId;
                accessToUpdate.Scopes = request.Scopes?.Select(s => new RoleEntity.ScopeResources { Scope = s.Scope, Resources = s.Resources?.Select(r => new RoleEntity.Resource { Id = r.Id, Name = r.Name }).ToArray() }).ToArray();
                await accessRepository.UpdateItemAsync(accessToUpdate.Id, accessToUpdate);
            });

            var updatedEvent = new AppUserUpdatedEvent()
            {
                Action = EventActionType.Updated,
                AppCodeName = request.AppCodeName,
                AppId = accessToUpdate.AppId,
                OrgCodeName = request.OrgCodeName,
                OrgId = accessToUpdate.OrganisationId,
                UserId = accessToUpdate.User.Id
            };
            await messageBus.PublishAsync(updatedEvent);

            return CommandResponse.Success();
        }

        public async Task<CommandResponse> CreateNewUserAccess(string orgCodeName, string appCodeName, string roleId, UserEntity userDetail, string emailAddress, IReadOnlyCollection<Models.Role.ScopeResourcesBody> scopes)
        {
            var correlationId = correlationIdProvider.Provide();
            var clientDetails = clientDetailsProvider.Provide();
            var org = await organisationRepository.GetItemByCodeNameAsync(orgCodeName);
            if (org == null)
            {
                return CommandResponse.NotFound(nameof(OrganisationEntity), orgCodeName);
            }

            string appId = null;
            if (!appCodeName.Equals(AppNameConstants.AxonCoreCodeName, StringComparison.OrdinalIgnoreCase))
            {
                var app = await appRepository.GetItemByAppCodeNameAsync(appCodeName);
                if (app == null)
                {
                    return CommandResponse.NotFound(nameof(AppEntity), appCodeName);
                }
                appId = app.Id;
            }
            else
            {
                appId = AppNameConstants.AxonCoreCodeName;
            }

            var entity = new AccessEntity();

            var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Access, AuditEventDescriptions.AccessCreated, correlationId, clientDetails, orgCodeName);

            await auditService.LogAsync(AuditEventTypes.AccessCreated, tenantAuditExtensions, entity,
            async () =>
            {
                entity.AccessType = AccessType.UserAccess;
                entity.AppCodeName = appCodeName;
                entity.AppId = appId;
                entity.CreatedAt = DateTime.UtcNow;
                entity.OrganisationCodeName = orgCodeName;
                entity.OrganisationId = org.Id;
                entity.User = new AccessUser() { Email = emailAddress, Id = userDetail.Id, Name = userDetail.Name };
                entity.RoleId = roleId;
                entity.Scopes = scopes?.Select(s => new RoleEntity.ScopeResources { Scope = s.Scope, Resources = s.Resources?.Select(r => new RoleEntity.Resource { Id = r.Id, Name = r.Name }).ToArray() }).ToArray();

                await accessRepository.AddItemAsync(entity);
            });
            return CommandResponse.Success();
        }
    }
}
