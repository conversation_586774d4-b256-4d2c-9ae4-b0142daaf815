﻿using Axon.Core.Domain.Models.Auth;
using System.Collections.Generic;
namespace Axon.Core.Domain.Interfaces.Auth
{
    /// <summary>
    /// Wraps up access to the user making the current request
    /// </summary>
    public interface IUserRequestContext
    {
        string GetUserIdentityProviderId();
        string GetEmailAddress();
        UserRequestClaims GetClaimsData();
        IReadOnlyCollection<string> GetUserRoles();
        bool IsApplication();
        string ApplicationName();
    }
}
