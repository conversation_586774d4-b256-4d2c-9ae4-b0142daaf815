﻿using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Contracts;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services.GoodData;
using MassTransit;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.EventHandlers.GoodData;

public class AppGroupUpdatedEventHandler(
    IMessageBus messageBus,
    IAccessRepository accessRepository,
    IGoodDataService goodDataService,
    ILogger<AppGroupUpdatedEventHandler> logger)
    : IConsumer<AppGroupUpdatedEvent>
{
    public async Task Consume(ConsumeContext<AppGroupUpdatedEvent> context)
    {
        var message = context.Message;

        if (!await goodDataService.IsGoodDataReportingEnabled(message.AppId))
        {
            return;
        }

        if (message.Action is EventActionType.Created or EventActionType.Updated)
        {
            var groupAccessEntities = await accessRepository.GetAccessItemsForGroupAsync(message.OrgCodeName, message.AppCodeName, message.GroupId);

            if (!groupAccessEntities.Any())
            {
                logger.LogWarning("Access items for group {GroupId} do not exist.", message.GroupId);
                return;
            }

            foreach (var accessItem in groupAccessEntities)
            {
                var appUserUpdatedEvent = new AppUserUpdatedEvent
                {
                    OrgId = message.OrgId,
                    OrgCodeName = message.OrgCodeName,
                    AppId = message.AppId,
                    AppCodeName = message.AppCodeName,
                    UserId = accessItem.User.Id,
                    Action = EventActionType.Updated,
                };

                await messageBus.PublishAsync(appUserUpdatedEvent);
            }
        }
    }
}