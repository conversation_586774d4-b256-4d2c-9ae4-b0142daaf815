﻿using Axon.Core.Api.Models.Role;
using Axon.Core.Api.Models.ScopeResource;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services;
using Grpc.Core.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Axon.Core.Api.Validators.Role
{
    public class UserAndGroupAccessRoleValidator : IUserAndGroupAccessRoleValidator
    {
        private readonly IScopeResourceProvider scopeResourceProvider;
        private readonly IRoleRepository roleRepository;

        public UserAndGroupAccessRoleValidator(IScopeResourceProvider scopeResourceProvider, IRoleRepository roleRepository)
        {
            this.scopeResourceProvider = scopeResourceProvider;
            this.roleRepository = roleRepository;
        }

        public async Task<(bool valid, string message)> Validate(string roleId, string appCodeName, string orgCodeName, IReadOnlyCollection<ScopeResourcesBody> accessScopes)
        {
            var roles = await roleRepository.GetEnabledRolesAsync([roleId], [orgCodeName]);

            var relevantRole = GetRelevantRole(roles);

            var roleValidationResult = ValidateRole(appCodeName, relevantRole);
            if (!roleValidationResult.valid)
            {
                return roleValidationResult;
            }

            var roleHasUserGroupScopes = relevantRole.UserGroupScopes != null && relevantRole.UserGroupScopes.Any();
            var accessScopesHaveBeenProvided = accessScopes != null && accessScopes.Any();

            if (!roleHasUserGroupScopes && !accessScopesHaveBeenProvided)
            {
                return (true, null);
            }

            var validateScopesResult = ValidateScopes(accessScopes, relevantRole, roleHasUserGroupScopes, accessScopesHaveBeenProvided);

            if (!validateScopesResult.valid)
            {
                return validateScopesResult;
            }

            return await ValidateScopeResources(orgCodeName, accessScopes, relevantRole);
        }

        private static (bool valid, string message) ValidateScopes(IReadOnlyCollection<ScopeResourcesBody> accessScopes, RoleEntity relevantRole, bool roleHasUserGroupScopes, bool accessScopesHaveBeenProvided)
        {
            if (roleHasUserGroupScopes && !accessScopesHaveBeenProvided)
            {
                return (false, "Role is expecting some scopes to be set at the user / group level");
            }

            if (!roleHasUserGroupScopes && accessScopesHaveBeenProvided)
            {
                return (false, "Role is not expecting scopes to be set at the user / group level");
            }

            if (accessScopes.Count != relevantRole.UserGroupScopes.Count())
            {
                return (false, "All scopes specified by the role must be provided");
            }

            return (true, null);
        }

        private static (bool valid, string message) ValidateRole(string appCodeName, RoleEntity relevantRole)
        {
            if (relevantRole == null)
            {
                return (false, "Cannot find suitable role");
            }

            if (!relevantRole.AppCodeName.Equals(appCodeName))
            {
                return (false, $"Role provided is not relevent for the application {appCodeName}");
            }

            return (true, null);
        }

        private async Task<(bool valid, string message)> ValidateScopeResources(string orgCodeName, IReadOnlyCollection<ScopeResourcesBody> accessScopes, RoleEntity relevantRole)
        {
            //If we have a scope that is not on the UserGroupScopes of the role, then we can't use it.
            if (accessScopes.Any(s => !Array.Exists(relevantRole.UserGroupScopes, ugs => ugs.Scope.Equals(s.Scope, System.StringComparison.OrdinalIgnoreCase))))
            {
                return (false, "All scopes provided must be available on the Role for User/Group scope selection");
            }

            var availableScopeResources = await scopeResourceProvider.GetAvailableScopeResourcesByOrgAsync(relevantRole.AppCodeName, orgCodeName);

            var availableScopeLookup = availableScopeResources.ToLookup(x => x.Scope);

            foreach (var scope in accessScopes)
            {
                if (scope.Resources != null && scope.Resources.Any())
                {
                    var matchingScopes = availableScopeLookup[scope.Scope];

                    var hasAll = Array.TrueForAll(scope.Resources, sr => matchingScopes.Any(ms => ms.Resources.Any(mr => mr.Id.Equals(sr.Id, StringComparison.OrdinalIgnoreCase) &&
                                                                                       mr.Name.Equals(sr.Name, StringComparison.OrdinalIgnoreCase))));

                    if (!hasAll)
                    {
                        return (false, $"Provided resources for scope {scope.Scope} are not all available for use");
                    }
                }
            }

            return (true, null);
        }

        private static RoleEntity GetRelevantRole(IReadOnlyCollection<RoleEntity> roles)
        {
            var customRole = roles.SingleOrDefault(r => r.RoleType == RoleType.Custom);

            if (customRole != null)
            {
                return customRole;
            }

            var overridenRole = roles.SingleOrDefault(r => r.InheritRoleId != null && r.RoleType == RoleType.System);

            if(overridenRole != null)
            {
                return overridenRole;
            }

            return roles.SingleOrDefault();
        }
    }
}
