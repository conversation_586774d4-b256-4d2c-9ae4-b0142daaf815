using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Domain.Interfaces.Persistence;
using MediatR;
using Axon.Core.Api.Models.MasterData;
using Axon.Core.Api.Validators.MasterData;
using System.Collections.Generic;

namespace Axon.Core.Api.Queries.MasterData.GetById;

internal sealed class GetMasterDataByIdQueryHandler : IRequestHandler<GetMasterDataByIdQueryRequest, CommandResponse<MasterDataModel>>
{
    private readonly IMasterDataRepository MasterDataRepository;
    private readonly IMapper mapper;
    private readonly IMasterDataTypeValidator masterDataTypeValidator;

    public GetMasterDataByIdQueryHandler(IMasterDataRepository MasterDataRepository, IMapper mapper, IMasterDataTypeValidator masterDataTypeValidator)
    {
        this.MasterDataRepository = MasterDataRepository;
        this.mapper = mapper;
        this.masterDataTypeValidator = masterDataTypeValidator;
    }

    public async Task<CommandResponse<MasterDataModel>> Handle(GetMasterDataByIdQueryRequest request, CancellationToken cancellationToken)
    {
        (var isValid, var errorMessage) = await masterDataTypeValidator.Validate(request.DataType);

        if (!isValid)
        {
            return CommandResponse<MasterDataModel>.BadRequest(nameof(MasterDataModel), errorMessage);
        }

        var masterData = await MasterDataRepository.GetMasterDataAsync(request.DataType, request.MasterDataId);
        if (masterData == null)
        {
            return CommandResponse<MasterDataModel>.NotFound("MasterData Not Found", request.MasterDataId);
        }

        return CommandResponse<MasterDataModel>.Data(mapper.Map<MasterDataModel>(masterData));
    }
}