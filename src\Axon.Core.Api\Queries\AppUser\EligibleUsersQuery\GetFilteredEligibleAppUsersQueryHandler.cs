﻿using MediatR;
using System.Threading.Tasks;
using System.Threading;
using Axon.Core.Api.Commands;
using Phlex.Core.Api.Abstractions.Models;
using System.Linq;
using Axon.Core.Api.Models.User;
using Axon.Core.Domain.Interfaces.Persistence;
using Microsoft.Extensions.Logging;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Constants;

namespace Axon.Core.Api.Queries.AppUser.IdQuery
{
    internal class GetFilteredEligibleAppUsersQueryHandler : IRequestHandler<GetFilteredEligibleAppUsersQueryRequest, CommandResponse<ApiPagedListResult<UserModel>>>
    {
        private readonly IAccessRepository accessRepository;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAppRepository appRepository;
        private readonly ILogger<GetFilteredEligibleAppUsersQueryHandler> logger;

        public GetFilteredEligibleAppUsersQueryHandler(IAccessRepository accessRepository, 
                                                       IOrganisationRepository organisationRepository, 
                                                       IAppRepository appRepository, 
                                                       ILogger<GetFilteredEligibleAppUsersQueryHandler> logger)
        {
            this.accessRepository = accessRepository;
            this.organisationRepository = organisationRepository;
            this.appRepository = appRepository;
            this.logger = logger;
        }

        public async Task<CommandResponse<ApiPagedListResult<UserModel>>> Handle(GetFilteredEligibleAppUsersQueryRequest request, CancellationToken cancellationToken)
        {
            OrganisationEntity org = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);
            if (org == null)
            {
                logger.LogWarning("Organisation {organisationCodeName} does not exist.", request.OrganisationCodeName);
                return CommandResponse<ApiPagedListResult<UserModel>>.NotFound(nameof(OrganisationEntity), request.OrganisationCodeName);
            }

            if (!request.AppCodeName.Equals(AppNameConstants.AxonCoreCodeName, System.StringComparison.OrdinalIgnoreCase))
            {
                var app = await appRepository.GetItemByAppCodeNameAsync(request.AppCodeName);
                if (app == null)
                {
                    logger.LogWarning("Application {appCodeName} does not exist.", request.AppCodeName);
                    return CommandResponse<ApiPagedListResult<UserModel>>.NotFound(nameof(AppEntity), request.AppCodeName);
                }
            }

            var eligibleUsersResult = await accessRepository.GetOrganisationAccessRecordsForUsersWithNoRoles(request.OrganisationCodeName, request.AppCodeName, request.ListParams);

            var mapped = eligibleUsersResult.filteredUsers.Select(ua => new UserModel { Email = ua.User.Email, Id = ua.User.Id, UserName = ua.User.Name}).ToList();

            return CommandResponse<ApiPagedListResult<UserModel>>.Data(new ApiPagedListResult<UserModel>(mapped,
                request.ListParams.Offset,
                request.ListParams.Limit, eligibleUsersResult.totalUsers));
        }
    }
}
