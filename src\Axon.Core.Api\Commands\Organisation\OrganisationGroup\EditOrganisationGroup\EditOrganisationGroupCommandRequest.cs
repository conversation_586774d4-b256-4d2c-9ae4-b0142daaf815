﻿using MediatR;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.EditOrganisationGroup;

public class EditOrganisationGroupCommandRequest : IRequest<CommandResponse>
{
    public string OrganisationCodeName { get; }
    public string OrganisationGroupId { get; }
    public EditOrganisationGroupCommandRequestBody EditOrganisationGroupCommandRequestBody { get; }

    public EditOrganisationGroupCommandRequest(string organisationCodeName, string organisationGroupId, EditOrganisationGroupCommandRequestBody editOrganisationGroupCommandRequestBody)
    {
        OrganisationCodeName = organisationCodeName;
        OrganisationGroupId = organisationGroupId;
        EditOrganisationGroupCommandRequestBody = editOrganisationGroupCommandRequestBody;
    }
}