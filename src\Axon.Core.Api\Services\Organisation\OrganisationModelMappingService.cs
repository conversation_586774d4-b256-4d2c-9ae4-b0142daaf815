﻿using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Extensions;
using Axon.Core.Domain.Models.Access;

namespace Axon.Core.Api.Services.Organisation
{
    internal sealed class OrganisationModelMappingService : IOrganisationModelMappingService
    {
        private readonly IMapper mapper;

        public OrganisationModelMappingService(IMapper mapper)
        {
            this.mapper = mapper;
        }

        public OrganisationModel MapForApp(OrganisationEntity entity)
        {
            return mapper.Map<OrganisationModel>(entity);
        }

        public OrganisationModel MapForUser(OrganisationEntity entity,
                                            UserEffectivePermissionsForAppAndOrg orgAxonPermissions,
                                            IReadOnlyCollection<UserEffectivePermissionsForAppAndOrg> appPermissions)
        {
            var model = mapper.Map<OrganisationModel>(entity);

            model.ShowAudits = orgAxonPermissions.EffectivePermissions.HasPermission(nameof(CorePermissions.ViewAudit));
            model.CanManage = orgAxonPermissions.EffectivePermissions.HasPermissionFromSet([nameof(CorePermissions.EditOrganisation),
                                                                                            nameof(CorePermissions.CreateOrganisation),
                                                                                            nameof(CorePermissions.DeleteOrganisation)]);

            foreach (var appWithAccess in model.Apps.Where(app => appPermissions.Any(p => p.AppCodeName.Equals(app.AppCodeName, StringComparison.OrdinalIgnoreCase) &&
                                                                                          p.EffectivePermissions.HasRole())))
            {
                appWithAccess.HasAccess = true;
            }

            return model;
        }
    }
}
