﻿using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Interfaces.Access;
using MediatR;

namespace Axon.Core.Api.Queries.UserAccess.CurrentUserPermissionsQuery;

internal class GetCurrentUserPermissionAccessQueryHandler : IRequestHandler<GetCurrentUserPermissionAccessQuery, CommandResponse<bool>>
{
    private readonly IAccessGate accessGate;

    public GetCurrentUserPermissionAccessQueryHandler(IAccessGate accessGate)
    {
        this.accessGate = accessGate;
    }

    public async Task<CommandResponse<bool>> Handle(GetCurrentUserPermissionAccessQuery request, CancellationToken cancellationToken)
    {
        var hasAccess = await accessGate.IsAuthorised(AppNameConstants.AxonCoreCodeName, request.Permission);

        return CommandResponse<bool>.Data(hasAccess);
    }
}