{"ConnectionStrings": {"Axon-Core-ApiDb": {"EndpointUrl": "https://localhost:8081/", "PrimaryKey": "C2y6yDjf5/R+ob0N8A7Cgv30VRDJIWEHLM+4QDU5DE2nQ9nDuVTqobD4b8mGGyPMbIZnqyMsEcaGQy67XIw/Jw==", "DatabaseName": "Axon-Core-ApiDb-dev"}}, "AzureAd": {"ClientId": "167cd45b-7d4f-4b3d-8c05-a87f12c40609", "TenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77"}, "EntraSettings": {"Issuer": "https://login.microsoftonline.com/common/v2.0", "UseCustomRefresh": "false"}, "Gigya": {"ClientId": "cUDSdI53tU5LgmVJH2AkCH-8", "Issuer": "https://tst.aaas.cencora.com/oidc/op/v1.0/4_Pv18t6XTOc51PxyYytQzHA/authorize", "UseCustomRefresh": "true"}, "AzureBlobStorage": {"StorageConnectionString": "UseDevelopmentStorage=true"}, "cors": {"origins": [{"uri": "https://app-dev.smartphlex.com"}, {"uri": "https://localhost:4000"}]}, "Audit": {"TestOdata": {"UseTestEndpoint": true, "TestEndpointOdataUrl": "http://localhost:5200/odata/audits", "TestEndpointFilterUrl": "http://localhost:5200/audits/filterOptions"}, "AxonCoreOdataEndpointUrlTemplate": "https://{env}:5001/v1/odata/organisation/{tenant}/Audits"}, "AzureIdentity": {"ManageIdentityClientId": ""}, "AxonCoreShared": {"GigyaClientSecret": "rBq9U9DtJ_gb2ZkRTl4Xylpd8LGOYwKFnu0o-j8HZhOxsxwmhE9Rveakz5vj06slzCbmYWEnrqF3lByFeTAO8g", "DataProtection": {"StorageUri": "UseDevelopmentStorage=true", "KeyVaultKey": "https://axn-dev-kv-eun.vault.azure.net/keys/AxonDataProtection"}}, "Caching": {"Caches": {"Users": {"CacheItemDuration": "00:01:00"}, "IdentityProviders": {"CacheItemDuration": "00:01:00"}}}, "GoodData": {"BaseUri": "https://phlexglobal-dev.cloud.gooddata.com/", "Environment": "dev"}, "KeyVaultName": "axn-dev-kv-eun"}