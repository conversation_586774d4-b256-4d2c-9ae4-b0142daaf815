﻿using Axon.Core.Api.Models.Organisation;
using JetBrains.Annotations;
using MediatR;
using Axon.Core.Api.Commands;
using System.Threading.Tasks;
using System.Threading;
using Axon.Core.Api.Services.Organisation;

namespace Axon.Core.Api.Queries.Organisation.IdQuery
{
    [UsedImplicitly]
    internal class GetOrganisationByIdQueryHandler : IRequestHandler<IdQueryRequest<OrganisationModel>, CommandResponse<OrganisationModel>>
    {
        private readonly IOrganisationRequestService organisationRequestService;

        public GetOrganisationByIdQueryHandler(IOrganisationRequestService organisationRequestService)
        {
            this.organisationRequestService = organisationRequestService;
        }

        public async Task<CommandResponse<OrganisationModel>> Handle(IdQueryRequest<OrganisationModel> request, CancellationToken cancellationToken)
        {
            return await organisationRequestService.GetOrganisationById(request.Id);
        }
    }
}