﻿using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.App
{
    //NOTE: CAL 23/06/2021 - After extensive discussion with TK I found that [Required] attributes are only used to avoid problems
    // with the generated typescript SDK and having to deal with "string | undefined is not assignable to string" errors.
    public class AppModel
    {
        [Required] public string Id { get; set; }

        [Required] public string ClientId { get; set; }

        [Required] public string DisplayName { get; set; }

        [Required] public string Description { get; set; }

        [Required] public string Icon { get; set; }

        [Required] public string Url { get; set; }

        [Required] public string AppCodeName { get; set; }

        [Required] public bool IsDeleted { get; set; }

        [Required] public bool IsEnabled { get; set; }

        [Required] public bool IsDefault { get; set; }

        [Required] public bool IsSystemApp { get; set; }

        [Required] public string RunAs { get; set; }

        public AppThemeConfigModel Theme { get; set; }

        public static class ValidationValues
        {
            public const int MaxDisplayNameLength = 50;
            public const int MaxDescriptionLength = 200;
        }
    }

    public sealed class AppThemeConfigModel
    {
        public string AvatarUrl { get; set; }
    }
}