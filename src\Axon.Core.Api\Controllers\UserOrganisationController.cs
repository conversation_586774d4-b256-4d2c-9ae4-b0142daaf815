﻿using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Api.Queries.Organisation.UsersOrganisation;
using Axon.Core.Api.Services.Authorisation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Axon.Core.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("v{version:apiVersion}/User/Organisation")]
[ProducesResponseType(401, Type = typeof(CommandResponse))]
[ProducesResponseType(403, Type = typeof(CommandResponse))]
[ProducesResponseType(500, Type = typeof(CommandResponse))]
[Authorize]
public class UserOrganisationController : ApiControllerBase
{

    public UserOrganisationController(IMediator mediator) : base(mediator)
    {
    }

    [HttpGet]
    [HasPermissions(nameof(CorePermissions.ViewOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<OrganisationModel>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetOrganisationAsync()
    {
        return await Send(new GetUsersOrganisationQueryRequest());
    }
}