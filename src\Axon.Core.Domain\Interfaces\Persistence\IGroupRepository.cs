﻿using Axon.Core.Domain.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Domain.Interfaces.Persistence
{
    public interface IGroupRepository : IRepository<GroupEntity>
    {
        Task<IList<GroupEntity>> GetForOrganisationAsync(string organisationCodeName);
        Task<bool> GroupExistsAsync(string orgCodeName, string groupId, string groupName);

        Task<IReadOnlyCollection<GroupEntity>> GetGroupsAsync(IEnumerable<string> groupIds);
    }
}