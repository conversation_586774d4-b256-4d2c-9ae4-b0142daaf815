﻿using System;
using MediatR;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.Extensions.Logging;
using Axon.Core.Api.Models.User;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.Audit;
using Axon.Core.Domain.Models.Audit;
using Microsoft.AspNetCore.Http;
using Axon.Core.Domain.Constants;
using Axon.Core.Shared.Extensions;
using Axon.Core.Domain.Interfaces.Persistence;
namespace Axon.Core.Api.Commands.Authentication.SignIn
{
    internal class SignInCommandHandler : IRequestHandler<SignInCommand, CommandResponse<UserModel>>
    {
        private readonly ILogger<SignInCommandHandler> logger;
        private readonly IAuditService<TenantAuditExtensions> auditService;
        private readonly IAuthenticationService authenticationService;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IUserRepository userRepository;
        private readonly IOrganisationRepository organisationRepository;

        public SignInCommandHandler(ILogger<SignInCommandHandler> logger,
                                          IAuditService<TenantAuditExtensions> auditService,
                                          IAuthenticationService authenticationService,
                                          IHttpContextAccessor httpContextAccessor,
                                          IUserRepository userRepository,
                                          IOrganisationRepository organisationRepository)
        {
            this.logger = logger;
            this.auditService = auditService;
            this.authenticationService = authenticationService;
            this.httpContextAccessor = httpContextAccessor;
            this.userRepository = userRepository;
            this.organisationRepository = organisationRepository;
        }

        public async Task<CommandResponse<UserModel>> Handle(SignInCommand request, CancellationToken cancellationToken)
        {
            var user = await authenticationService.Authenticate(request.AccessToken);

            if (user == null)
            {
                throw new UnauthorizedAccessException();
            }
            var userEntity = await userRepository.GetItemAsync(user.Id);
            var tenant = await organisationRepository.GetItemAsync(userEntity.OwnerOrganisationId);

            if (tenant != null)
            {
                auditService.Log(AuditEventTypes.UserLogin,
                             new TenantAuditExtensions(AuditEventCategories.User,
                             "User has successfully logged into SmartPhlex",
                             Guid.NewGuid(),
                             new ClientDetails(httpContextAccessor.HttpContext.User.Claims.ObjectId(),
                                               user.Email,
                                               httpContextAccessor.HttpContext.Connection?.RemoteIpAddress?.ToString()), tenant.CodeName));
            }
            else
            {
                logger.LogInformation("Could not find organisation for User: {UserId}. So login cannot be audited", user.Id);
            }

            logger.LogInformation("User: {UserId} has successfully authenticated", user.Id);

            return CommandResponse<UserModel>.Data(new UserModel() { Id = user.Id, Email = user.Email, UserName = user.Name });
        }
    }
}
