﻿using System;
using Axon.Core.Api.Validators;
using Axon.Core.Shared.Api;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries;

[UsedImplicitly]
public class ListParamsValidator : AbstractValidator<ListParams>
{
    public ListParamsValidator()
    {
        RuleFor(x => x.Limit)
            .GreaterThanOrEqualTo(1)
            .LessThanOrEqualTo(ListParams.MaxLimit)
            .WithMessage($"Limit must be between 1 and {ListParams.MaxLimit}.");
        RuleFor(x => x.Offset)
            .GreaterThanOrEqualTo(0)
            .LessThanOrEqualTo(ListParams.MaxOffset)
            .WithMessage($"Offset must be between 0 and {ListParams.MaxOffset}.");
        RuleFor(x => x.Filter)
            .MaximumLength(1000)
            .WithMessage("Filter must be at most 1000 characters long.");
        RuleFor(x => x.OrderBy)
            .Must(BeAValidOrderByClause);
    }

    private static bool BeAValidOrderByClause(OrderByClauses orderBy)
    {
        if (orderBy == null)
        {
            return true;
        }
        foreach (var orderByClause in orderBy.Clauses)
        {
            var isOrderDirectionValid = Enum.IsDefined(typeof(OrderDirection), orderByClause.Direction);
            if (!isOrderDirectionValid)
            {
                return false;
            }

            var match = CustomRegex.LettersDots().Match(orderByClause.Field);
            if (!match.Success)
            {
                return false;
            }
        }
        return true;
    }
}