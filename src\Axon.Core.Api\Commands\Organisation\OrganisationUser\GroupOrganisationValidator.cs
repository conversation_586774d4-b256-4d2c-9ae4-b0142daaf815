﻿using Axon.Core.Api.Validators;
using FluentValidation;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser;

public class GroupOrganisationValidator : AbstractValidator<GroupOrganisation>
{
    public GroupOrganisationValidator()
    {
        RuleForEach(x => x.Groups)
            .MustBeAValidGuid();
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
    }
}