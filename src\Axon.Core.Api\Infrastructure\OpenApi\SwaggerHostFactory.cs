﻿using Asp.Versioning.ApiExplorer;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;

namespace Axon.Core.Api.Infrastructure.OpenApi
{
    public static class SwaggerHostFactory
    {
        /// <summary>
        /// This use used to generate the swagger.json file used for SDK generation
        /// https://github.com/domaindrivendev/Swashbuckle.AspNetCore#use-the-cli-tool-with-a-custom-host-configuration
        /// </summary>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        public static IHost CreateHost()
        {
            var builder = WebApplication.CreateBuilder(new WebApplicationOptions
            {
                ApplicationName = typeof(Program).Assembly.FullName,
                EnvironmentName = Environments.Development
            });

            builder.Services.AddApiVersioning(VersionConfiguration.VersioningOptions).AddApiExplorer(VersionConfiguration.ExplorerOptions);
            builder.Services.AddTransient<IConfigureOptions<SwaggerGenOptions>>(s =>
            {
                return new ConfigureSwaggerOptions(s.GetService<IApiVersionDescriptionProvider>(), AppConstants.ApiTitle, AppConstants.ApiDescription, string.Empty, builder.Configuration);
            });
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen(
            c =>
            {
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey

                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        Array.Empty<string>()
                    }
                });
            });

            return builder.Build();
        }
    }
}
