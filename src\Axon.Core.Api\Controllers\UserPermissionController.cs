﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.OrganisationAccess;
using Axon.Core.Api.Queries.OrganisationAccess.OrgUserPermissionQuery;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using Axon.Core.Api.Attributes;
using Axon.Core.Api.Services.Authorisation;
using FluentValidation;
using Axon.Core.Api.Extensions;

namespace Axon.Core.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("v{version:apiVersion}/Organisation/{orgCodeName}/app/{appCodeName}/user-permission")]
[ProducesResponseType(401, Type = typeof(CommandResponse))]
[ProducesResponseType(403, Type = typeof(CommandResponse))]
[ProducesResponseType(500, Type = typeof(CommandResponse))]
[Authorize]
public class UserPermissionController : ApiControllerBase
{
    private readonly IValidator<GetAppOrgUserPermissionsQueryRequest> getAppOrgUserPermissionsQueryRequestValidator;

    public UserPermissionController(IMediator mediator, IValidator<GetAppOrgUserPermissionsQueryRequest> getAppOrgUserPermissionsQueryRequestValidator) : base(mediator)
    {
        this.getAppOrgUserPermissionsQueryRequestValidator = getAppOrgUserPermissionsQueryRequestValidator;
    }

    [HttpGet("{userEmail}/permission", Name = "GetAppOrgUserPermissions")]
    [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<AppOrganisationPermission>>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [HasOrganisationPermissions(permissionsKey: nameof(CorePermissions.EditOrganisation))]
    public async Task<IActionResult> GetAppOrgUserPermissionsAsync(string orgCodeName, string appCodeName, string userEmail)
    {
        var request = new GetAppOrgUserPermissionsQueryRequest(orgCodeName, appCodeName, userEmail);
        var validationResult = await getAppOrgUserPermissionsQueryRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }
}