﻿using Axon.Core.Api.Models.OrganisationAccess;

namespace Axon.Core.Api.Commands.OrganisationAccess;

public class AppOrganisationPermissionBody
{
    public string Permission { get; set; }
    public string Scope { get; set; }
    public bool Permitted { get; set; }  
    public AppOrganisationPermissionScopeResource[] Resources { get; set; }

    public AppOrganisationPermissionBody(string permission, string scope, bool permitted, AppOrganisationPermissionScopeResource[] resources)
    {
        Permission = permission;
        Scope = scope;
        Permitted = permitted;
        Resources = resources;
    }
}
