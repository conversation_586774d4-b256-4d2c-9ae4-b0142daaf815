﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.App;
using Axon.Core.Shared.Api;
using MediatR;
using System.Collections;
using System.Collections.Generic;

namespace Axon.Core.Api.Queries.App.ListQuery
{
    public class GetAppListQueryRequest : IRequest<CommandResponse<IEnumerable<AppModel>>>
    {
        public ListParams ListParams { get; }

        public GetAppListQueryRequest(ListParams listParams)
        {
            ListParams = listParams;
        }
    }
}
