﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Axon.Core.Domain.Entities;

namespace Axon.Core.Domain.Interfaces.Persistence
{
    public interface IAppRepository : IRepository<AppEntity>
    {
        Task<AppEntity> GetItemByDisplayNameAsync(string displayName);
        Task<IList<AppEntity>> GetItemsByDisplayNameAsync(string displayName);
        Task<AppEntity> GetItemByUrlAsync(string url);
        Task<IList<AppEntity>> GetItemsByUrlAsync(string url);
        Task<AppEntity> GetItemByAppCodeNameAsync(string appCodeName);
        Task<IList<AppEntity>> GetItemsByAppCodeNamesAsync(IEnumerable<string> appCodeNames);
        Task<IList<AppEntity>> GetItemsByAppIdsAsync(IEnumerable<string> appIds);
        Task<IList<AppEntity>> GetItemsByAppCodeNameAsync(string appCodeName);
        Task<AppEntity> GetItemByClientId(string clientId);
    }
}