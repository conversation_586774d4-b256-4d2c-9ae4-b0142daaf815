﻿using System;
using System.Collections.Generic;
using System.Linq;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Persistence;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using Axon.Core.Domain.Models.Access;

namespace Axon.Core.Api.Services.Migration
{
    public class UserOwnerOrganisationMigrationService : IUserOwnerOrganisationMigrationService
    {
        private readonly IAccessService accessService;
        private readonly IUserRequestContext userRequestContext;
        private readonly IUserRepository userRepository;
        private readonly ILogger<UserOwnerOrganisationMigrationService> logger;

        public UserOwnerOrganisationMigrationService(ILogger<UserOwnerOrganisationMigrationService> logger, IAccessService accessService, IUserRequestContext userRequestContext, IUserRepository userRepository)
        {
            this.accessService = accessService;
            this.userRequestContext = userRequestContext;
            this.userRepository = userRepository;
            this.logger = logger;
        }

        public async Task<bool> Migrate()
        {
            //get current user
            var user = await userRepository.GetByIdentityProviderObjectIdAsync(userRequestContext.GetClaimsData().UserOid);
            if (user == null)
            {
                logger.Log(LogLevel.Error, "UserOwnerOrganisationMigration unable to continue, unable to retrieve user by useroid, UserOid:{UserOid}", userRequestContext.GetClaimsData().UserOid);
                return false;
            }

            if (string.IsNullOrEmpty(user.Email))
            {
                logger.Log(LogLevel.Debug, "UserOwnerOrganisationMigration unable to continue, user email is blank, useroid, UserOid:{UserOid}", user.Id);
                return false;
            }

            logger.Log(LogLevel.Debug, "UserOwnerOrganisationMigration started for user, User Id:{UserId}, User Email{email}", user.Id, user.Email);
            IReadOnlyCollection<UserOrganisationAccess> organisationAccessItems = await accessService.GetOrganisationAccessForUser(user.Id);
            if (!organisationAccessItems.Any())
            {
                logger.Log(LogLevel.Debug, "UserOwnerOrganisationMigration unable to get any organisation access items for the user, User Id:{UserId}, User Email{email}", user.Id, user.Email);
                return false;
            }

            //try to match email to organisation?
            var startOfMailServer = user.Email.IndexOf('@') + 1;
            var endOfMailServer = user.Email.LastIndexOf('.');
            var mailServer = user.Email.Substring(startOfMailServer,endOfMailServer - startOfMailServer);
            //if yes set OwnerOrganisationId
            var organisationMatches = organisationAccessItems.Where(item => item.OrganisationCodeName.Equals(mailServer, StringComparison.InvariantCultureIgnoreCase));
            var userOrganisationAccesses = organisationMatches.ToList();
            if (!userOrganisationAccesses.Any())
            {
                logger.Log(LogLevel.Debug, "UserOwnerOrganisationMigration unable to match a owner organisation for the user, User Id:{UserId}, User Email{email}", user.Id, user.Email);
                return false;
            }

            if (userOrganisationAccesses.Count() > 1)
            {
                logger.Log(LogLevel.Debug, "UserOwnerOrganisationMigration unable to continue more than one possible match for a owner organisation for the user, User Id:{UserId}, User Email{email}, possible matches:{organisationmatches}", user.Id, user.Email, string.Join(",",userOrganisationAccesses.Select(item => item.OrganisationCodeName)));
                return false;
            }
            else
            {
                UserOrganisationAccess matchUserOrganisationAccess = userOrganisationAccesses.FirstOrDefault();
                if (matchUserOrganisationAccess is null or default(UserOrganisationAccess))
                {
                    logger.Log(LogLevel.Debug, "UserOwnerOrganisationMigration unable to match a owner organisation for the user as the user has no OrganisationAccess items, User Id:{UserId}, User Email{email}", user.Id, user.Email);
                    return false;
                }
                user.OwnerOrganisationId = matchUserOrganisationAccess.OrganisationId;
                await userRepository.UpdateItemAsync(user.Id, user);
                logger.Log(LogLevel.Debug, "UserOwnerOrganisationMigration migration complete for the user, User Id:{UserId}, User Email{email}, OrganisationId:{OrganisationId}", user.Id, user.Email, matchUserOrganisationAccess.OrganisationId);
                return true;
            }
        }
    }
}
