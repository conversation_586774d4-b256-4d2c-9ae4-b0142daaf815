﻿using Axon.Core.Api.Commands.UserPreference;
using Axon.Core.Api.Services.AppGroup;
using Axon.Core.Api.Services.Audit;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Api.Services.FileUpload;
using Axon.Core.Api.Services.Migration;
using Axon.Core.Api.Services.Organisation;
using Axon.Core.Api.Services.OrganisationGroup;
using Axon.Core.Api.Services.Role;
using Axon.Core.Api.Services.Users;
using Axon.Core.Api.Validators.AppOrganisationSettings;
using Axon.Core.Api.Validators.FileUploads;
using Axon.Core.Api.Validators.MasterData;
using Axon.Core.Api.Validators.Role;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Services.Apps;
using Axon.Core.Domain.Services.KeyVault;
using Axon.Core.Domain.Services.Users;
using Axon.Core.Infrastructure.Auth;
using Axon.Core.Shared.Interfaces.Auth;
using Axon.Core.Shared.Services.Apps;
using Axon.Core.Shared.Services.Auth;
using Axon.Core.Shared.Services.User;
using Axon.Core.Shared.Services.Users;
using Microsoft.Extensions.DependencyInjection;

namespace Axon.Core.Api.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddCoreServices(this IServiceCollection services)
    {
        services.AddScoped<IUserPreferenceService, UserPreferenceService>();
        services.AddScoped<ICurrentUserProvider, CurrentUserProvider>();
        services.AddScoped<IAllowedRolesProvider, AllowedRolesProvider>();
        services.AddScoped<IUserOnboardingService, UserOnboardingService>();
        services.AddScoped<ISecretClientProvider, SecretClientProvider>();
        services.AddScoped<IUserRequestContext, UserRequestContext>();
        services.AddScoped<IHttpContextStreamFileReader, HttpContextStreamFileReader>();
        services.AddScoped<IMultipartReaderWrapper, MultipartReaderWrapper>();
        services.AddScoped<IUpdateImageValidator, UpdateImageValidator>();
        services.AddScoped<IAuditEndpointService, AuditEndpointService>();
        services.AddScoped<IImageResizer, ImageResizer>();
        services.AddScoped<IRoleBodyValidator, RoleBodyValidator>();
        services.AddScoped<IUserAndGroupAccessRoleValidator, UserAndGroupAccessRoleValidator>();
        services.AddScoped<IMasterDataSelectionValidator, MasterDataSelectionValidator>();
        services.AddScoped<IMasterDataTypeValidator, MasterDataTypeValidator>();
        services.AddScoped<IRoleModelService, RoleModelService>();
        services.AddScoped<IUserRoleMigrationService, UserRoleMigrationService>();
        services.AddScoped<IUserOwnerOrganisationMigrationService, UserOwnerOrganisationMigrationService>();
        services.AddScoped<IUserOrganisationAccessMigrationCleanupService, UserOrganisationAccessMigrationCleanupService>();
        services.AddScoped<IAuthenticationSchemeProvider, AuthenticationSchemeProvider>();
        services.AddScoped<IAppGroupMemberSynchroniser, AppGroupMemberSynchroniser>();
        services.AddScoped<IOrganisationGroupSynchroniser, OrganisationGroupSynchroniser>();
        services.AddScoped<IUserMigrationService, UserMigrationService>();
        services.AddScoped<IOrganisationRequestService, OrganisationRequestService>();
        services.AddScoped<IOrganisationModelMappingService, OrganisationModelMappingService>();
        services.AddSingleton<IUserIdentityService, UserIdentityService>();
        services.AddSingleton<ICachingUserIdentityService, CachingUserIdentityService>();
        services.AddSingleton<IAppIdentityService, AppIdentityService>();
        services.AddSingleton<ICachingAppIdentityService, CachingAppIdentityService>();
        services.AddSingleton<IUserSessionService, UserSessionService>();

        services.AddSingleton<IIdentityProviderRetrievalService, CachingIdentityProviderRetrievalService>();

        services.AddSingleton<IOidcUserIdentityService, OidcUserIdentityService>();
        services.AddSingleton<IOidcConfigAccessor, OidcConfigAccessor>();
        services.AddSingleton<IAuthenticationService, AuthenticationService>();
        services.AddSingleton<ICookieAuthenticationHttpWrapper, CookieAuthenticationHttpWrapper>();

        services.AddSingleton<ITokenAuthorisationService, TokenAuthorisationService>();

        return services;
    }
}