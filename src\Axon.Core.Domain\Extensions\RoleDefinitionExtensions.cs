﻿using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Services.Access;
using System.ComponentModel;
using System.Linq;

namespace Axon.Core.Domain.Extensions
{
    public static class RoleDefinitionExtensions
    {
        public static RoleDefinitionEntity FilterByAccessLevel(this RoleDefinitionEntity definition, AccessLevel accessLevel)
        {
            return new RoleDefinitionEntity
            {
                Id = definition.Id,
                AppCodeName = definition.AppCodeName,
                Permissions = definition.Permissions.Where(x=> x.MinimumAccessLevel == null || IsAccessLevelAllowed(x.MinimumAccessLevel.Value, accessLevel)).ToList()
            };
        }

        public static bool IsAccessLevelAllowed(AccessLevel target, AccessLevel actual)
        {
            switch (target)
            {
                case AccessLevel.Global:
                    return actual == AccessLevel.Global;
                case AccessLevel.ParentChild:
                    return actual == AccessLevel.ParentChild || actual == AccessLevel.Global;
                case AccessLevel.Restricted:
                    return true;
                default:
                    throw new InvalidEnumArgumentException("Unsupported access level");
            }
        }
    }
}
