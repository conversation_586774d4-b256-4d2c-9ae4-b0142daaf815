﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services.Access;
using MediatR;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.AppAccess.OrganisationUserQuery
{
    internal class GetOrganisationUserQueryHandler : IRequestHandler<GetOrganisationUserQueryRequest, CommandResponse<OrganisationUserModel>>
    {
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAccessRepository accessRepository;
        private readonly IUserRepository userRepository;
        private readonly IOrganisationUserGroupAccessProvider organisationUserGroupAccessProvider;

        public GetOrganisationUserQueryHandler(IOrganisationRepository organisationRepository, IAccessRepository accessRepository, IUserRepository userRepository, IOrganisationUserGroupAccessProvider organisationUserGroupAccessProvider)
        {
            this.organisationRepository = organisationRepository;
            this.accessRepository = accessRepository;
            this.userRepository = userRepository;
            this.organisationUserGroupAccessProvider = organisationUserGroupAccessProvider;
        }
        public async Task<CommandResponse<OrganisationUserModel>> Handle(GetOrganisationUserQueryRequest request, CancellationToken cancellationToken)
        {
            var organisation = await organisationRepository.GetItemByCodeNameAsync(request.OrgCodeName);
            if (organisation == null)
                return CommandResponse<OrganisationUserModel>.NotFound(nameof(OrganisationModel), request.OrgCodeName);
            var user = await userRepository.GetItemAsync(request.UserId);
            if (user == null)
                return CommandResponse<OrganisationUserModel>.NotFound(nameof(OrganisationUserModel), request.UserId);

            var accessEntities = (await accessRepository.GetAccessItemsForOrganisationAsync(organisation.Id, AccessType.OrganisationAccess)).ToList();
            var userAccessEntity = accessEntities.Find(entity => string.Equals(entity.User.Id, user.Id, StringComparison.OrdinalIgnoreCase));

            if (userAccessEntity == null && request.TakeUserFromChildOrganisations && organisation.AccessLevel == AccessLevel.ParentChild)
            {
                var childOrgs = await organisationRepository.GetAllChildOrganisationsByParentId(organisation.Id);

                foreach (var organisationEntity in childOrgs)
                {
                    var organisationAccessEntities = await accessRepository.GetAccessItemsForOrganisationAsync(organisationEntity.Id, AccessType.OrganisationAccess);
                    var userChildOrgAccessEntity = organisationAccessEntities.FirstOrDefault(entity => string.Equals(entity.User.Id, user.Id, StringComparison.OrdinalIgnoreCase));
                    if (userChildOrgAccessEntity == null) continue;

                    userAccessEntity = userChildOrgAccessEntity;
                    break;
                }
            }

            if (userAccessEntity == null)
                return CommandResponse<OrganisationUserModel>.NotFound(nameof(OrganisationUserModel), request.UserId);

            OrganisationEntity userOrganisation = null;
            if (!string.IsNullOrWhiteSpace(user.OwnerOrganisationId))
            {
                userOrganisation = string.Equals(user.OwnerOrganisationId, organisation.Id, StringComparison.OrdinalIgnoreCase)
                    ? organisation
                    : await organisationRepository.GetItemAsync(user.OwnerOrganisationId);
            }

            var embedOptions = request.Embed?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? [];
            var includeGroups = embedOptions.Contains("groups", StringComparer.OrdinalIgnoreCase);
            var includeChildOrganisations = embedOptions.Contains("childOrganisations", StringComparer.OrdinalIgnoreCase);
            var (groups, childOrganisations) = await organisationUserGroupAccessProvider.ProvideAsync(organisation.Id, user.Id, includeGroups, includeChildOrganisations);

            var organisationUserModel = new OrganisationUserModel
            {
                Id = userAccessEntity.Id,
                UserId = user.Id,
                Email = user.Email,
                Name = user.Name,
                Status = user.Status.ToString(),
                LastAccessed = user.LastAccessed,
                OrganisationName = userOrganisation?.DisplayName,
                OrganisationCodeName = userOrganisation?.CodeName,
                CreatedAt = userAccessEntity.CreatedAt,
                Groups = groups,
                ChildOrganisations = childOrganisations
            };

            return CommandResponse<OrganisationUserModel>.Data(organisationUserModel);
        }
    }
}