﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Shared.Api;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.AppGroup.ListQuery
{
    public class GetFilteredAppGroupListQueryRequest : IRequest<CommandResponse<ApiPagedListResult<AppGroupModel>>>
    {
        public string OrganisationCodeName { get; }
        public string AppCodeName { get; }
        public ListParams ListParams { get; }

        public GetFilteredAppGroupListQueryRequest(string organisationCodeName, string appCodeName, ListParams listParams)
        {
            OrganisationCodeName = organisationCodeName;
            AppCodeName = appCodeName;
            ListParams = listParams;
        }
    }
}
