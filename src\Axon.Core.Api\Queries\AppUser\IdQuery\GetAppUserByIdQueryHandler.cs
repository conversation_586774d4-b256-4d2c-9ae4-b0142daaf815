﻿using MediatR;
using System.Threading.Tasks;
using System.Threading;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppUser;
using System.Linq;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Api.Queries.AppAccess.OrganisationGroupsQuery;
using AutoMapper;
using Axon.Core.Domain.Enums;
using Axon.Core.Infrastructure.Cosmos.Repository;
using System.Collections.Generic;
using MassTransit.Contracts;
using Axon.Core.Api.Models.Role;
using Axon.Core.Domain.Entities;

namespace Axon.Core.Api.Queries.AppUser.IdQuery
{

    internal sealed class GetAppUserByIdQueryHandler : IRequestHandler<GetAppUserByIdQueryRequest, CommandResponse<SpecificAppUserModel>>
    {
        private readonly IAccessRepository accessRepository;
        private readonly IUserRepository userRepository;
        private readonly IRoleRepository roleRepository;
        private readonly IGroupRepository groupRepository;
        private readonly IMapper mapper;

        public GetAppUserByIdQueryHandler(IAccessRepository accessRepository, IUserRepository userRepository, IRoleRepository roleRepository, IGroupRepository groupRepository, IMapper mapper)
        {
            this.accessRepository = accessRepository;
            this.userRepository = userRepository;
            this.roleRepository = roleRepository;
            this.groupRepository = groupRepository;
            this.mapper = mapper;
        }

        public async Task<CommandResponse<SpecificAppUserModel>> Handle(GetAppUserByIdQueryRequest request, CancellationToken cancellationToken)
        {
            var user = await userRepository.GetUserByEmailAsync(request.EmailAddress);

            if (user == null)
            {
                return CommandResponse<SpecificAppUserModel>.NotFound("AppUser", request.EmailAddress);
            }

            var usersAppOrgAccess = await accessRepository.GetOrgAppUserAccessAsync(request.OrgCodeName, request.AppCodeName, request.EmailAddress);
            if (!usersAppOrgAccess.Any())
            {
                return CommandResponse<SpecificAppUserModel>.NotFound("AppUser", request.EmailAddress);
            }

            var userAccessList = usersAppOrgAccess.Where(x => x.AccessType == AccessType.UserAccess);
            var groupAccess = usersAppOrgAccess.Where(x => x.AccessType == AccessType.GroupAccess);

            var userDirectRoleAccess = userAccessList.SingleOrDefault();
            var userDetails = usersAppOrgAccess.Select(x => x.User).First();

            var groups = groupAccess.Any() ? await groupRepository.GetGroupsAsync(groupAccess.Select(x => x.GroupId)) : [];
            var groupsMapped = mapper.Map<IEnumerable<AppUserGroupModel>>(groups);

            var role = userDirectRoleAccess != null ? await roleRepository.GetItemAsync(userDirectRoleAccess.RoleId) : null;

            return CommandResponse<SpecificAppUserModel>.Data(new SpecificAppUserModel
            {
                AppCodeName = request.AppCodeName,
                OrganisationCodeName = request.OrgCodeName,
                UserId = userDetails.Id,
                UserName = userDetails.Name,
                Email = userDetails.Email,
                Role = role != null ? new AppUserRoleModel() { Id = role.Id, RoleName = role.RoleName } : null,
                Groups = groupsMapped.ToArray(),
                Scopes = userDirectRoleAccess?.Scopes?.Select(s => new RoleModel.ScopeResources
                {
                    Scope = s.Scope,
                    Resources = s.Resources?.Select(r => new RoleModel.Resource
                    {
                        Id = r.Id,
                        Name = r.Name
                    }).ToArray()
                }).ToArray()
            });

        }
    }
}
