﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppUser;
using Axon.Core.Shared.Api;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.AppUser.ListQuery
{
    public class GetFilteredAppUsersQueryRequest : IRequest<CommandResponse<ApiPagedListResult<AppUserModel>>>
    {
        public GetFilteredAppUsersQueryRequest(string organisationCodeName, string appCodeName, ListParams listParams)
        {
            OrganisationCodeName = organisationCodeName;
            AppCodeName = appCodeName;
            ListParams = listParams;
        }

        public string OrganisationCodeName { get; }
        public string AppCodeName { get; }
        public ListParams ListParams { get; }
    }
}
