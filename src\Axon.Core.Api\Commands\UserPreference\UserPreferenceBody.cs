﻿using System.Collections.Generic;

namespace Axon.Core.Api.Commands.UserPreference
{
    public class UserPreferenceBody
    {
        public Dictionary<string, string> UserPreferences { get; set; }

        public UserPreferenceBody(Dictionary<string, string> userPreferences)
        {
            UserPreferences = userPreferences;
        }

        public UserPreferenceBody()
        {
            
        }

        public UserPreferenceBody(string key, string value)
        {
            UserPreferences = new Dictionary<string, string>() {{key, value}};
        }
    }
}
