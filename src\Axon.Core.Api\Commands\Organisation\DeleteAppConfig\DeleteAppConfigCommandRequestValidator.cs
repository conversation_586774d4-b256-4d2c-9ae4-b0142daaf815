﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.DeleteAppConfig;

[UsedImplicitly]
public class DeleteAppConfigCommandRequestValidator : AbstractValidator<DeleteAppConfigCommandRequest>
{
    public DeleteAppConfigCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationId)
            .MustBeAValidGuid();
        RuleFor(x => x.AppId)
            .MustBeAValidGuid();
    }
}