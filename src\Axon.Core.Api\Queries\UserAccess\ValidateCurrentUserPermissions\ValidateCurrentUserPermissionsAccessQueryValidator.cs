﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.UserAccess.ValidateCurrentUserPermissions;

[UsedImplicitly]
public class ValidateCurrentUserPermissionsAccessQueryValidator : AbstractValidator<ValidateCurrentUserPermissionsAccessQuery>
{
    public ValidateCurrentUserPermissionsAccessQueryValidator()
    {
        RuleFor(x => x.OrgCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
        RuleForEach(x => x.Permissions)
            .MustBeAValidPermission();
    }
}