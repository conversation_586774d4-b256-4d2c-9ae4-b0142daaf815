﻿using Axon.Core.Domain.Entities.Base;
using Axon.Core.Domain.Enums;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using static Axon.Core.Domain.Entities.OrganisationEntity;

namespace Axon.Core.Domain.Entities
{
    public class AppEntity : BaseEntity
    {
        public string ClientId { get; set; } // Azure app client Id
        public string DisplayName { get; set; }
        public string Description { get; set; }
        public string Icon { get; set; }
        public string Url { get; set; }
        public string AppCodeName { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsEnabled { get; set; }
        public bool IsDefault { get; set; }
        public bool IsSystemApp { get; set; }
        public bool IsGoodDataReportingEnabled { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public AppRunAs RunAs { get; set; }
        public bool ShowAudits { get; set; }
        public AppThemeConfigEntity Theme { get; set; }
    }

    public class AppThemeConfigEntity
    {
        public string AvatarUrl { get; set; }
    }
}