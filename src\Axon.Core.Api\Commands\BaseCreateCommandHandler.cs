using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Domain.Entities.Base;
using Axon.Core.Domain.Interfaces.Persistence;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands
{
#pragma warning disable S2436 // Generics enable reuse across entities of standard CRUD type features from the API down to the Repository
    internal abstract class BaseCreateCommandHandler<TBody, TEntity, TMessage> : BaseCommandHandler<TEntity,CreateCommandRequest<TBody>, CommandResponse>
#pragma warning restore S2436 // Types and methods should not have too many generic parameters
        where TEntity : BaseEntity 
        where TMessage : class
    {

        protected BaseCreateCommandHandler(IRepository<TEntity> repo,
            IMapper mapper, IMessageBus messageBus
            ) : base(repo, mapper, messageBus)
        { }

        public override async Task<CommandResponse> Handle(CreateCommandRequest<TBody> request, CancellationToken cancellationToken)
        {
            var entity = Mapper.Map<TEntity>(request.Model);

            var createdId = await Repo.AddItemAsync(entity);

            await SendMessage(entity.Id, entity);

            return CommandResponse.Created(typeof(TEntity).Name, createdId);
        }

        protected virtual async Task SendMessage(string id, TEntity org)
        {
            org.Id = id;
            var msg = Mapper.Map<TMessage>(org);
            
            await MessageBus.PublishAsync(msg);
        }
    }
}