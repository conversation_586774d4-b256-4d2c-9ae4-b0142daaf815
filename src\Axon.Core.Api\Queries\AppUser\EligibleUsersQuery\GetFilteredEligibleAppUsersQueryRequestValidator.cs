﻿using Axon.Core.Api.Queries.AppUser.IdQuery;
using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.AppAccess.RolesListQuery;

[UsedImplicitly]
public class GetFilteredEligibleAppUsersQueryRequestValidator : AbstractValidator<GetFilteredEligibleAppUsersQueryRequest>
{
    public GetFilteredEligibleAppUsersQueryRequestValidator()
    {
        RuleFor(o => o.OrganisationCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
        RuleFor(o => o.AppCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
    }
}