﻿using System.Threading.Tasks;
using System.Threading;
using MediatR;
using System;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Exceptions;
using Axon.Core.Domain.Enums;
using System.Linq;
using AutoMapper;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Api.Validators.Role;
using Axon.Core.Contracts;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.User;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.AppUser.Create
{
    internal class CreateAppUserCommandHandler : IRequestHandler<CreateAppUserCommand, CommandResponse>
    {
        private readonly IUserRepository userRepository;
        private readonly IAccessRepository accessRepository;
        private readonly IAppRepository appRepository;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IClientDetailsProvider clientDetailsProvider;
        private readonly ICorrelationIdProvider correlationIdProvider;
        private readonly IAuditService<TenantAuditExtensions> auditService;
        private readonly IUserAndGroupAccessRoleValidator userGroupRoleValidator;
        private readonly IMessageBus messageBus;

        public CreateAppUserCommandHandler(IUserRepository userRepository,
                                           IAccessRepository accessRepository,
                                           IAppRepository appRepository,
                                           IOrganisationRepository organisationRepository,
                                           IClientDetailsProvider clientDetailsProvider,
                                           ICorrelationIdProvider correlationIdProvider,
                                           IAuditService<TenantAuditExtensions> auditService,
                                           IUserAndGroupAccessRoleValidator userGroupRoleValidator,
                                           IMessageBus messageBus)
        {
            this.userRepository = userRepository;
            this.accessRepository = accessRepository;
            this.userRepository = userRepository;
            this.appRepository = appRepository;
            this.organisationRepository = organisationRepository;
            this.clientDetailsProvider = clientDetailsProvider;
            this.correlationIdProvider = correlationIdProvider;
            this.auditService = auditService;
            this.userGroupRoleValidator = userGroupRoleValidator;
            this.messageBus = messageBus;
        }
        public async Task<CommandResponse> Handle(CreateAppUserCommand request, CancellationToken cancellationToken)
        {
            var user = await userRepository.GetUserByEmailAsync(request.EmailAddress);
            if (user == null) { throw new EntityNotFoundException($"User not found. EmailAddress: {request.EmailAddress}"); }

            var existing = await accessRepository.GetAccessItemsForUserAsync(request.EmailAddress, request.AppCodeName, [request.OrgCodeName], [AccessType.UserAccess]);
            if (existing.Any())
                return CommandResponse.Failed(nameof(request.EmailAddress), $"UserAccess for `{request.EmailAddress}` already exists");

            var org = await organisationRepository.GetItemByCodeNameAsync(request.OrgCodeName);
            if (org == null)
            {
                return CommandResponse.NotFound(nameof(OrganisationEntity), request.OrgCodeName);
            }

            string appId = null;
            if (!request.AppCodeName.Equals(AppNameConstants.AxonCoreCodeName, StringComparison.OrdinalIgnoreCase))
            {
                var app = await appRepository.GetItemByAppCodeNameAsync(request.AppCodeName);
                if (app == null)
                {
                    return CommandResponse.NotFound(nameof(AppEntity), request.AppCodeName);
                }
                appId = app.Id;
            }
            else
            {
                appId = AppNameConstants.AxonCoreCodeName;
            }

            var roleValidation = await userGroupRoleValidator.Validate(request.RoleId, request.AppCodeName, request.OrgCodeName, request.Scopes);
            if (!roleValidation.valid)
            {
                return CommandResponse.BadRequest(nameof(AppUser), roleValidation.message);
            }

            var entity = new AccessEntity();
            var correlationId = correlationIdProvider.Provide();
            var clientDetails = clientDetailsProvider.Provide();

            var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Access, AuditEventDescriptions.AccessCreated, correlationId, clientDetails, request.OrgCodeName);

            await auditService.LogAsync(AuditEventTypes.AccessCreated, tenantAuditExtensions, entity,
            async () =>
            {
                entity.AccessType = AccessType.UserAccess;
                entity.AppCodeName = request.AppCodeName;
                entity.AppId = appId;
                entity.CreatedAt = DateTime.UtcNow;
                entity.OrganisationCodeName = request.OrgCodeName;
                entity.OrganisationId = org.Id;
                entity.User = new AccessUser() { Email = request.EmailAddress, Id = user.Id, Name = user.Name };
                entity.RoleId = request.RoleId;
                entity.Scopes = request.Scopes?.Select(s => new RoleEntity.ScopeResources { Scope = s.Scope, Resources = s.Resources?.Select(r => new RoleEntity.Resource { Id = r.Id, Name = r.Name }).ToArray() }).ToArray();

                await accessRepository.AddItemAsync(entity);
                });

            var updatedEvent = new AppUserUpdatedEvent
            {
                OrgId = org.Id,
                OrgCodeName = org.CodeName,
                AppId = appId,
                AppCodeName = request.AppCodeName,
                UserId = user.Id,
                Action = EventActionType.Created
            };

            await messageBus.PublishAsync(updatedEvent, cancellationToken: cancellationToken);

            return CommandResponse.Created(nameof(AccessEntity), entity.Id);
        }
    }
}
