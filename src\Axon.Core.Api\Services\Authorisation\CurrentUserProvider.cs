﻿using Axon.Core.Infrastructure.Auth;
using CommunityToolkit.Diagnostics;
using Microsoft.AspNetCore.Http;

namespace Axon.Core.Api.Services.Authorisation
{
    public interface ICurrentUserProvider
    {
        UserContext GetUserContext();
    }

    public class CurrentUserProvider : ICurrentUserProvider
    {
        private readonly IHttpContextAccessor httpContextAccessor;

        public CurrentUserProvider(IHttpContextAccessor httpContextAccessor)
        {
            Guard.IsNotNull(httpContextAccessor);
            this.httpContextAccessor = httpContextAccessor;
        }

        public UserContext GetUserContext()
        {
            return new UserContext(httpContextAccessor.HttpContext?.User.Claims.ObjectId());
        }
    }
}
