﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Shared.Api;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Api.Services.Organisation
{
    /// <summary>
    /// Handles retrieval and mapping of organisations for users, supporting both standard and AppPrinciple users
    /// </summary>
    public interface IOrganisationRequestService
    {
        /// <summary>
        /// Retrieves the set of organisations the user has access to 
        /// (for Applications this is all organistions, for users this is organistions they have the ViewOrganisation permission on)
        /// </summary>
        /// <param name="listParams">The parameters to filter the accessible organistion set with</param>
        /// <returns>An <see cref="IEnumerable{T}"/> of <see cref="OrganisationModel"/> that the user can see</returns>
        Task<CommandResponse<IEnumerable<OrganisationModel>>> GetFilteredOrganisations(ListParams listParams);

        /// <summary>
        /// Retrieves a single organistion by code name, and maps it to the appropriate model
        /// No permission checks are performed to determine fundamental access for the organistion being accessed, this should be handled by the authorisation layer.
        /// </summary>
        /// <param name="orgCodeName">The code name of the organisation being accessed</param>
        /// <returns>An <see cref="OrganisationModel"/> for the matching organisation if present, if not an error response </returns>
        Task<CommandResponse<OrganisationModel>> GetOrganisationByCode(string orgCodeName);

        /// <summary>
        /// Retrieves a single organistion by code name, and maps it to the appropriate model.
        /// No permission checks are performed to determine fundamental access for the organistion being accessed, this should be handled by the authorisation layer.
        /// </summary>
        /// <param name="orgId">The id of the organisation being accessed</param>
        /// <returns>An <see cref="OrganisationModel"/> for the matching organisation if present, if not an error response </returns>
        Task<CommandResponse<OrganisationModel>> GetOrganisationById(string orgId);
    }
}
