﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Commands.Organisation;
using Axon.Core.Api.Commands.Organisation.AddAppListTo;
using Axon.Core.Api.Commands.Organisation.AddAppTo;
using Axon.Core.Api.Commands.Organisation.DeleteAppConfig;
using Axon.Core.Api.Commands.Organisation.OrganisationGroup.AddOrganisationGroup;
using Axon.Core.Api.Commands.Organisation.OrganisationGroup.AddOrganisationGroupUser;
using Axon.Core.Api.Commands.Organisation.OrganisationGroup.DeleteOrganisationGroup;
using Axon.Core.Api.Commands.Organisation.OrganisationGroup.DeleteOrganisationGroupUser;
using Axon.Core.Api.Commands.Organisation.OrganisationGroup.EditOrganisationGroup;
using Axon.Core.Api.Commands.Organisation.OrganisationUser.AddUserOrganisation;
using Axon.Core.Api.Commands.Organisation.OrganisationUser.DeleteOrganisationUser;
using Axon.Core.Api.Commands.Organisation.OrganisationUser.UpdateUserInOrganisation;
using Axon.Core.Api.Commands.Organisation.Update;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Filters;
using Axon.Core.Api.Models;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Api.Queries;
using Axon.Core.Api.Queries.Admin.Organisation;
using Axon.Core.Api.Queries.AppAccess.OrganisationGroupsQuery;
using Axon.Core.Api.Queries.AppAccess.OrganisationUserQuery;
using Axon.Core.Api.Queries.AppAccess.OrganisationUsersQuery;
using Axon.Core.Api.Queries.Organisation;
using Axon.Core.Api.Queries.Organisation.CodeNameQuery;
using Axon.Core.Api.Queries.Organisation.ValidateOrganisation;
using Axon.Core.Api.Queries.OrganisationGroup.GetById;
using Axon.Core.Api.Queries.OrganisationGroup.GetEligibleUsers;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Shared.Api;
using FluentValidation;
using JetBrains.Annotations;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("v{version:apiVersion}/Organisation")]
[ProducesResponseType(401, Type = typeof(CommandResponse))]
[ProducesResponseType(403, Type = typeof(CommandResponse))]
[ProducesResponseType(500, Type = typeof(CommandResponse))]
[Authorize]
public class OrganisationController : ApiControllerBase
{
    private readonly IValidator<ListParams> listParamsValidator;
    private readonly IValidator<IdQueryRequest<OrganisationModel>> idQueryRequestOrganisationModelValidator;
    private readonly IValidator<DeleteCommandRequest<OrganisationModel>> deleteCommandRequestOrganisationModelValidator;
    private readonly IValidator<DeleteAppConfigCommandRequest> deleteAppConfigCommandRequestValidator;
    private readonly IValidator<DeleteOrganisationUserCommandRequest> deleteOrganisationUserCommandRequestValidator;
    private readonly IValidator<GetOrganisationGroupsQueryRequest> getOrganisationGroupsQueryRequestValidator;
    private readonly IValidator<GetOrganisationUsersQueryRequest> getOrganisationUsersQueryRequestValidator;
    private readonly IValidator<GetOrganisationUserQueryRequest> getOrganisationUserQueryRequestValidator;
    private readonly IValidator<ValidateOrganisationQueryRequest> validateOrganisationUserQueryRequestValidator;
    private readonly IValidator<GetOrganisationGroupByIdQueryRequest> getOrganisationGroupByIdQueryRequestValidator;
    private readonly IValidator<GetOrganisationGroupEligibleUsersQueryRequest> getOrganisationGroupEligibleUsersQueryValidator;
    private readonly IValidator<DeleteOrganisationGroupCommandRequest> deleteOrganisationGroupCommandRequestValidator;
    private readonly IValidator<GetOrganisationByCodeNameRequest> getOrganisationByCodeNameRequestValidator;

    public OrganisationController(IMediator mediator, 
        IValidator<ListParams> listParamsValidator, 
        IValidator<IdQueryRequest<OrganisationModel>> idQueryRequestOrganisationModelValidator, 
        IValidator<DeleteCommandRequest<OrganisationModel>> deleteCommandRequestOrganisationModelValidator, 
        IValidator<DeleteAppConfigCommandRequest> deleteAppConfigCommandRequestValidator,
        IValidator<DeleteOrganisationUserCommandRequest> deleteOrganisationUserCommandRequestValidator,
        IValidator<GetOrganisationGroupsQueryRequest> getOrganisationGroupsQueryRequestValidator, 
        IValidator<GetOrganisationUsersQueryRequest> getOrganisationUsersQueryRequestValidator, 
        IValidator<GetOrganisationUserQueryRequest> getOrganisationUserQueryRequestValidator, 
        IValidator<ValidateOrganisationQueryRequest> validateOrganisationUserQueryRequestValidator,
        IValidator<GetOrganisationGroupByIdQueryRequest> getOrganisationGroupByIdQueryRequestValidator,
        IValidator<GetOrganisationGroupEligibleUsersQueryRequest> getOrganisationGroupEligibleUsersQueryValidator,
        IValidator<DeleteOrganisationGroupCommandRequest> deleteOrganisationGroupCommandRequestValidator,
        IValidator<GetOrganisationByCodeNameRequest> getOrganisationByCodeNameRequestValidator) 
        : base(mediator)
    {
        this.listParamsValidator = listParamsValidator;
        this.idQueryRequestOrganisationModelValidator = idQueryRequestOrganisationModelValidator;
        this.deleteCommandRequestOrganisationModelValidator = deleteCommandRequestOrganisationModelValidator;
        this.deleteAppConfigCommandRequestValidator = deleteAppConfigCommandRequestValidator;
        this.deleteOrganisationUserCommandRequestValidator = deleteOrganisationUserCommandRequestValidator;
        this.getOrganisationGroupsQueryRequestValidator = getOrganisationGroupsQueryRequestValidator;
        this.getOrganisationUsersQueryRequestValidator = getOrganisationUsersQueryRequestValidator;
        this.getOrganisationUserQueryRequestValidator = getOrganisationUserQueryRequestValidator;
        this.validateOrganisationUserQueryRequestValidator = validateOrganisationUserQueryRequestValidator;
        this.getOrganisationGroupByIdQueryRequestValidator = getOrganisationGroupByIdQueryRequestValidator;
        this.getOrganisationGroupEligibleUsersQueryValidator = getOrganisationGroupEligibleUsersQueryValidator;
        this.deleteOrganisationGroupCommandRequestValidator = deleteOrganisationGroupCommandRequestValidator;
        this.getOrganisationByCodeNameRequestValidator = getOrganisationByCodeNameRequestValidator;
    }

    [HttpGet("admin", Name = "GetAdminOrganisationList")]
    [HasPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<OrganisationModel>>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [ListParamsActionFilter]
    public async Task<IActionResult> GetAdminOrganisationListAsync([FromQuery][CanBeNull] string filter = "",
                                                              [FromQuery][CanBeNull] string orderBy = "",
                                                              [FromQuery] int? offset = 0,
                                                              [FromQuery] int? limit = 20)
    {
        var validationResult = await listParamsValidator.ValidateAsync(ListParams);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(new GetAdminOrganisationQueryRequest(ListParams));
    }

    [HttpGet(Name = "GetOrganisationList")]
    [HasPermissions(nameof(CorePermissions.ViewOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<OrganisationModel>>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [ListParamsActionFilter]
    public async Task<IActionResult> GetOrganisationListAsync([FromQuery][CanBeNull] string filter = "",
                                                              [FromQuery][CanBeNull] string orderBy = "",
                                                              [FromQuery] int? offset = 0,
                                                              [FromQuery] int? limit = 20)
    {
        var validationResult = await listParamsValidator.ValidateAsync(ListParams);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(new GetOrganisationQueryRequest(ListParams));
    }

    [HttpGet("{id:guid}", Name = "GetOrganisation")]
    [HasOrganisationByIdPermissions(nameof(CorePermissions.ViewOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<OrganisationModel>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetOrganisationByIdAsync(Guid id)
    {
        var request = new IdQueryRequest<OrganisationModel>(id.ToString());
        var validationResult = await idQueryRequestOrganisationModelValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpGet("{orgCodeName}", Name = "GetOrganisationByCodeName")]
    [HasOrganisationPermissions(nameof(CorePermissions.ViewOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<OrganisationModel>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetOrganisationByCodeNameAsync(string orgCodeName)
    {
        var request = new GetOrganisationByCodeNameRequest(orgCodeName);
        var validationResult = await getOrganisationByCodeNameRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpPost(Name = "CreateOrganisation")]
    [HasPermissions(nameof(CorePermissions.CreateOrganisation))]
    [ProducesResponseType(201, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> CreateOrganisationAsync([FromBody] OrganisationBody command)
    {
        return await Send(new CreateCommandRequest<OrganisationBody>(command));
    }

    [HttpPut("{id}", Name = "UpdateOrganisation")]
    [HasOrganisationByIdPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> UpdateOrganisationAsync(string id, [FromBody] UpdateOrganisationBody command)
    {
        return await Send(new UpdateCommandRequest<UpdateOrganisationBody>(id, command));
    }

    [HttpPut("{id}/header", Name = "UpdateOrganisationHeader")]
    [DisableFormValueModelBinding]
    [HasOrganisationByIdPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> UpdateOrganisationHeaderAsync(string id)
    {
        return await Send(new UpdateCommandRequest<UpdateOrganisationHeaderCommandRequest>(id, new UpdateOrganisationHeaderCommandRequest()));
    }

    [HttpDelete("{id}", Name = "DeleteOrganisation")]
    [HasOrganisationByIdPermissions(nameof(CorePermissions.DeleteOrganisation))]
    [ProducesResponseType(204)]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteOrganisationAsync(string id)
    {
        var request = new DeleteCommandRequest<OrganisationModel>(id);
        var validationResult = await deleteCommandRequestOrganisationModelValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpPost("{id}/App/{appId}", Name = "AddAppToOrganisation")]
    [HasOrganisationByIdPermissions(nameof(CorePermissions.InstallApplication))]
    [ProducesResponseType(200, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> AddAppToOrganisationAsync([FromRoute(Name = "id")] string id, [FromRoute(Name = "appId")] string appId)
    {
        return await Send(new AddAppToOrganisationCommandRequest(id, appId));
    }

    [HttpPost("{id}/App", Name = "AddAppListToOrganisation")]
    [HasOrganisationByIdPermissions(nameof(CorePermissions.InstallApplication))]
    [ProducesResponseType(200, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> AddAppListToOrganisationAsync([FromRoute(Name = "id")] string id, List<string> apps)
    {
        return await Send(new AddAppListToOrganisationCommandRequest(id, apps));
    }


    [HttpDelete("{id}/Config/{appId}", Name = "DeleteAppConfig")]
    [HasOrganisationByIdPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(204)]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteAppConfigAsync(string id, string appId)
    {
        var request = new DeleteAppConfigCommandRequest(id, appId);
        var validationResult = await deleteAppConfigCommandRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.Errors);
        }

        return await Send(request);
    }

    [HttpGet("{orgCodeName}/users", Name = "GetOrganisationUsers")]
    [HasOrganisationPermissions(nameof(CorePermissions.ViewOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<ApiPagedListResult<OrganisationUserModel>>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [ListParamsActionFilter]
    public async Task<IActionResult> GetOrganisationUsersAsync(string orgCodeName,
                                                              [FromQuery][CanBeNull] string filter = "",
                                                              [FromQuery][CanBeNull] string orderBy = "",
                                                              [FromQuery] int? offset = 0,
                                                              [FromQuery] int? limit = 20,
                                                              string embed = "")
    {
        var validationResult = await listParamsValidator.ValidateAsync(ListParams);
        var request = new GetOrganisationUsersQueryRequest(orgCodeName, ListParams, embed);
        var requestValidationResult = await getOrganisationUsersQueryRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid || !requestValidationResult.IsValid)
        {
            var validationResultErrors = validationResult.ToErrorDictionary();
            var requestValidationResultErrors = requestValidationResult.ToErrorDictionary();
            var errors = validationResultErrors.Merge(requestValidationResultErrors);
            return BadRequest(errors);
        }

        return await Send(request);
    }

    [HttpGet("{orgCodeName}/users/{userId}", Name = "GetOrganisationUser")]
    [HasOrganisationPermissions(nameof(CorePermissions.ViewOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<OrganisationUserModel>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetOrganisationUserAsync(string orgCodeName, string userId, bool takeUserFromChildOrganisations = false, string embed = "")
    {
        var request = new GetOrganisationUserQueryRequest(orgCodeName, userId, takeUserFromChildOrganisations, embed);
        var validationResult = await getOrganisationUserQueryRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpPost("{orgCodeName}/users", Name = "AddUsersToOrganisation")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse))]
    [ProducesResponseType(400, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ProducesResponseType(409, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> AddUserToOrganisationAsync(string orgCodeName, [FromBody] AddOrganisationUserCommandRequestBody addUserToOrganisationModel)
    {
        return await Send(new AddOrganisationUserCommandRequest(orgCodeName, addUserToOrganisationModel));
    }

    [HttpPut("{orgCodeName}/users/{userId}", Name = "UpdateUserInOrganisation")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse))]
    [ProducesResponseType(201, Type = typeof(CommandResponse))]
    [ProducesResponseType(400, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ProducesResponseType(409, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Put))]
    public async Task<IActionResult> UpdateUserInOrganisationAsync(string orgCodeName, string userId, [FromBody] UpdateUserInOrganisationCommandRequestBody updateUserInOrganisationModel)
    {
        return await Send(new UpdateUserInOrganisationCommandRequest(orgCodeName, userId, updateUserInOrganisationModel));
    }

    [HttpDelete("{orgCodeName}/users/{userId}", Name = "DeleteOrganisationUser")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(204)]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteOrganisationUserAsync(string orgCodeName, string userId)
    {
        var request = new DeleteOrganisationUserCommandRequest(orgCodeName, userId);
        var validationResult = await deleteOrganisationUserCommandRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpGet("{orgCodeName}/groups", Name = "GetOrganisationGroups")]
    [HasOrganisationPermissions(nameof(CorePermissions.ViewOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<ApiPagedListResult<OrganisationGroupModel>>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [ListParamsActionFilter]
    public async Task<IActionResult> GetOrganisationGroupsAsync(string orgCodeName,
                                                               [FromQuery][CanBeNull] string filter = "",
                                                               [FromQuery][CanBeNull] string orderBy = "",
                                                               [FromQuery] int? offset = 0,
                                                               [FromQuery] int? limit = 20)
    {
        var request = new GetOrganisationGroupsQueryRequest(orgCodeName, ListParams);
        var validationResult = await getOrganisationGroupsQueryRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpGet("{orgCodeName}/groups/{groupId}", Name = "GetOrganisationGroup")]
    [HasOrganisationPermissions(nameof(CorePermissions.ViewOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<OrganisationGroupPagedModel>))]
    [ProducesResponseType(400, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [ListParamsActionFilter]
    public async Task<IActionResult> GetOrganisationGroupAsync(string orgCodeName,
                                                               string groupId,
                                                               [FromQuery][CanBeNull] string filter = "",
                                                               [FromQuery][CanBeNull] string orderBy = "",
                                                               [FromQuery] int? offset = 0,
                                                               [FromQuery] int? limit = 20)
    {
        var request = new GetOrganisationGroupByIdQueryRequest(orgCodeName, groupId, ListParams);
        var validationResult = await getOrganisationGroupByIdQueryRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpGet("{orgCodeName}/groups/{groupId}/eligibleUsers", Name = "GetOrganisationGroupEligibleUsers")]
    [HasOrganisationPermissions(nameof(CorePermissions.ViewOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<ApiPagedListResult<OrganisationUserModel>>))]
    [ProducesResponseType(400, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [ListParamsActionFilter]
    public async Task<IActionResult> GetOrganisationGroupEligibleUsersAsync(string orgCodeName,
                                                                            string groupId,
                                                                            [FromQuery][CanBeNull] string filter = "",
                                                                            [FromQuery][CanBeNull] string orderBy = "",
                                                                            [FromQuery] int? offset = 0,
                                                                            [FromQuery] int? limit = 20,
                                                                            string embed = "")
    {
        var request = new GetOrganisationGroupEligibleUsersQueryRequest(orgCodeName, groupId, ListParams, embed);
        var validationResult = await getOrganisationGroupEligibleUsersQueryValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpPost("{orgCodeName}/groups", Name = "AddOrganisationGroup")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(201, Type = typeof(CommandResponse<OrganisationGroupModel>))]
    [ProducesResponseType(400, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ProducesResponseType(409, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> AddOrganisationGroupAsync(string orgCodeName, [FromBody] AddOrganisationGroupCommandRequestBody addGroupToOrganisationModel)
    {
        return await Send(new AddOrganisationGroupCommandRequest(orgCodeName, addGroupToOrganisationModel));
    }

    [HttpPut("{orgCodeName}/groups/{groupId}", Name = "EditOrganisationGroup")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<OrganisationGroupModel>))]
    [ProducesResponseType(400, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ProducesResponseType(409, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> EditOrganisationGroupAsync(string orgCodeName, string groupId, [FromBody] EditOrganisationGroupCommandRequestBody editOrganisationGroupModel)
    {
        return await Send(new EditOrganisationGroupCommandRequest(orgCodeName, groupId, editOrganisationGroupModel));
    }

    [HttpPost("{orgCodeName}/groups/{groupId}/users", Name = "AddUsersToOrganisationGroup")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse))]
    [ProducesResponseType(400, Type = typeof(CommandResponse))]
    [ProducesResponseType(403, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ProducesResponseType(409, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> AddUserToOrganisationGroupAsync(string orgCodeName, string groupId, [FromBody] AddOrganisationGroupUserCommandRequestBody addUserToOrganisationGroupModel)
    {
        return await Send(new AddOrganisationGroupUserCommandRequest(orgCodeName, groupId, addUserToOrganisationGroupModel));
    }

    [HttpDelete("{orgCodeName}/groups/{groupId}/users/{userId}", Name = "RemoveUserFromOrganisationGroup")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse))]
    [ProducesResponseType(400, Type = typeof(CommandResponse))]
    [ProducesResponseType(403, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ProducesResponseType(409, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> RemoveUserFromOrganisationGroupAsync(string orgCodeName, string groupId, string userId)
    {
        return await Send(new DeleteOrganisationGroupUserCommandRequest(orgCodeName, groupId, userId));
    }

    [HttpDelete("{orgCodeName}/groups/{groupId}", Name = "DeleteGroupFromOrganisation")]
    [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(204, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteGroupFromOrganisationAsync(string orgCodeName, string groupId)
    {
        var request = new DeleteOrganisationGroupCommandRequest(orgCodeName, groupId);
        var validationResult = await deleteOrganisationGroupCommandRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpGet("{id}/validate", Name = "ValidateOrganisationById")]
    [HasPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<ValidationResultModel>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    public async Task<IActionResult> ValidateOrganisationByIdAsync([FromRoute(Name = "id")] string id, [FromQuery(Name = "Name")] string name, [FromQuery(Name = "CodeName")] string codeName)
    {
        var request = new ValidateOrganisationQueryRequest(id, name, codeName);
        var validationResult = await listParamsValidator.ValidateAsync(ListParams);
        var requestValidationResult = await validateOrganisationUserQueryRequestValidator.ValidateAsync(request);

        if (!validationResult.IsValid || !requestValidationResult.IsValid)
        {
            var validationResultErrors = validationResult.ToErrorDictionary();
            var requestValidationResultErrors = requestValidationResult.ToErrorDictionary();
            var errors = validationResultErrors.Merge(requestValidationResultErrors);
            return BadRequest(errors);
        }

        return await Send(request);
    }

    [HttpGet("validate", Name = "ValidateOrganisation")]
    [HasPermissions(nameof(CorePermissions.CreateOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<ValidationResultModel>))]
    public async Task<IActionResult> ValidateOrganisationAsync([FromQuery(Name = "Name")] string name, [FromQuery(Name = "CodeName")] string codeName)
    {
        var request = new ValidateOrganisationQueryRequest(null, name, codeName);
        var validationResult = await validateOrganisationUserQueryRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpPut("{id}/avatar", Name = "UpdateOrganisationAvatar")]
    [DisableFormValueModelBinding]
    [HasOrganisationByIdPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> UpdateOrganisationAvatarAsync(string id)
    {
        return await Send(new UpdateCommandRequest<UpdateOrganisationAvatarCommandRequest>(id, new UpdateOrganisationAvatarCommandRequest()));
    }
}