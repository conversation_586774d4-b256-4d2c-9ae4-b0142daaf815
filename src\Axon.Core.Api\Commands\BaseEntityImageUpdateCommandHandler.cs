﻿using AutoMapper;
using Axon.Core.Api.Validators.FileUploads;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services.AzureBlobStorage;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;
using System.Threading.Tasks;
using System.Threading;
using System;
using Axon.Core.Api.Services.FileUpload;
using Axon.Core.Domain.Entities.Base;
using System.Net;

namespace Axon.Core.Api.Commands
{
#pragma warning disable S2436 // Base class requires these generics
    internal abstract class BaseEntityImageUpdateCommandHandler<TBody, TEntity, TMessage> : BaseUpdateCommandHandler<TBody, TEntity, TMessage>
#pragma warning restore S2436
                where TEntity : BaseEntity
                where TMessage : class
    {
        private readonly IAzureBlobStorageImageUploader imageUploader;
        private readonly IUpdateImageValidator updateImageValidator;
        private readonly IImageResizer imageResizer;
        private readonly EntityImageRequirements imageRequirements;
        private readonly ILogger logger;

        protected BaseEntityImageUpdateCommandHandler(
            IRepository<TEntity> repo,
            IMapper mapper,
            IMessageBus messageBus,
            IAzureBlobStorageImageUploader imageUploader,
            IUpdateImageValidator updateImageValidator,
            IImageResizer imageResizer,
            EntityImageRequirements imageRequirements,
            ILogger logger) : base(repo, mapper, messageBus)
        {
            this.imageUploader = imageUploader;
            this.updateImageValidator = updateImageValidator;
            this.imageResizer = imageResizer;
            this.imageRequirements = imageRequirements;
            this.logger = logger;
        }

        protected override async Task<(bool success, CommandResponse response)> ProcessRequest(UpdateCommandRequest<TBody> request, TEntity entity, CancellationToken cancellationToken)
        {
            try
            {
                var validationResult = await updateImageValidator.Validate(imageRequirements, cancellationToken);
                if (!validationResult.isUploadedFileValid)
                {
                    logger.LogError(validationResult.errorMessage);
                    return (false, CommandResponse.Failed(nameof(IUpdateImageValidator), validationResult.errorMessage));
                }

                var resizedImage = imageResizer.Resize(validationResult.fileStream, validationResult.newFileName, imageRequirements);
                var blobUri = await imageUploader.UploadImageAsync(validationResult.newFileName, resizedImage.resizedImageStream, GetEntityCodeName(entity));

                AppendImageUrlToEntity(entity, blobUri.AbsoluteUri);
                return (true, null);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error with image upload occurred.");
                return (false, CommandResponse.Failed(nameof(request.Model), "An error with image upload occurred.", HttpStatusCode.InternalServerError));
            }
        }

        /// <summary>
        /// When given a <see cref="TEntity"/> it returns the code name field
        /// </summary>
        /// <param name="entity">The entity</param>
        /// <returns>The code name field of the provided entity</returns>
        protected abstract string GetEntityCodeName(TEntity entity);

        /// <summary>
        /// Appends the blob uri to the correct location on the <see cref="TEntity"/>
        /// </summary>
        /// <param name="entity">The entity to modify</param>
        /// <param name="blobUri">The blob uri to append</param>
        protected abstract void AppendImageUrlToEntity(TEntity entity, string blobUri);
    }

    public sealed class EntityImageRequirements
    {
        public required int MaxFileSizeInBytes { get; init; }
        public int? TargetWidthInPixels { get; init; }
        public int? TargetHeightInPixels { get; init; }
        public bool ShouldBeSquare { get; init; }
    }
}
