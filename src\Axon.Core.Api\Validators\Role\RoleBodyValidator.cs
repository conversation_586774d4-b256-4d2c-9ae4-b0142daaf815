﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Api.Models.Role;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Extensions;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services;
using Axon.Core.Domain.Services.Access;
using CommunityToolkit.Diagnostics;

namespace Axon.Core.Api.Validators.Role
{
    public class RoleBodyValidator : IRoleBodyValidator
    {
        private readonly IScopeResourceProvider scopeResourceProvider;
        private readonly IRoleDefinitionRepository roleDefinitionRepository;
        private readonly IOrganisationRepository organisationRepository;

        public RoleBodyValidator(IScopeResourceProvider scopeResourceProvider,
                                 IRoleDefinitionRepository roleDefinitionRepository,
                                 IOrganisationRepository organisationRepository)
        {
            Guard.IsNotNull(scopeResourceProvider);
            this.scopeResourceProvider = scopeResourceProvider;
            Guard.IsNotNull(roleDefinitionRepository);
            this.roleDefinitionRepository = roleDefinitionRepository;
            Guard.IsNotNull(organisationRepository);
            this.organisationRepository = organisationRepository;
        }

        public async Task<(bool isValid, string errorMessage)> IsValid(string orgCodeName, string appCodeName, RoleBody roleBody)
        {
            var organisation = await organisationRepository.GetItemByCodeNameAsync(orgCodeName);
            if (organisation == null)
            {
                return (false, $"Role definition for `{orgCodeName}` could not be found");
            }

            var definition = await roleDefinitionRepository.GetItemByAppCodeAsync(appCodeName);
            if (definition == null)
            {
                return (false, $"Role definition for `{appCodeName}` is not found");
            }

            var filteredDefinition = definition.FilterByAccessLevel(organisation.AccessLevel ?? AccessLevel.Restricted);

            var availableScopes = await scopeResourceProvider.GetAvailableScopeResourcesByOrgAsync(appCodeName, orgCodeName);

            foreach (var permission in roleBody.Permissions)
            {
                var permissionDefinition = filteredDefinition.Permissions.Find(x => x.Name.Equals(permission.Name, StringComparison.CurrentCultureIgnoreCase));
                if (permissionDefinition == null)
                {
                    return (false, $"Permission `{permission.Name}` for `{appCodeName}` is not found");
                }

                var isValid = ValidatePermissionScopes(permissionDefinition, permission, availableScopes, (roleBody.UserGroupScopes?.Select(x => x.Scope) ?? []).ToList());
                if (!isValid)
                {
                    return (false, $"Invalid scopes/resources for permission `{permission.Name}`");
                }
            }

            return (true, null);
        }

        private static bool ValidatePermissionScopes(RoleDefinitionEntity.Permission permissionDefinition,
                                                     PermissionBody permission,
                                                     IReadOnlyCollection<AvailableScope> availableScopes,
                                                     IReadOnlyCollection<string> userGroupScopes)
        {
            //If a permission does not have scopes, then its scopes should be null
            if (!permissionDefinition.HasScopes)
            {
                return permission.Scopes == null;
            }

            //For scoped permissions, scopes should never be null
            //It can however be an empty array in some cases
            if (permission.Scopes == null)
            {
                return false;
            }

            //If a scoped permission is not allowed, then we don't expect any scopes to be provided
            if (!permission.Allow)
            {
                return !permission.Scopes.Any();
            }

            //For all available scopes, they must either be referenced on userGroupScopes or explicitly on the permission scope
            if (availableScopes.Select(x => x.Scope)
                .Any(scope => !userGroupScopes.Contains(scope) && permission.Scopes.All(x => x.Scope != scope)))
            {
                return false;
            }


            return ValidateScopesResources(permission, availableScopes);
        }

        private static bool ValidateScopesResources(PermissionBody permission, IReadOnlyCollection<AvailableScope> availableScopes)
        {
            var scopes = permission.Scopes.Select(x => x.Scope).ToList();
            var allScopesAreValid = scopes.TrueForAll(s => availableScopes.Any(x => x.Scope == s));

            return allScopesAreValid && Array.TrueForAll(permission.Scopes, scope => ValidateResources(scope, availableScopes));
        }

        private static bool ValidateResources(ScopeResourcesBody scope, IReadOnlyCollection<AvailableScope> scopeResources)
        {
            return scope.Resources == null ||
                   (Array.TrueForAll(scope.Resources, resource => scopeResources.Any(sr => sr.Scope == scope.Scope && sr.Resources.Any(r => r.Id == resource.Id && r.Name == resource.Name)))
                    || scope.Resources.Length == 0);
        }
    }
}
