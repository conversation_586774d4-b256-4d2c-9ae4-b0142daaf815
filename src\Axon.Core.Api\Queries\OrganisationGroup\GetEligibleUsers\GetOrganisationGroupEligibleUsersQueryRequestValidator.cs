﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.OrganisationGroup.GetEligibleUsers;

[UsedImplicitly]
public class GetOrganisationGroupEligibleUsersQueryRequestValidator : AbstractValidator<GetOrganisationGroupEligibleUsersQueryRequest>
{
    public GetOrganisationGroupEligibleUsersQueryRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
        RuleFor(x => x.OrganisationGroupId)
            .MustBeAValidGuid()
            .NotEmpty();
    }
}
