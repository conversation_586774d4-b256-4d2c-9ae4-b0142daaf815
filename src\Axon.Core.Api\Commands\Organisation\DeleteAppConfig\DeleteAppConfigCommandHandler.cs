﻿using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Extensions;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Exceptions;
using Axon.Core.Domain.Interfaces.Persistence;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.Organisation.DeleteAppConfig;

internal class DeleteAppConfigCommandHandler : BaseCommandHandler<OrganisationEntity, DeleteAppConfigCommandRequest, CommandResponse>
{
    public DeleteAppConfigCommandHandler(IRepository<OrganisationEntity> repo, IMapper mapper, IMessageBus messageBus) : base(repo, mapper, messageBus)
    {
    }

    public override async Task<CommandResponse> Handle(DeleteAppConfigCommandRequest request, CancellationToken cancellationToken)
    {
        if (!await Repo.TryGetItemAsync(request.OrganisationId, out var entity))
            throw new EntityNotFoundException($"Organisation with `{request.OrganisationId}` id doesn't exist.");

        if (entity.Apps == null || entity.Apps.All(x => x.AppId != request.AppId))
            throw new EntityNotFoundException($"App with `{request.AppId}` id in organisation with `{request.OrganisationId}` id doesn't exist.");

        entity.Apps = entity.Apps.Where(x => x.AppId != request.AppId);
        await Repo.UpdateItemAsync(request.OrganisationId, entity);

        var msg = new OrgUpdatedEvent()
        {
            OrgId = entity.Id, 
            OrgCodeName = entity.CodeName, 
            AppIds = new []{request.AppId}, 
            Action = EventActionType.Deleted
        };
        await MessageBus.PublishAsync(msg, cancellationToken: CancellationToken.None);
        return CommandResponse.Success();
    }
}