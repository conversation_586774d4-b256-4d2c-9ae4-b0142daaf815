﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Commands.Authentication.GoodData;
using Axon.Core.Api.Commands.Authentication.SignIn;
using Axon.Core.Api.Commands.Authentication.SignOut;
using Axon.Core.Api.Models.User;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Axon.Core.Api.Controllers
{

    [ApiController]
    [Produces("application/json", "application/xml")]
    [ProducesResponseType(401, Type = typeof(CommandResponse))]
    [ProducesResponseType(403, Type = typeof(CommandResponse))]
    [ProducesResponseType(500, Type = typeof(CommandResponse))]
    [Route("v{version:apiVersion}/Authentication")]
    [Authorize]
    public class AuthenticationController : ApiControllerBase
    {
        public AuthenticationController(IMediator mediator) : base(mediator)
        {
        }

        [HttpPost("SignIn", Name = "SignIn")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<UserModel>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
        public async Task<IActionResult> SignIn([FromQuery] string accessToken)
        {
            var command = new SignInCommand(accessToken);
            return await Send(command);
        }

        [HttpPost("SignOut", Name = "SignOut")]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
        public async Task<IActionResult> SignOutAction([FromQuery]bool autoLogout)
        {
            var command = new SignOutCommand(autoLogout);
            return await Send(command);
        }

        [HttpPost("GoodData/token", Name = "GetGoodDataUserToken")]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
        public async Task<IActionResult> GetGoodDataUserToken()
        {
            return await Send(new GetGoodDataUserTokenCommand());
        }
    }
}
