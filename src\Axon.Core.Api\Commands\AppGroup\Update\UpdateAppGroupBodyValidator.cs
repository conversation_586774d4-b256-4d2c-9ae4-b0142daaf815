﻿using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.AppGroup.Update
{
    [UsedImplicitly]
    public class UpdateAppGroupBodyValidator : BaseCommandValidator<UpdateAppGroupRequest>
    {
        public UpdateAppGroupBodyValidator()
        {
            RuleFor(o => o.OrganisationCodeName)
                .MustBeAValidCodeName()
                .NotEmpty();
            RuleFor(o => o.AppCodeName)
                .MustBeAValidCodeName()
                .NotEmpty();
            RuleFor(o => o.UpdateAppGroupBody).NotNull();
            When(o => o.UpdateAppGroupBody != null, () => RuleFor(o => o.UpdateAppGroupBody.RoleId)
                .MustBeAValidGuid()
                .NotEmpty()
                .WithMessage("'RoleId' must not be empty"));
            When(o => o.UpdateAppGroupBody != null, () => RuleFor(o => o.UpdateAppGroupBody.RoleName)
                .MustBeAValidRoleName()
                .NotEmpty()
                .WithMessage("'RoleName' must not be empty"));
        }
    }
}
