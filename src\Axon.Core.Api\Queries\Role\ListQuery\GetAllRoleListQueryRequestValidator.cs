﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.Role.ListQuery;

[UsedImplicitly]
public class GetAllRoleListQueryRequestValidator : AbstractValidator<GetAllRoleListQueryRequest>
{
    public GetAllRoleListQueryRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
    }
}