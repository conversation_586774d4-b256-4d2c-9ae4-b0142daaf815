﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;
using System.Linq;
using System;

namespace Axon.Core.Api.Queries.AppAccess.OrganisationUsersQuery;

[UsedImplicitly]
public class GetOrganisationUsersQueryRequestValidator : AbstractValidator<GetOrganisationUsersQueryRequest>
{
    public GetOrganisationUsersQueryRequestValidator()
    {
        RuleFor(x => x.OrgCodeName)
            .MustBeAValidCodeName();
        RuleFor(o => o.Embed)
            .Must(embed =>
            {
                if (string.IsNullOrWhiteSpace(embed))
                    return true;

                var embedOptions = embed.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
                return embedOptions.TrueForAll(embedOption =>
                    embedOption.Equals("groups", StringComparison.OrdinalIgnoreCase) || embedOption.Equals("childOrganisations", StringComparison.OrdinalIgnoreCase));
            })
            .WithMessage("Valid embed options are: 'groups' and 'childOrganisations'.");
    }
}