﻿using System.Linq;
using Microsoft.AspNetCore.Http;

namespace Axon.Core.Api.Extensions
{
    public static class QueryCollectionExtensions
    {
        public static string ByAlias(this IQueryCollection query, params string[] aliases)
        {
            var key = query.Keys.SingleOrDefault(k => aliases.Select(a => a.ToLowerInvariant()).Contains(k.ToLowerInvariant()));
            return key == null
                ? string.Empty
                : query[key];
        }
    }
}