﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Api.Queries.AppAccess.OrganisationUserQuery;
using Axon.Core.Api.Services.UserOrganisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Infrastructure.Extensions;
using Axon.Core.Shared.Api;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.AppAccess.OrganisationUsersQuery
{
    internal class GetOrganisationUsersQueryHandler : IRequestHandler<GetOrganisationUsersQueryRequest, CommandResponse<ApiPagedListResult<OrganisationUserModel>>>
    {
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAccessRepository accessRepository;
        private readonly IUserRepository userRepository;
        private readonly IOrganisationUserGroupAccessProvider organisationUserGroupAccessProvider;
        private readonly IOrganisationUserProvider organisationUserProvider;


        public GetOrganisationUsersQueryHandler(IOrganisationRepository organisationRepository, IAccessRepository accessRepository, IUserRepository userRepository, IOrganisationUserGroupAccessProvider organisationUserGroupAccessProvider, IOrganisationUserProvider organisationUserProvider)
        {
            this.organisationRepository = organisationRepository;
            this.accessRepository = accessRepository;
            this.userRepository = userRepository;
            this.organisationUserGroupAccessProvider = organisationUserGroupAccessProvider;
            this.organisationUserProvider = organisationUserProvider;
        }

        public async Task<CommandResponse<ApiPagedListResult<OrganisationUserModel>>> Handle(GetOrganisationUsersQueryRequest request, CancellationToken cancellationToken)
        {
            var organisation = await organisationRepository.GetItemByCodeNameAsync(request.OrgCodeName);
            if (organisation == null)
                return CommandResponse<ApiPagedListResult<OrganisationUserModel>>.NotFound(nameof(OrganisationModel), request.OrgCodeName);

            var accessEntities = await accessRepository.GetAccessItemsForOrganisationAsync(organisation.Id, AccessType.OrganisationAccess);
            
            var filteredAccessEntities = accessEntities.AsQueryable().FilteredResult(request.ListParams.Filter);

            if (!request.ListParams.OrderBy.Any())
                request.ListParams.OrderBy = OrderByClauses.Parse($"{nameof(AccessEntity.User)}.{nameof(AccessEntity.User.Name)}");
            var pagedAccessEntities = filteredAccessEntities
                .OrderedResult(request.ListParams.OrderBy)
                .PagedResult(request.ListParams.Offset, request.ListParams.Limit);

            var userEmails = pagedAccessEntities
                .Select(accessEntity => accessEntity.User.Email)
                .ToArray();
            var users = await userRepository.GetUsersAsync(userEmails);

            var organisationUserModels = new List<OrganisationUserModel>();
            var userOrganisations = new Dictionary<string, OrganisationEntity>();
            foreach (var accessEntity in pagedAccessEntities)
            {
                var (user, userOrganisation) = await organisationUserProvider.ProvideAsync(users, organisation, accessEntity, userOrganisations);

                organisationUserModels.Add(new OrganisationUserModel
                {
                    Id = accessEntity.Id,
                    UserId = user?.Id ?? accessEntity.User.Id,
                    Email = user?.Email ?? accessEntity.User.Email,
                    Name = user?.Name ?? accessEntity.User.Name,
                    Status = (user?.Status ?? UserStatus.Inactive).ToString(),
                    LastAccessed = user?.LastAccessed == DateTime.MinValue ? null : user?.LastAccessed,
                    OrganisationName = userOrganisation?.DisplayName,
                    OrganisationCodeName = userOrganisation?.CodeName,
                    CreatedAt = accessEntity.CreatedAt == DateTime.MinValue ? null : accessEntity.CreatedAt,
                    Groups = [],
                    ChildOrganisations = []
                });
            }

            var embedOptions = request.Embed?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? [];
            var includeGroups = embedOptions.Contains("groups", StringComparer.OrdinalIgnoreCase);
            var includeChildOrganisations = embedOptions.Contains("childOrganisations", StringComparer.OrdinalIgnoreCase);
            if (includeGroups || includeChildOrganisations)
                foreach (var organisationUserModel in organisationUserModels)
                {
                    var (groups, childOrganisations) = await organisationUserGroupAccessProvider.ProvideAsync(organisation.Id, organisationUserModel.UserId, includeGroups, includeChildOrganisations);
                    organisationUserModel.Groups = groups;
                    organisationUserModel.ChildOrganisations = childOrganisations;
                }

            return CommandResponse<ApiPagedListResult<OrganisationUserModel>>.Data(
                new ApiPagedListResult<OrganisationUserModel>(
                    organisationUserModels,
                    request.ListParams.Offset,
                    request.ListParams.Limit,
                    filteredAccessEntities.Count()));
        }
    }
}