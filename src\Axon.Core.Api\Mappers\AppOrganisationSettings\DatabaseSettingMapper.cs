﻿using System.Collections.Generic;
using System.Text.Json;
using Axon.Core.Domain.Models;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Axon.Core.Api.Mappers.AppOrganisationSettings;

public class DatabaseSettingMapper : ISettingMapper
{
    public object Map(string settingValue)
        => JsonConvert.DeserializeObject<DatabaseSettingValue[]>(settingValue);

    public object Map(JsonElement settingValue)
    {
        var jsonOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
        return JsonSerializer.Deserialize<IEnumerable<DatabaseSettingValue>>(settingValue.GetRawText(), jsonOptions);
    }
}