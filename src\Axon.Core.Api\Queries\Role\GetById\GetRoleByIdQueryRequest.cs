﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Role;
using MediatR;

namespace Axon.Core.Api.Queries.Role.GetById;

public class GetRoleByIdQueryRequest : IRequest<CommandResponse<RoleModel>>
{
    public string OrgCodeName { get; }
    public string AppCodeName { get; }
    public string Id { get; }

    public GetRoleByIdQueryRequest(string orgCodeName, string appCodeName, string id)
    {
        OrgCodeName = orgCodeName;
        AppCodeName = appCodeName;
        Id = id;
    }
}