﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Role;
using MediatR;
using System.Collections.Generic;

namespace Axon.Core.Api.Queries.RoleDefinition.RoleDefinitionListQuery
{
    public class GetRoleDefinitionQueryRequest : IRequest<CommandResponse<IEnumerable<AppRoleDefinitionPermission>>>
    {
        public GetRoleDefinitionQueryRequest(string appCode)
        {
            AppCode = appCode;
        }
        public string AppCode { get; }

    }
}
