using System;
using System.Runtime.Serialization;

namespace Axon.Core.Api.Exceptions
{
    [Serializable]
    public class StartupException : Exception
    {
        //
        // For guidelines regarding the creation of new exception types, see
        //    http://msdn.microsoft.com/library/default.asp?url=/library/en-us/cpgenref/html/cpconerrorraisinghandlingguidelines.asp
        // and
        //    http://msdn.microsoft.com/library/default.asp?url=/library/en-us/dncscol/html/csharp07192001.asp
        //

        public StartupException()
        {
        }

        public StartupException(string message) : base(message)
        {
        }

        public StartupException(string message, Exception inner) : base(message, inner)
        {
        }

        protected StartupException(
            SerializationInfo info,
            StreamingContext context) : base(info, context)
        {
        }
    }
}