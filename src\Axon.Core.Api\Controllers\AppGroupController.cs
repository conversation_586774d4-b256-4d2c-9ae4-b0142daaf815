﻿using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Filters;
using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Api.Queries.AppGroup.ListQuery;
using Axon.Core.Api.Services.Authorisation;
using JetBrains.Annotations;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;
using System.Threading.Tasks;
using Axon.Core.Api.Queries.AppGroup.GetById;
using FluentValidation;
using Axon.Core.Api.Queries.AppGroup.ListUnassignedGroups;
using Axon.Core.Shared.Api;
using Axon.Core.Api.Extensions;

namespace Axon.Core.Api.Controllers
{
    [ApiController]
    [Produces("application/json", "application/xml")]
    [Route("v{version:apiVersion}/organisation/{orgCodeName}/app/{appCodeName}/group")]
    [ProducesResponseType(401, Type = typeof(CommandResponse))]
    [ProducesResponseType(403, Type = typeof(CommandResponse))]
    [ProducesResponseType(500, Type = typeof(CommandResponse))]
    [Authorize]
    public class AppGroupController : ApiControllerBase
    {
        private readonly IValidator<ListParams> listParamsValidator;
        private readonly IValidator<GetFilteredAppGroupListQueryRequest> getFilteredAppGroupListQueryValidator;
        private readonly IValidator<GetAppGroupByIdQueryRequest> getAppGroupByIdQueryValidator;
        private readonly IValidator<DeleteCommandRequest<AppGroupModel>> deleteCommandRequestAppGroupModelValidator;
        private readonly IValidator<GetUnassignedGroupsQueryRequest> unassignedGroupsModelValidator;

        public AppGroupController(IMediator mediator, IValidator<ListParams> listParamsValidator, IValidator<GetFilteredAppGroupListQueryRequest> getFilteredAppGroupListQueryValidator, IValidator<GetAppGroupByIdQueryRequest> getAppGroupByIdQueryValidator, IValidator<DeleteCommandRequest<AppGroupModel>> deleteCommandRequestAppGroupModelValidator, IValidator<GetUnassignedGroupsQueryRequest> unassignedGroupsModelValidator) : base(mediator)
        {
            this.listParamsValidator = listParamsValidator;
            this.getFilteredAppGroupListQueryValidator = getFilteredAppGroupListQueryValidator;
            this.getAppGroupByIdQueryValidator = getAppGroupByIdQueryValidator;
            this.deleteCommandRequestAppGroupModelValidator = deleteCommandRequestAppGroupModelValidator;
            this.unassignedGroupsModelValidator = unassignedGroupsModelValidator;
        }

        [HttpGet(Name = "GetFilteredAppGroups")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<ApiPagedListResult<AppGroupModel>>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        [ListParamsActionFilter]
        public async Task<IActionResult> GetFilteredAppGroupsAsync(string orgCodeName,
                                                                   string appCodeName,
                                                                   [FromQuery][CanBeNull] string filter = "",
                                                                   [FromQuery][CanBeNull] string orderBy = "",
                                                                   [FromQuery] int? offset = 0,
                                                                   [FromQuery] int? limit = 20)
        {
            var request = new GetFilteredAppGroupListQueryRequest(orgCodeName, appCodeName, ListParams);
            var validationResult = await listParamsValidator.ValidateAsync(ListParams);
            var requestValidationResult = await getFilteredAppGroupListQueryValidator.ValidateAsync(request);
            if (!validationResult.IsValid || !requestValidationResult.IsValid)
            {
                var validationResultErrors = validationResult.ToErrorDictionary();
                var requestValidationResultErrors = requestValidationResult.ToErrorDictionary();
                var errors = validationResultErrors.Merge(requestValidationResultErrors);
                return BadRequest(errors);
            }

            return await Send(request);
        }

        [HttpGet("unassigned", Name = "GetUnassignedGroups")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<ApiListResult<UnassignedGroupModel>>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        public async Task<IActionResult> GetUnassignedGroupsAsync(string orgCodeName, string appCodeName)
        {
            var request = new GetUnassignedGroupsQueryRequest(orgCodeName, appCodeName);
            var validationResult = await unassignedGroupsModelValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpGet("{id}", Name = "GetAppGroupById")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<AppGroupModel>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        public async Task<IActionResult> GetAppGroupByIdAsync(string orgCodeName, string appCodeName, [FromRoute] string id)
        {
            var request = new GetAppGroupByIdQueryRequest(orgCodeName, appCodeName, id);
            var validationResult = await getAppGroupByIdQueryValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpPut("{id}", Name = "UpdateAppGroup")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
        public async Task<IActionResult> UpdateAppGroupAsync([FromRoute] string id, UpdateAppGroupRequest command)
        {
            return await Send(new UpdateCommandRequest<UpdateAppGroupRequest>(id, command));
        }

        [HttpDelete("{id}", Name = "DeleteAppGroup")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(204)]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
        public async Task<IActionResult> DeleteAppGroupAsync([FromRoute] string id)
        {
            var request = new DeleteCommandRequest<AppGroupModel>(id);
            var validationResult = await deleteCommandRequestAppGroupModelValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpPost(Name = "CreateAppGroup")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(201, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
        public async Task<IActionResult> CreateAppGroupAsync(CreateAppGroupRequest command)
        {
            return await Send(new CreateCommandRequest<CreateAppGroupRequest>(command));
        }
    }
}
