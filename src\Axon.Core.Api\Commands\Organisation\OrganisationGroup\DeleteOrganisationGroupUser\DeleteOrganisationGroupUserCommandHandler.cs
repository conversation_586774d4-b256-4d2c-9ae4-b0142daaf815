﻿using System;
using AutoMapper;
using Axon.Core.Api.Services.UserOrganisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Phlex.Core.MessageBus;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.DeleteOrganisationGroupUser;

internal class DeleteOrganisationGroupUserCommandHandler : BaseCommandHandler<AccessEntity, DeleteOrganisationGroupUserCommandRequest, CommandResponse>
{
    private readonly IOrganisationRepository organisationRepository;
    private readonly IGroupRepository groupRepository;
    private readonly IUserRepository userRepository;
    private readonly IOrganisationUserManager organisationUserManager;
    private readonly IClientDetailsProvider clientDetailsProvider;
    private readonly ICorrelationIdProvider correlationIdProvider;

    public DeleteOrganisationGroupUserCommandHandler(
        IAccessRepository accessRepository,
        IMapper mapper,
        IMessageBus messageBus,
        IOrganisationRepository organisationRepository,
        IGroupRepository groupRepository,
        IUserRepository userRepository,
        IOrganisationUserManager organisationUserManager,
        IClientDetailsProvider clientDetailsProvider,
        ICorrelationIdProvider correlationIdProvider)
        : base(accessRepository, mapper, messageBus)
    {
        this.organisationRepository = organisationRepository;
        this.groupRepository = groupRepository;
        this.userRepository = userRepository;
        this.organisationUserManager = organisationUserManager;
        this.clientDetailsProvider = clientDetailsProvider;
        this.correlationIdProvider = correlationIdProvider;
    }

    public override async Task<CommandResponse> Handle(DeleteOrganisationGroupUserCommandRequest request, CancellationToken cancellationToken)
    {
        var organisation = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);
        if (organisation == null)
            return CommandResponse.NotFound(nameof(OrganisationEntity), request.OrganisationCodeName);

        var group = await groupRepository.GetItemAsync(request.GroupId);
        if (group == null)
            return CommandResponse.NotFound(nameof(GroupEntity), request.GroupId);

        var user = await userRepository.GetItemAsync(request.UserId);
        if (user == null)
            return CommandResponse.NotFound(nameof(UserEntity), request.UserId);

        var clientDetails = clientDetailsProvider.Provide();
        var correlationId = correlationIdProvider.Provide();

        await organisationUserManager.UnassignUserFromGroup(
            correlationId,
            clientDetails,
            organisation.CodeName,
            user.Id,
            user.Email,
            group);

        group.LastUpdatedDate = DateTime.UtcNow;
        await groupRepository.UpdateItemAsync(group.Id, group);

        return CommandResponse.Success();
    }
}