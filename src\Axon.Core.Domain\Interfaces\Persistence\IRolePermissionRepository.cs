﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Axon.Core.Domain.Entities;

namespace Axon.Core.Domain.Interfaces.Persistence
{
    public interface IRolePermissionRepository : IRepository<RolePermissionEntity>
    {
        Task<IList<RolePermissionEntity.Permission>> GetRolePermissionsAsync(params string[] roles);
        Task<IList<RolePermissionEntity>> GetAppRolePermissionsAsync(string appCodeName);
        Task<RolePermissionEntity> GetAppRolePermissionsAsync(string appCodeName, string role);
        Task<IList<RolePermissionEntity.Permission>> GetAppRolePermissionsAsync(string appCodeName, string[] roles);
        Task<RolePermissionEntity> GetDefaultAppPermissionsAsync(string appCodeName);
        Task<RolePermissionEntity> GetAppRolePermissionByNameAsync(string appCodeName, string role);
    }
}