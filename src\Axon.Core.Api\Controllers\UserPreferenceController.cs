﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Commands.UserPreference;
using Axon.Core.Api.Commands.UserPreference.Create;
using Axon.Core.Api.Commands.UserPreference.Delete;
using Axon.Core.Api.Commands.UserPreference.Update;
using Axon.Core.Api.Models.UserPreference;
using Axon.Core.Api.Queries.UserPreferences.CurrentUserQuery;
using Axon.Core.Api.Queries.UserPreferences.NameQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Axon.Core.Api.Extensions;

namespace Axon.Core.Api.Controllers
{
    [ApiController]
    [Produces("application/json", "application/xml")]
    [Route("v{version:apiVersion}/User/Preferences")]
    [ProducesResponseType(401, Type = typeof(CommandResponse))]
    [ProducesResponseType(403, Type = typeof(CommandResponse))]
    [ProducesResponseType(500, Type = typeof(CommandResponse))]
    [Authorize]
    public class UserPreferenceController : ApiControllerBase
    {
        private readonly IValidator<GetPreferenceByKeyQueryRequest> getPreferenceByKeyQueryRequestValidator;
        private readonly IValidator<DeleteUserPreferenceCommandRequest> deleteUserPreferenceCommandRequestValidator;

        public UserPreferenceController(IMediator mediator, IValidator<GetPreferenceByKeyQueryRequest> getPreferenceByKeyQueryRequestValidator, IValidator<DeleteUserPreferenceCommandRequest> deleteUserPreferenceCommandRequestValidator) : base(mediator)
        {
            this.getPreferenceByKeyQueryRequestValidator = getPreferenceByKeyQueryRequestValidator;
            this.deleteUserPreferenceCommandRequestValidator = deleteUserPreferenceCommandRequestValidator;
        }

        [HttpGet(Name = "GetUserPreferences")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<UserPreferencesModel>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        public async Task<IActionResult> GetUserPreferencesAsync() =>
            await Send(new GetCurrentUserPreferencesQueryRequest());

        [HttpGet("{key}", Name = "GetUserPreferenceByKey")]
        [ProducesResponseType(200, Type = typeof(CommandResponse<UserPreferencesModel>))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        public async Task<IActionResult> GetUserPreferenceByKeyAsync(string key)
        {
            var request = new GetPreferenceByKeyQueryRequest(key);
            var validationResult = await getPreferenceByKeyQueryRequestValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }

        [HttpPost("{key}/{value}", Name = "CreatePreference")]
        [ProducesResponseType(201, Type = typeof(CommandResponse))]
        [ProducesResponseType(409, Type = typeof(CommandResponse))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
        public async Task<IActionResult> CreatePreferenceAsync(string key, string value) =>
            await Send(new CreateUserPreferenceCommandRequest(new UserPreferenceBody(key,value)));

        [HttpPost(Name = "CreateManyPreference")]
        [ProducesResponseType(201, Type = typeof(CommandResponse))]
        [ProducesResponseType(409, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
        public async Task<IActionResult> CreateManyPreferenceAsync([FromBody] UserPreferenceBody model) =>
            await Send(new CreateUserPreferenceCommandRequest(model));

        [HttpPut("{key}/{value}", Name = "CreateOrUpdatePreference")]
        [ProducesResponseType(201, Type = typeof(CommandResponse))]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
        public async Task<IActionResult> CreateOrUpdatePreferenceAsync(string key, string value)
        {
            var body = new UserPreferenceBody(key, value);
            return await Send(new UpdateUserPreferenceCommandRequest(body));
        }

        [HttpPut(Name = "CreateOrUpdateManyPreference")]
        [ProducesResponseType(201, Type = typeof(CommandResponse))]
        [ProducesResponseType(200, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
        public async Task<IActionResult> CreateOrUpdateManyPreferenceAsync([FromBody] UserPreferenceBody model) =>
            await Send(new UpdateUserPreferenceCommandRequest(model));

        [HttpDelete("{key}", Name = "DeletePreference")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
        public async Task<IActionResult> DeletePreferenceAsync(string key)
        {
            var request = new DeleteUserPreferenceCommandRequest(key);
            var validationResult = await deleteUserPreferenceCommandRequestValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            return await Send(request);
        }
    }
}