﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Queries.GoodData;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.GoodData.Models.Workspaces;
using System.Threading.Tasks;

namespace Axon.Core.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("v{version:apiVersion}/GoodData/app/{appCodeName}/organisation/{orgCodeName}")]
[ProducesResponseType(401, Type = typeof(CommandResponse))]
[ProducesResponseType(403, Type = typeof(CommandResponse))]
[ProducesResponseType(500, Type = typeof(CommandResponse))]
[Authorize]
public class GoodDataController(IMediator mediator) : ApiControllerBase(mediator)
{
    [HttpGet(Name = "GetWorkspaceWithDashboards")]
    [ProducesResponseType(200, Type = typeof(CommandResponse<WorkspaceDetails>))]
    [ProducesResponseType(400, Type = typeof(CommandResponse<WorkspaceDetails>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse<WorkspaceDetails>))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetWorkspaceWithDashboardsAsync(string appCodeName, string orgCodeName)
    {
        return await Send(new GetWorkspaceWithDashboardsQueryRequest(appCodeName, orgCodeName));
    }
}
