using AutoMapper;
using Axon.Core.Api.Commands.Organisation.Update;
using Axon.Core.Api.Services.FileUpload;
using Axon.Core.Api.Validators.FileUploads;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services.AzureBlobStorage;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.App.Update;

internal class UpdateAppAvatarCommandHandler : BaseEntityImageUpdateCommandHandler<UpdateAppAvatarCommandRequest, AppEntity, AppUpdated>
{
    private static EntityImageRequirements imageRequirements = new EntityImageRequirements { MaxFileSizeInBytes = 1048576, TargetHeightInPixels = 50, TargetWidthInPixels = 50, ShouldBeSquare = true };

    public UpdateAppAvatarCommandHandler(IAppRepository repo,
                                         IMapper mapper,
                                         IMessageBus messageBus,
                                         IAppAvatarAzureBlobStorageManager appAvatarAzureBlobStorageManager,
                                         IUpdateImageValidator updateImageValidator,
                                         IImageResizer imageResizer,
                                         ILogger<UpdateAppAvatarCommandHandler> logger) :
        base(repo, mapper, messageBus, appAvatarAzureBlobStorageManager, updateImageValidator, imageResizer, imageRequirements, logger)
    {
    }

    protected override string GetEntityCodeName(AppEntity entity)
    {
        return entity.AppCodeName;
    }

    protected override void AppendImageUrlToEntity(AppEntity entity, string blobUri)
    {
        if (entity.Theme != null)
        {
            entity.Theme.AvatarUrl = blobUri;
        }
        else
        {
            entity.Theme = new AppThemeConfigEntity
            {
                AvatarUrl = blobUri
            };
        }
    }
}