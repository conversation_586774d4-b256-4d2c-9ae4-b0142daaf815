﻿using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Infrastructure.Auth;
using CommunityToolkit.Diagnostics;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using System;
using System.Linq;
using Axon.Core.Api.Services.Authorisation;

namespace Axon.Core.Api.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
    public abstract class HasAuthorizationBaseAttribute : Attribute, IAsyncAuthorizationFilter, IAuthorizationFilter
    {

        protected class ValidatedClaims
        {
            public required IReadOnlyCollection<Claim> Claims { get; init; }
            public string OidClaim { get; init; }
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            await Authorise(context);
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            Authorise(context).Wait();
        }

        protected abstract Task Authorise(AuthorizationFilterContext context);

        protected (ValidatedClaims claimValues, IActionResult errorResponse) TryGetStandardClaims(AuthorizationFilterContext context, ILogger logger)
        {
            var claims = context.HttpContext.User.Claims.ToList();
            var oidClaim = claims.ObjectId();

            if ((!claims.ContainsAppClaims() && string.IsNullOrEmpty(oidClaim)))
            {
                logger.LogDebug("Auth failed: The sub/nameidentifier `{OidClaim}` claim are empty", oidClaim);
                return (null, new UnauthorizedResult());
            }


            return (new ValidatedClaims { Claims = claims, OidClaim = oidClaim }, null);
        }

        protected static (IServiceProvider serviceProvider, IAccessGate accessGate, IAuthenticationSchemeProvider authenticationSchemeProvider, ILogger logger) GetStandardServices<T>(AuthorizationFilterContext context)
        {
            var services = context.HttpContext.RequestServices;
            Guard.IsNotNull(services);

            var accessGate = services.GetService(typeof(IAccessGate)) as IAccessGate;
            Guard.IsNotNull(accessGate);

            var authenticationSchemeProvider = services.GetService(typeof(IAuthenticationSchemeProvider)) as IAuthenticationSchemeProvider;
            Guard.IsNotNull(authenticationSchemeProvider);

            var logger = GetLogger<T>(services);

            return (services, accessGate, authenticationSchemeProvider, logger);
        }
        protected static ILogger GetLogger<T>(IServiceProvider services)
        {
            var logFactory = services.GetService(typeof(ILoggerFactory)) as ILoggerFactory;
            Guard.IsNotNull(logFactory);
            var logger = logFactory.CreateLogger(typeof(ILogger<T>));
            Guard.IsNotNull(logger);
            return logger;
        }
    }
}
