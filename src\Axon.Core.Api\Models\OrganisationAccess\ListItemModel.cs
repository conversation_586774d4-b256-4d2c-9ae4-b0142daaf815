﻿using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.OrganisationAccess;

public class ListItemModel
{
    public ListItemModel(string identityProviderId, string name, string email)
    {
        IdentityProviderId = identityProviderId;
        Name = name;
        Email = email;
    }

    public ListItemModel()
    {
            
    }

    [Required]
    public string IdentityProviderId { get; init; }

    [Required]
    public string Name { get; init; }

    [Required]
    public string Email { get; init; }
}
