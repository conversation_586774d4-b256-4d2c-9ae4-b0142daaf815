﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Services.Audit;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using JetBrains.Annotations;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.Audit
{
    [UsedImplicitly]
    internal class GetAuditsUrlQueryRequestHandler : IRequestHandler<GetAuditsUrlQueryRequest, CommandResponse>
    {
        public IAppRepository AppRepository;
        public IOrganisationRepository OrganisationRepository;
        public IConfiguration Config;
        public IAuditEndpointService AuditODataEndpointService;
        private readonly ILogger<GetAuditsUrlQueryRequestHandler> Logger;

        public GetAuditsUrlQueryRequestHandler(IOrganisationRepository organisationRepository,
            IAppRepository appRepository,
            IConfiguration config,
            IAuditEndpointService auditODataEndpointService,
            ILogger<GetAuditsUrlQueryRequestHandler> logger)
        {
            Guard.IsNotNull(organisationRepository);
            OrganisationRepository = organisationRepository;
            Guard.IsNotNull(appRepository);
            AppRepository = appRepository;
            Guard.IsNotNull(auditODataEndpointService);
            AuditODataEndpointService = auditODataEndpointService;
            Guard.IsNotNull(logger);
            Logger = logger;
            Guard.IsNotNull(config);
            Config = config;
        }

        public async Task<CommandResponse> Handle(GetAuditsUrlQueryRequest request, CancellationToken cancellationToken)
        {
            Logger.LogDebug("Entered GetAuditsUrlQueryRequestHandler, orgCodeName:{OrgCodeName} appCodeName:{AppCodeName}", request.OrgCodeName,request.AppCodeName);

            //axon core isn't a "real" app so won't have app settings etc
            if (request.AppCodeName != AppNameConstants.AxonCoreCodeName)
            {
                var securityResponse = await CheckOrgAndAppSecurity(request);
                if (securityResponse.status != HttpStatusCode.OK)
                {
                    return securityResponse;
                }

                var appEntity = await AppRepository.GetItemByAppCodeNameAsync(request.AppCodeName);

                if (!appEntity.ShowAudits)
                {
                    Logger.LogError("Auditing is not enabled for this application: {AppCodeName}.", request.AppCodeName);
                    return new CommandResponse() { status = HttpStatusCode.Forbidden, title = "Not available on this application" };
                }
            }
            
            var oDataUrl = await AuditODataEndpointService.GetOdataUrl(request);
            if (oDataUrl.status != HttpStatusCode.OK)
                return oDataUrl;

            Logger.LogDebug("Exiting GetAuditsUrlQueryRequestHandler, orgCodeName:{OrgCodeName} appCodeName:{AppCodeName}", request.OrgCodeName, request.AppCodeName);
            return CommandResponse.Data(data: oDataUrl.data);

        }

        private async Task<CommandResponse> CheckOrgAndAppSecurity(GetAuditsUrlQueryRequest request)
        {
            if(await AppRepository.GetItemByAppCodeNameAsync(request.AppCodeName) == null) return CommandResponse.NotFound("app", request.AppCodeName);

            var org = await OrganisationRepository.GetItemByCodeNameAsync(request.OrgCodeName);
            if (org == null)
            {
                return CommandResponse.NotFound("organisation", request.OrgCodeName);
            }

            var appExistsForOrg = org.Apps.Any(item => item.AppCodeName.Equals(request.AppCodeName, StringComparison.InvariantCulture));
            if (!appExistsForOrg) return CommandResponse.Failed("AppCodeName", "Requested Application doesn't belong to organisation.", status:HttpStatusCode.BadRequest);

            return CommandResponse.Success();
        }
    }
}
