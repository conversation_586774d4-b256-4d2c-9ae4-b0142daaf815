﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppSetting;
using Axon.Core.Api.Services.Settings;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services.KeyVault;
using MediatR;

namespace Axon.Core.Api.Queries.AppOrganisationSettings;

internal class GetOrganisationAppPublicSettingsQueryHandler : IRequestHandler<GetOrganisationAppPublicSettingsQueryRequest, CommandResponse<SettingsValueModel[]>>
{
    private readonly IAppOrganisationSettingsRepository appOrganisationSettingsRepository;
    private readonly IAppSettingsRepository appSettingsRepository;
    private readonly ISettingsProvider settingsProvider;

    public GetOrganisationAppPublicSettingsQueryHandler(
        IAppSettingsRepository appSettingsRepository,
        IAppOrganisationSettingsRepository appOrganisationSettingsRepository,
        ISettingsProvider settingsProvider)
    {
        this.appSettingsRepository = appSettingsRepository;
        this.appOrganisationSettingsRepository = appOrganisationSettingsRepository;
        this.settingsProvider = settingsProvider;
    }

    public async Task<CommandResponse<SettingsValueModel[]>> Handle(GetOrganisationAppPublicSettingsQueryRequest request, CancellationToken cancellationToken)
    {
        var appSettings = await appSettingsRepository.GetByAppCodeNameAsync(request.AppCodeName);
        if (appSettings == null)
            return CommandResponse<SettingsValueModel[]>.Data(Array.Empty<SettingsValueModel>());

        var appSettingsValues = await appOrganisationSettingsRepository.GetAppOrganisationSettingsAsync(request.OrgCodeName, request.AppCodeName);

        var result = appSettings.Settings.Where(x=> x.Value.IsPublic && !x.Value.IsKeyVault)
            .Select(x => settingsProvider.GetSetting(x, appSettingsValues, null, request.OrgCodeName))
            .ToArray();

        return CommandResponse<SettingsValueModel[]>.Data(result);
    }
}