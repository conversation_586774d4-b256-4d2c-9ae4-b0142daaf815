﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xmlns:sl="http://www.nlog-project.org/schemas/NLog.Targets.Syslog.xsd"
      autoReload="true"
      internalLogLevel="info"
      internalLogFile="/app/logs/internal-nlog.log">

	<!-- enable asp.net core layout renderers -->
	<extensions>
		<add assembly="NLog.Web.AspNetCore"/>
	</extensions>

	<!-- the targets to write to -->
	<targets>
		<!-- IMPORTANT: Console JSON output is picked up by new relic in production environments, we log the json
			to a file in dev etc.
		-->
		<target xsi:type="File" name="new-relic-json" fileName="/app/logs/api-${shortdate}.json.log">
			<layout type="JsonLayout" includeMdlc="true">
				<attribute name="time" layout="${longdate}" />
				<attribute name="level" layout="${uppercase:${level}}" />
				<attribute name="traceId" layout="${activityid:whenEmpty=${mdlc:item=RequestId:whenEmpty=${aspnet-TraceIdentifier}}}" />
				<attribute name="threadId" layout="${threadid}" />
				<attribute name="logger" layout="${logger}" />
				<attribute name="errorCode" layout="${event-properties:item=ErrorCode}" />
				<attribute name="errorClass" layout="${exception:format=Type}" />
				<attribute name="errorMessage" layout="${exception:format=Message}" />
				<attribute name="errorStack" layout="${exception:format=StackTrace}" />
				<attribute name="innerException" layout="${exception:maxInnerExceptionLevel=5:innerFormat=shortType,message,method}" />
				<attribute name="message" layout="${event-properties:item=Message:whenEmpty=${message}}" />
				<attribute name="applicationName" layout="${appdomain:format={1\}}" />
				<attribute name="area" layout="${event-properties:item=Area:whenEmpty=${logger}}" />
				<!-- New Relic Agents logs in context specific attributes -->
				<attribute name="entity.name" layout="${gdc:item=entity.name}" />
				<attribute name="entity.type" layout="${gdc:item=entity.type}" />
				<attribute name="entity.guid" layout="${gdc:item=entity.guid}" />
				<attribute name="hostname" layout="${gdc:item=hostname" />
			</layout>
		</target>

		<!-- IMPORTANT: File output is only used by devs locally so is in a more human readable format.
				NOTE: new lines in the layout is ignored (use ${newline}),
				whitespace is not ignored and is used for indentation in some layouts below
				see appsettings.json for per namespace filtering options
				
				NOTE: we log exactly the same format to the logs folder as well, 
				console windows can be hard to search!
		-->

		<target xsi:type="Console" name="human-readable-detailed-console">
			<layout type="JsonLayout" includeMdlc="true">
				<attribute name="time" layout="${longdate}" />
				<attribute name="level" layout="${uppercase:${level}}" />
				<attribute name="traceId" layout="${activityid:whenEmpty=${mdlc:item=RequestId:whenEmpty=${aspnet-TraceIdentifier}}}" />
				<attribute name="threadId" layout="${threadid}" />
				<attribute name="logger" layout="${logger}" />
				<attribute name="errorCode" layout="${event-properties:item=ErrorCode}" />
				<attribute name="errorClass" layout="${exception:format=Type}" />
				<attribute name="errorMessage" layout="${exception:format=Message}" />
				<attribute name="errorStack" layout="${exception:format=StackTrace}" />
				<attribute name="innerException" layout="${exception:maxInnerExceptionLevel=5:innerFormat=shortType,message,method}" />
				<attribute name="message" layout="${event-properties:item=Message:whenEmpty=${message}}" />
				<attribute name="applicationName" layout="${appdomain:format={1\}}" />
				<attribute name="area" layout="${event-properties:item=Area:whenEmpty=${logger}}" />
				<!-- New Relic Agents logs in context specific attributes -->
				<attribute name="entity.name" layout="${gdc:item=entity.name}" />
				<attribute name="entity.type" layout="${gdc:item=entity.type}" />
				<attribute name="entity.guid" layout="${gdc:item=entity.guid}" />
				<attribute name="hostname" layout="${gdc:item=hostname" />
			</layout>
		</target>


		<target xsi:type="File" name="human-readable-detailed-file" fileName="/app/logs/axon-api-${shortdate}.log"
		        layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|CorrelationId=${aspnet-item:variable=CorrelationId}|ClientIP=${aspnet-item:variable=ClientIP}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}" />
	</targets>

	<!-- rules to map from logger name to target -->
	<rules>
		<!-- the minLevel is filters at the lowest level that will be output setting higher than `Trace` will stop some
		LogLevels being logged at all.
	  -->
		<logger name="System.*" finalMinLevel="Warn" />
		<logger name="Microsoft.*" finalMinLevel="Warn" />
		<logger name="Microsoft.Hosting.Lifetime*" finalMinLevel="Info" />
		<logger name="*" minlevel="Trace" writeTo="human-readable-detailed-console" />
		<logger name="*" minlevel="Trace" writeTo="human-readable-detailed-file" />
		<!-- <logger name="*" minlevel="Trace" writeTo="human-readable-compact" /> -->
	</rules>


</nlog>