﻿using Microsoft.AspNetCore.Mvc.Formatters;
using System.IO;
using System.Text.Json;
using System.Text;
using System.Threading.Tasks;
using System;

namespace Axon.Core.Api.Formatters;

public class SafeJsonInputFormatter : TextInputFormatter
{
    public SafeJsonInputFormatter()
    {
        SupportedMediaTypes.Add("application/json");
        SupportedEncodings.Add(Encoding.UTF8);
        SupportedEncodings.Add(Encoding.Unicode);
    }

    public override async Task<InputFormatterResult> ReadRequestBodyAsync(InputFormatterContext context, Encoding encoding)
    {
        try
        {
            using var reader = new StreamReader(context.HttpContext.Request.Body, encoding);
            var body = await reader.ReadToEndAsync();
            var result = JsonSerializer.Deserialize(body, context.ModelType, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                AllowTrailingCommas = true
            });
            return await InputFormatterResult.SuccessAsync(result);
        }
        catch (JsonException)
        {
            context.ModelState.AddModelError("RequestBody", "The request payload is invalid or malformed.");
            return await InputFormatterResult.FailureAsync();
        }
    }

    protected override bool CanReadType(Type type)
    {
        return true;
    }
}