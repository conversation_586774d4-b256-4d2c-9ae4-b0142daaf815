﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Contracts;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Access;
using Axon.Core.Domain.Services.GoodData;
using MassTransit;
using Microsoft.Extensions.Logging;
using Phlex.Core.GoodData.Interfaces;
using Phlex.Core.GoodData.Models.Users;

namespace Axon.Core.Api.EventHandlers.GoodData;

public class AppUserUpdatedEventHandler(
    IUserRepository userRepository,
    IRoleDefinitionRepository roleDefinitionRepository,    
    IGoodDataService goodDataService,
    IAccessService accessService,
    IGoodData goodData,
    ILogger<AppUserUpdatedEventHandler> logger)
    : IConsumer<AppUserUpdatedEvent>
{

    public async Task Consume(ConsumeContext<AppUserUpdatedEvent> context)
    {
        var message = context.Message;
        if (message.AppCodeName == AppNameConstants.AxonCoreCodeName || !await goodDataService.IsGoodDataReportingEnabled(message.AppId))
        {
            return;
        }

        var user = await userRepository.GetItemAsync(message.UserId);
        if (user == null)
        {
            logger.LogWarning("User {UserId} does not exist.", message.UserId);
            return;
        }

        var roleDefinition = await roleDefinitionRepository.GetItemByAppCodeAsync(message.AppCodeName);
        if (roleDefinition == null)
        {
            logger.LogWarning("Role Definition for application {AppCodeName} cannot be found.", message.AppCodeName);
            return;
        }

        var userEffectivePermissions = await accessService.GetEffectivePermissions(user.Email, message.AppCodeName, message.OrgCodeName);

        var goodDataPermission = userEffectivePermissions.EffectivePermissions.Permissions.SingleOrDefault(p => p.Allow
                                                                                                                && p.HasScopes
                                                                                                                && string.Equals(p.Name, roleDefinition.GoodDataPermission, System.StringComparison.OrdinalIgnoreCase));

        if (goodDataPermission == null)
        {
            // user does not have permission to access anything in GoodData, need to revoke access for existing users or skip user creation for new users
            if (message.Action is not (EventActionType.Updated or EventActionType.Deleted)) return;

            await goodData.RevokeAppTenantUserAccessAsync(user.Email, message.AppCodeName, message.OrgCodeName);
            logger.LogInformation("User {User} access revoked for {AppCodeName}-{OrgCodeName} in GoodData.", user.Email, message.AppCodeName, message.OrgCodeName);
            return;
        }

        var userDataFilterSets = GenerateUserDataFilterSets(goodDataPermission);
        var model = new AppTenantUser()
        {
            User = new UserDetails()
            {
                Email = user.Email
            },
            ApplicationName = message.AppCodeName,
            OrganisationName = message.OrgCodeName,
            UserDataFilterSets = userDataFilterSets
        };

        await goodData.CreateOrUpdateAppTenantUserAsync(model);
        logger.LogInformation("User {User} was successfully created/updated in GoodData.", user.Email);
    }

    private List<UserDataFilterSet> GenerateUserDataFilterSets(UserEffectivePermission permission)
    {
        var userDataFilterSets = new List<UserDataFilterSet>();
        foreach (var set in permission.ScopeSets)
        {
            var userDataFilters = new List<UserDataFilter>();
            foreach (var scope in set.Scopes)
            {
                if (scope.AccessLevel == ScopeAccessLevel.PartialAccess)
                {
                    userDataFilters.Add(new UserDataFilter()
                    {
                        Scope = scope.Scope,
                        Resources = scope.Resources.Select(x => x.Name).ToArray()
                    });
                }
                else if (scope.AccessLevel == ScopeAccessLevel.NoAccess)
                {
                    userDataFilters.Add(new UserDataFilter()
                    {
                        Scope = scope.Scope,
                        Resources = null
                    });
                }
            }
            if (userDataFilters.Count != 0)
            {
                userDataFilterSets.Add(new UserDataFilterSet()
                {
                    UserDataFilters = userDataFilters
                });
            }
        }

        return userDataFilterSets;
    }
}