# NB. THe OpenApi generator tool
#  If needed, update the link to the latest build of the version (so currently, the latest 7.4.0 build)
Param(
	[String]$JREdownloadUrl = "https://javadl.oracle.com/webapps/download/AutoDL?BundleId=242990_a4634525489241b9a9e1aa73d9e118e6", 
	[String]$OpenApiGeneratorDownloadUrl = "https://repo1.maven.org/maven2/org/openapitools/openapi-generator-cli/7.6.0/openapi-generator-cli-7.6.0.jar",
	[switch]$DisableSDKBuild
)
	
function SetupPrerequisites()
{
	if (Get-Command java | Select-Object Version) 
	{
		Write-Host 'Java already installed.'  -ForegroundColor black -BackgroundColor green
	}
	else 
	{
		Write-Host "Java not found." -ForegroundColor red -BackgroundColor black
		DownloadAndInstallJava	
	}

	$openapiCheckResult = Test-Path ".\openapi-generator-cli.jar"
	if ($openapiCheckResult)
	{
		Write-Host "openapi-generator-cli.jar found." -ForegroundColor black -BackgroundColor green
	}
	else
	{
		Write-Host "Openapi-generator-cli not found. Downloading." -ForegroundColor red -BackgroundColor black
		try { 
			Invoke-WebRequest -OutFile openapi-generator-cli.jar $OpenApiGeneratorDownloadUrl
		}
		catch { 
			Write-Host "Error downloading openapi generator, check\n $OpenApiGeneratorDownloadUrl" 
			exit
		}
	
	}
	
	if (dotnet tool list | Select-String "swagger") 
	{
		Write-Host 'Swagger already installed.' -ForegroundColor black -BackgroundColor green
	}
	else 
	{
		Write-Host "Swagger not found. Installing." -ForegroundColor red -BackgroundColor black
		dotnet new tool-manifest
		dotnet tool install --version 6.9.0 Swashbuckle.AspNetCore.Cli --ignore-failed-sources
	}

	if(!$DisableSDKBuild) {
		# Ensure the API has been rebuilt, disable the enhanced documentation when building SDKs as it causes problems
		Write-Host "Cleaning Axon.Core.API for SDK generation"
		dotnet clean ..\src\Axon.Core.Api\Axon.Core.Api.csproj
		Write-Host "Rebuilding Axon.Core.API for SDK generation"
		dotnet build /p:DefineConstants="GENERATING_SDKS" ..\src\Axon.Core.Api\Axon.Core.Api.csproj 
	}
}
function CheckForError([string]$message) {
    if($LASTEXITCODE)  
    { 
        Write-Host $message
        exit
     }
}

#returns X:\my\path\Phlex.Name.Api\src\Phlex.Name.Api.Sdk.NetCore
#projectsuffix is ".Sdk.NetCore" or ".Sdk.Typescript"
function GetPathForSdk()
{
	Param($projectsuffix)
	$path = $PSScriptRoot.Split("\") | Select-Object -SkipLast 1
	$path = $path -join "\"

	$projectname = GetPackageNameFromPath $PSScriptRoot 
	$projectname = $projectname + $projectsuffix

	$projectpath = $path + "\src\" + $projectname

	return $projectpath
}

#returns Phlex.Name.Api from X:\my\path\Phlex.Name.Api\scripts
function GetPackageNameFromPath()
{
	Param($path)
	$path = $path.Split("\") | Select-Object -SkipLast 1
	$path = $path -join "\"
	$path = $path + "\src\"

	$packagepath = (Get-ChildItem -Path $path -Filter "*.Api*" -Recurse -Directory).Fullname | Select-Object -First 1

	$packagename = $packagepath.Split("\") | Select-Object -Last 1
	$packagename = $packagename -join "\"

	return $packagename
}

function GenerateSwagger()
{
	$path = $PSScriptRoot.Split("\") | Select-Object -SkipLast 1
	$path = $path -join "\"
	
	$packagename = GetPackageNameFromPath $PSScriptRoot

	$path = $path + "\src\" + $packagename + "\bin\Debug"
	$depsjson = $packagename + ".deps.json"
	
	if (-Not (Test-Path -Path $path))
	{
		Write-Host "$path does not exist. Please build the API project." -ForegroundColor red -BackgroundColor black
		Exit
	}

	$depsjsonpath = Get-ChildItem -Path $path -Recurse $depsjson | Sort CreationTime -Descending | Select-Object -Property FullName -First 1
	$depsjsonfullname = $depsjsonpath.FullName
	
	if (($null -eq $depsjsonfullname) -Or (-Not (Test-Path -Path $depsjsonfullname)))
	{
		Write-Host "$depsjson does not exist in $path. Please build the API project." -ForegroundColor red -BackgroundColor black
		Exit
	}
	
	$parts = $depsjsonfullname.Split(".") | Select-Object -SkipLast 2
	$parts = $parts -join "."
	$dllpath = $parts + ".dll"
	
	if (-Not (Test-Path -Path $dllpath))
	{
		Write-Host "$packagename.dll does not exist in $dllpath." -ForegroundColor red -BackgroundColor black
		Exit
	}

	CreateLocalAppSettingsForSwaggerGen

	Write-Host "Generating swagger.json from  " $dllpath
    dotnet swagger tofile --output swagger.json $dllpath v1
	Remove-Item -Force ".\appsettings.json"
}
function CreateLocalAppSettingsForSwaggerGen() {
	# copy settings from appsettings.development.json to the local appsettings.json created by this script
	# Swagger gen will fail if the app can't initialise properly, which will happen if theres is no ConnectionString or MessageBus config available.
	# Swagger fires the app up but doesn't use the enhanced configuration reading we use (namely getting values from appsettings.development)
	# so we manually do that here

	$appsettings = GetJson $path "appsettings.json"
	$appsettingsdev = GetJson $path  "appsettings.development.json"

	CopyToAppSettings $appsettings $appsettingsdev "ConnectionStrings"
	CopyToAppSettings $appsettings $appsettingsdev "MessageBus"

	$appsettings | ConvertTo-Json -Depth 100 | Out-File ".\appsettings.json"
}

function CopyToAppSettings(
	[object]$appsettings,
	[object]$appsettingsdev,
	[object]$section
){
	# Copies a section of settings from appsettingsdev to appsettings if it doesn't already exist.

	if ($null -ne $appsettings.$section)
	{
		Write-Warning "Root level appSettings already contains a [$section] section ignoring any values from appSettings.development.sjon"
	}
	else {
		Write-Host "Checking for appsettings.development.json"
		if ($null -eq $appsettingsdev.$section)
		{
			Write-Error "No [$section] section found in appSettings.json or appSettings.development.json, unable to continue as the Swagger gen will fail. This is due to swagger running the app, but not setting up a configuration path that checks for appsettings.development.json"
			exit
		}	
		Write-Host "[$section] found in development settings, copying to local appsettings"
		$appsettings | Add-Member -Name $section -value $appsettingsdev.$section -MemberType NoteProperty
	}

	$appsettings
}

# Load some json and return a JSON object
function GetJson(
	[String]$path,
	[String]$file

) {
	$appsettingsjson = $file
	$appsettingsjsonpath = Get-ChildItem -Path $path -Recurse $appsettingsjson | Sort CreationTime -Descending | Select-Object -Property FullName -First 1
	$appsettingsjsonfullname = $appsettingsjsonpath.FullName

	if (($null -eq $appsettingsjsonfullname) -Or (-Not (Test-Path -Path $appsettingsjsonfullname)))
	{
		Write-Host "$appsettingsjson does not exist in $appsettingsjsonfullname." -ForegroundColor red -BackgroundColor black
		Exit
	}
	Write-Host "about to convert $appsettingsjson from $appsettingsjsonfullname."
	(Get-Content $appsettingsjsonfullname -Raw) | ConvertFrom-Json
}

function GenerateNetCoreWithOpenApi()
{
	$packagename = GetPackageNameFromPath $PSScriptRoot
	$packagename = $packagename + ".Sdk.NetCore"
	$path = GetPathForSdk ".Sdk.NetCore"
	$path = $path + "\temp"
	
	Write-Host "Generating NetCore files with OpenApi" -ForegroundColor black -BackgroundColor green
	
	Write-Host "java -jar openapi-generator-cli.jar generate -i swagger.json -g csharp-netcore -o $path --additional-properties=netCoreProjectFile=true,packageName=$packagename,targetFramework=net8.0 --library httpclient"
	java -jar openapi-generator-cli.jar generate -i swagger.json -g csharp -o $path --additional-properties=netCoreProjectFile=true,packageName=$packagename,targetFramework=net8.0 --library httpclient
	
	Write-Host "Generating NetCore finished" -ForegroundColor black -BackgroundColor green
}

function GenerateTypeScriptWithOpenApi()
{
	$packagename = GetPackageNameFromPath $PSScriptRoot
	$path = GetPathForSdk ".Sdk.Typescript"
	$path = $path + "\temp"
	
	Write-Host "Generating TypeScript files with OpenApi" -ForegroundColor black -BackgroundColor green
	
	java -jar openapi-generator-cli.jar generate -i swagger.json -g typescript-axios -o $path --skip-validate-spec --additional-properties=netCoreProjectFile=true,packageName=$packagename,targetFramework=net8.0 --global-property apis,models,supportingFiles,apiTests=false,apiDocs=false,modelTests=false,modelDocs=false
	
	Write-Host "Generating TypeScript finished" -ForegroundColor black -BackgroundColor green
}

function RemoveFiles()
{
	Write-Host 'Removing redundant files'
	
	#remove from sdk netcore
	$packagename = GetPackageNameFromPath $PSScriptRoot
	$packagename = $packagename + ".Sdk.NetCore"
	$targetDir = GetPathForSdk ".Sdk.NetCore"
	$path = $targetDir + "\temp\src\" + $packagename + "\*"
	Copy-Item -Path $path -Destination $targetDir -Force -Recurse
	$removesrc = $targetDir + "\temp"
	Remove-Item -Recurse -Force $removesrc
	
	#remove from sdk typescript
	$targetDir = GetPathForSdk ".Sdk.Typescript"
	
	$path = $targetDir + "\temp\*.ts"
	Copy-Item -Path $path -Destination $targetDir -Force -Recurse
	$removesrc = $targetDir + "\temp"
	Remove-Item -Recurse -Force $removesrc
	
	Write-Host 'Removing files finished'
}

function DownloadAndInstallJava()
{
	#download java
	Write-Host "Downloading Java. Please wait." -ForegroundColor black -BackgroundColor green

	Invoke-WebRequest -UseBasicParsing $JREdownloadUrl -OutFile jre.exe
	
	$path = $PSScriptRoot + "\jre.exe"
	Write-Host "Installing Java. Please wait." -ForegroundColor black -BackgroundColor green
	
	try
	{
		Start-Process -FilePath $path -passthru -wait -argumentlist /s
		Write-Host "Java installed successfully" -ForegroundColor black -BackgroundColor green
	}
	catch
	{
		Write-Host "Error occured while installing Java." -ForegroundColor red -BackgroundColor black
		Exit
	}
	finally
	{
		Write-Host "Removing instalation file." -ForegroundColor black -BackgroundColor green
		Remove-Item -Path $path -Force
	}	
}

SetupPrerequisites
GenerateSwagger
GenerateNetCoreWithOpenApi
GenerateTypeScriptWithOpenApi
RemoveFiles