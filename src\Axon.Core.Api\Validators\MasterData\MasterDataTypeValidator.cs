﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Axon.Core.Api.Validators.MasterData
{
    public class MasterDataTypeValidator : IMasterDataTypeValidator
    {
        private readonly List<string> validMasterDataTypes =
        [
            "Country"
        ];

        public Task<(bool isValid, string errorMessage)> Validate(string masterDataType)
        {
            if (masterDataType == null)
            {
                return Task.FromResult((false, "MasterDataType cannot be null"));
            }

            if (validMasterDataTypes.Any(x => x.Equals(masterDataType, StringComparison.OrdinalIgnoreCase)))
            {
                return Task.FromResult((true, string.Empty));
            }

            return Task.FromResult((false, $"MasterDataType '{masterDataType}' is not valid. Valid types are: {string.Join(", ", validMasterDataTypes)}"));
        }
    }
}
