﻿using System.Collections.Generic;
using Axon.Core.Api.Models.AppSetting;
using MediatR;

namespace Axon.Core.Api.Commands.AppMasterData.UpdateAppMasterData
{
    public class UpdateAppOrganisationMasterDataCommandRequest : IRequest<CommandResponse>
    {
        public UpdateAppOrganisationMasterDataCommandRequest(string organisationCodeName, string appCodeName, string masterDataType, IReadOnlyCollection<MasterDataSelectedValue> selected)
        {
            OrganisationCodeName = organisationCodeName;
            AppCodeName = appCodeName;
            MasterDataType = masterDataType;
            Selected = selected;
        }

        public string OrganisationCodeName { get; }
        public string AppCodeName { get; }
        public string MasterDataType { get; }
        public IReadOnlyCollection<MasterDataSelectedValue> Selected { get; }
    }
}
