﻿using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Infrastructure.Auth;
using Axon.Core.Shared.Services.Users;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Axon.Core.Shared.Services.Auth;
using Axon.Core.Infrastructure.Cosmos.Extensions;
using Microsoft.Extensions.Logging;

namespace Axon.Core.Api.Services.Authorisation
{

    internal sealed class AuthenticationService : IAuthenticationService
    {
        private readonly IHttpContextAccessor contextAccessor;
        private readonly IOidcUserIdentityService oidcUserIdentityService;
        private readonly IUserRepository userRepository;
        private readonly ISessionRepository sessionRepository;
        private readonly IUserOnboardingService userOnboardingService;
        private readonly ICookieAuthenticationHttpWrapper cookieAuthenticationHttpWrapper;
        private readonly ILogger<AuthenticationService> logger;

        public AuthenticationService(IHttpContextAccessor contextAccessor,
                                     IOidcUserIdentityService oidcUserIdentityService,
                                     IUserRepository userRepository,
                                     IUserOnboardingService userOnboardingService,
                                     ICookieAuthenticationHttpWrapper cookieAuthenticationHttpWrapper,
                                     ISessionRepository sessionRepository,
                                     ILogger<AuthenticationService> logger)
        {
            this.contextAccessor = contextAccessor;
            this.oidcUserIdentityService = oidcUserIdentityService;
            this.userRepository = userRepository;
            this.userOnboardingService = userOnboardingService;
            this.cookieAuthenticationHttpWrapper = cookieAuthenticationHttpWrapper;
            this.sessionRepository = sessionRepository;
            this.logger = logger;
        }

        public async Task<UserIdentityDetails> Authenticate(string accessToken)
        {
            //Its possible a user has a valid cookie, if that is the case we do not want to allow them to authenticate only with an existing one
            //They should be providing a valid jwt token instead.
            var nonCookieIdentity = contextAccessor.HttpContext.User.Identities.SingleOrDefault(x => x.IsAuthenticated && x.AuthenticationType != "Cookies");
            if (nonCookieIdentity == null)
            {
                throw new UnauthorizedAccessException();
            }

            //Identify the user based on their claims and infomation provided by the oidc user info endpoint
            var user = await oidcUserIdentityService.GetAsync(nonCookieIdentity.Claims, accessToken);

            //onboard user
            var onboardResponse = await userOnboardingService.Onboard(nonCookieIdentity.Claims, user.Email);

            if (!onboardResponse.status.IsSuccessStatusCode())
            {
                throw new UnauthorizedAccessException();
            }

            var newSessionId = Guid.NewGuid();

            List<Claim> claims = [
                new Claim(Shared.Auth.AxonClaimTypes.Email, user.Email),
                new Claim(Shared.Auth.AxonClaimTypes.Name, user.Name),
                new Claim(Shared.Auth.AxonClaimTypes.Issuer, nonCookieIdentity.Claims.Issuer()),
                new Claim(Shared.Auth.AxonClaimTypes.Subject, nonCookieIdentity.Claims.ObjectId()),
                new Claim(Shared.Auth.AxonClaimTypes.UserId, $"{user.Id}"),
                new Claim(Shared.Auth.AxonClaimTypes.UserMigrationVersion, $"{MigrationConstants.LatestMigrationVersion:F1}"),
                new Claim(Shared.Auth.AxonClaimTypes.UserSessionId, $"{newSessionId}")
            ];

            var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);

            var expiry = DateTimeOffset.UtcNow.Add(user.IdentityProviderDetails.SessionDetails.SessionDuration);
            var authProperties = new AuthenticationProperties
            {
                ExpiresUtc = expiry,
                IssuedUtc = DateTimeOffset.UtcNow,
                AllowRefresh = true
            };

            contextAccessor.HttpContext.Response.Cookies.Append("AxonAuthenticationExpiry",
                                                                $"{expiry.ToString("o")}",
                                                                new CookieOptions
                                                                {
                                                                    HttpOnly = false,
                                                                    Secure = true,
                                                                    SameSite = SameSiteMode.Strict,
                                                                    Expires = expiry
                                                                });

            await cookieAuthenticationHttpWrapper.SignInAsync(contextAccessor.HttpContext,
                                                              new ClaimsPrincipal(claimsIdentity),
                                                              authProperties);

            await userRepository.UpdateLastAccessed(user.Id, DateTime.UtcNow);
            await sessionRepository.CreateNewSession(newSessionId, user.Id, DateTime.UtcNow);

            return user;
        }

        public async Task SignOut()
        {
            var userId = contextAccessor.HttpContext.User.Claims.SingleOrDefault(x => x.Type.Equals(Shared.Auth.AxonClaimTypes.UserId, StringComparison.OrdinalIgnoreCase));
            var sessionId = contextAccessor.HttpContext.User.Claims.SingleOrDefault(x => x.Type.Equals(Shared.Auth.AxonClaimTypes.UserSessionId, StringComparison.OrdinalIgnoreCase));

            if(sessionId != null && userId != null)
            {
                logger.LogInformation("Expiring session: {sessionId}", sessionId.Value);
                await sessionRepository.ExpireSession(Guid.Parse(sessionId.Value), userId.Value);
            }
            else
            {
                //This should not happen as we issue the cookie and will ensure these are always provided.
                //The cookie validation check would also ensure both these claims are present before the user reaches this point.
                //Checking here as it is preferrable to log the user out still instead of throwing if it does occurr.
                logger.LogWarning("Could not find session and user claim for db session expiration, user will still be signed out");
            }

            await cookieAuthenticationHttpWrapper.SignOutAsync(contextAccessor.HttpContext);
            contextAccessor.HttpContext.Response.Cookies.Delete("AxonAuthenticationExpiry");
        }
    }
}
