using AutoMapper;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Api.Services.UserOrganisation;
using Axon.Core.Contracts;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Domain.Services.Access;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Phlex.Core.MessageBus;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using static Axon.Core.Domain.Entities.OrganisationEntity;

namespace Axon.Core.Api.Commands.Organisation.Create;

internal class CreateOrganisationCommandHandler : BaseCreateCommandHandler<OrganisationBody, OrganisationEntity, OrganisationCreated>
{
    private readonly IAppRepository appRepo;
    private readonly IAllowedRolesProvider allowedRolesProvider;
    private readonly IUserRequestContext userRequestContext;
    private readonly IAccessLevelProvider accessLevelProvider;
    private readonly IUserRepository userRepository;
    private readonly IClientDetailsProvider clientDetailsProvider;
    private readonly ICorrelationIdProvider correlationIdProvider;
    private readonly IOrganisationUserManager organisationUserManager;
    private readonly IAuditService<TenantAuditExtensions> auditService;

    public CreateOrganisationCommandHandler(
        IAppRepository appRepo,
        IOrganisationRepository repo,
        IMapper mapper,
        IMessageBus messageBus,
        IAllowedRolesProvider allowedRolesProvider,
        IUserRequestContext userRequestContext,
        IAccessLevelProvider accessLevelProvider,
        IUserRepository userRepository,
        IClientDetailsProvider clientDetailsProvider,
        ICorrelationIdProvider correlationIdProvider,
        IAuditService<TenantAuditExtensions> auditService,
        IOrganisationUserManager organisationUserManager)
        : base(repo, mapper, messageBus)
    {
        this.appRepo = appRepo;
        this.allowedRolesProvider = allowedRolesProvider;
        this.userRequestContext = userRequestContext;
        this.accessLevelProvider = accessLevelProvider;
        this.userRepository = userRepository;
        this.clientDetailsProvider = clientDetailsProvider;
        this.correlationIdProvider = correlationIdProvider;
        this.auditService = auditService;
        this.organisationUserManager = organisationUserManager;
    }

    public override async Task<CommandResponse> Handle(CreateCommandRequest<OrganisationBody> request, CancellationToken cancellationToken)
    {
        var organisationRepository = (IOrganisationRepository)Repo;
        
        var userAccessLevel = await accessLevelProvider.ProvideAsync();

        var (validationError, organisationAccessLevel) = ValidateOrganisationAccessLevel(userAccessLevel, request.Model.ParentOrganisationId, request.Model.AccessLevel);
        if (validationError != null)
            return validationError;

        validationError = await ValidateParentOrganisationId(userAccessLevel, request.Model.ParentOrganisationId, organisationRepository);
        if (validationError != null)
            return validationError;

        var existing = await organisationRepository.GetItemByDisplayNameAsync(request.Model.DisplayName);
        if (existing != null)
            return CommandResponse.Failed(nameof(request.Model.DisplayName), $"Organisation `{request.Model.DisplayName}` already exists");

        existing = await organisationRepository.GetItemByCodeNameAsync(request.Model.CodeName);
        if (existing != null)
            return CommandResponse.Failed(nameof(request.Model.CodeName), $"Organisation with CodeName `{request.Model.CodeName}` already exists");

        var setups = (request.Model.Apps ?? Array.Empty<SetupAppBody>()).ToList();

        var isUnique = setups.GroupBy(x => x.AppId).Count() == setups.Count;
        if (!isUnique) return CommandResponse.Failed(nameof(request.Model.Apps), "Organisation cannot have duplicated applications");

        var apps = new List<AppConfigEntity>();

        var idx = 0;
        foreach (var setup in setups)
        {
            var app = await appRepo.GetItemAsync(setup.AppId);

            if (app == null) return CommandResponse.Failed($"{nameof(request.Model.Apps)}[{idx}]", $"App `{setup.AppId}` requested for Organisation `{request.Model.DisplayName}` does not exist.", HttpStatusCode.NotFound);

            apps.Add(new AppConfigEntity
            {
                AppId = setup.AppId,
                DisplayName = app.DisplayName,
                AppCodeName = app.AppCodeName,
                Enabled = setup.Enabled,
                Status = AppStatus.ConfigComplete
            });
            idx++;
        }

        var entity = new OrganisationEntity();
        var allowedRoles = allowedRolesProvider.Get();
        var correlationId = correlationIdProvider.Provide();
        var clientDetails = clientDetailsProvider.Provide();

        var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Organisation, AuditEventDescriptions.OrganisationCreated, correlationId, clientDetails, request.Model.CodeName);

        await auditService.LogAsync(AuditEventTypes.OrganisationCreated, tenantAuditExtensions, entity,
            async () =>
            {
                Mapper.Map(request.Model, entity);

                entity.AccessLevel = organisationAccessLevel;
                entity.Apps = apps;
                entity.AllowedRoles = allowedRoles;
                entity.NormalizedDisplayName = request.Model.DisplayName.ToLower();

                await Repo.AddItemAsync(entity);
            });

        var mappedEvent = Mapper.Map<OrgUpdatedEvent>(entity);
        await MessageBus.PublishAsync(mappedEvent, cancellationToken: cancellationToken);

        if (entity.AccessLevel != AccessLevel.Global)
        {
            var userOid = userRequestContext.GetClaimsData().UserOid;
            var user = await userRepository.GetByIdentityProviderObjectIdAsync(userOid);
            await organisationUserManager.AssignAdminUser(user, correlationId, clientDetails, entity);
        }

        return CommandResponse.Created(nameof(OrganisationEntity), entity.Id);
    }

    private static (CommandResponse, AccessLevel) ValidateOrganisationAccessLevel(AccessLevel userAccessLevel, string parentOrganisationId, string requestAccessLevel)
    {
        if (userAccessLevel != AccessLevel.Global || string.IsNullOrWhiteSpace(requestAccessLevel))
            return (null, AccessLevel.Restricted);

        if (!Enum.TryParse<AccessLevel>(requestAccessLevel, true, out var organisationAccessLevel))
            return (CommandResponse.Failed(nameof(OrganisationBody.AccessLevel), $"Invalid Access Level `{requestAccessLevel}`"), AccessLevel.Restricted);

        if (!string.IsNullOrWhiteSpace(parentOrganisationId) && organisationAccessLevel != AccessLevel.Restricted)
            return (CommandResponse.Failed(nameof(OrganisationBody.AccessLevel), $"Organisation needs to have an Access Level of `{nameof(AccessLevel.Restricted)}` to allow it to be created as a child organisation"), AccessLevel.Restricted);

        return (null, organisationAccessLevel);
    }

    private async Task<CommandResponse> ValidateParentOrganisationId(AccessLevel userAccessLevel, string parentOrganisationId, IOrganisationRepository organisationRepository)
    {
        switch (userAccessLevel)
        {
            case AccessLevel.Global:
                break;

            case AccessLevel.ParentChild when string.IsNullOrWhiteSpace(parentOrganisationId):
                return CommandResponse.Failed(nameof(OrganisationBody.ParentOrganisationId), "User does not have permission to add an organisation without a parent", HttpStatusCode.Forbidden);

            case AccessLevel.ParentChild:
                var userOid = userRequestContext.GetClaimsData().UserOid;
                var user = await userRepository.GetByIdentityProviderObjectIdAsync(userOid);
                if (!string.Equals(user.OwnerOrganisationId, parentOrganisationId, StringComparison.OrdinalIgnoreCase))
                    return CommandResponse.Failed(nameof(OrganisationBody.ParentOrganisationId), "User does not have permission to add an organisation as a child of specified parent organisation", HttpStatusCode.Forbidden);
                break;

            case AccessLevel.Restricted:
            default:
                return CommandResponse.Failed(nameof(OrganisationBody.ParentOrganisationId), "User does not have permission to add an organisation", HttpStatusCode.Forbidden);
        }

        if (string.IsNullOrWhiteSpace(parentOrganisationId))
            return null;

        var parentOrganisation = await organisationRepository.GetItemAsync(parentOrganisationId);
        if (parentOrganisation == null)
            return CommandResponse.Failed(nameof(OrganisationBody.ParentOrganisationId), $"Organisation with Id `{parentOrganisationId}` does not exist", HttpStatusCode.NotFound);

        if (parentOrganisation.AccessLevel != AccessLevel.ParentChild)
            return CommandResponse.Failed(nameof(OrganisationBody.ParentOrganisationId), $"Organisation with Id `{parentOrganisationId}` is not a parent organisation");

        if (!string.IsNullOrWhiteSpace(parentOrganisation.ParentOrganisationId))
            return CommandResponse.Failed(nameof(OrganisationBody.ParentOrganisationId), $"Organisation with Id `{parentOrganisationId}` is a child of another organisation");

        return null;
    }
}