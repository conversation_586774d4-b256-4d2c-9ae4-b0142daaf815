﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Shared.User;
using Microsoft.Extensions.Logging;

namespace Axon.Core.Api.Services.Migration
{
    public class UserRoleMigrationService : IUserRoleMigrationService
    {
        private readonly IRolePermissionRepository rolePermissionRepository;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IUserRequestContext userRequestContext;
        private readonly IAccessService accessService;
        private readonly IUserRepository userRepository;
        private readonly IUserIpAddressProvider userIpAddressProvider;
        private readonly ILogger<UserRoleMigrationService> logger;

        public UserRoleMigrationService(ILogger<UserRoleMigrationService> logger, IRolePermissionRepository rolePermissionRepository, IOrganisationRepository organisationRepository,
            IUserRequestContext userRequestContext, IAccessService accessService, IUserRepository userRepository, IUserIpAddressProvider userIpAddressProvider)
        {
            this.rolePermissionRepository = rolePermissionRepository;
            this.organisationRepository = organisationRepository;
            this.userRequestContext = userRequestContext;
            this.accessService = accessService;
            this.userRepository = userRepository;
            this.userIpAddressProvider = userIpAddressProvider;
            this.logger = logger;
        }

        public async Task<bool> Migrate()
        {

            var user = await userRepository.GetByIdentityProviderObjectIdAsync(userRequestContext.GetClaimsData().UserOid);
            if (user == null)
            {
                logger.Log(LogLevel.Error, "UserRoleMigration unable to continue, unable to retrieve user by useroid, UserOid:{UserOid}", userRequestContext.GetClaimsData().UserOid);
                return false;
            }

            logger.Log(LogLevel.Debug, "UserRoleMigration started for user, User Id:{UserId}, User Email{email}", user.Id, user.Email);

            var ipAddress = userIpAddressProvider.Provide();

            var identityProviderId = user.IdentityProviderId;

            //does our users already have user access items?
            var userAccessItems = await accessService.GetUserAccessForUser(user.Id);
            // if true return!
            if (userAccessItems.Any())
            {
                //log user has already been migrated as user access items have been found
                logger.Log(LogLevel.Debug, "UserRoleMigration not continuing, UserAccess items found for user, User Id:{UserId}, User Email:{email}", user.Id, user.Email);
                return true;
            }

            //get organisations for identity provider from current user
            var organisationsAccess = await accessService.GetOrganisationAccessForUser(user.Id);

            var organisations = await organisationRepository.GetAllItemsInCodenameSetAsync(organisationsAccess.Select(x => x.OrganisationCodeName).ToList());
            if (!organisations.Any())
            {
                logger.Log(LogLevel.Error, "UserRoleMigration unable to continue, no organisations found for users IdentityProviderId:{IdentityProviderId}", identityProviderId);
                return false;
            }

            var roles = userRequestContext.GetUserRoles();
            if (!roles.Any())
            {
                logger.Log(LogLevel.Error, "UserRoleMigration unable to continue, no roles found for user, User Id:{UserId}, User Email:{email}", user.Id, user.Email);
                return false;
            }


            //foreach organisation
            foreach (var org in organisations)
            {
                List<string> apps = org.Apps.Select(item => item.AppCodeName).ToList();
                //we also need to get the permissions for axon core
                apps.Add("axon-core");

                //foreach app
                foreach (var app in apps)
                {
                    foreach (var role in roles)
                    {
                        //get users role details
                        var rolePermission = await rolePermissionRepository.GetAppRolePermissionByNameAsync(app, role);

                        if (rolePermission == null)
                        {
                            logger.Log(LogLevel.Debug, "Unable to create access item for user:{email} as role:{role} doesn't exist for app:{appCodeName}", user.Email, role, app);
                            continue;
                        }
                        //create the UserAccess type access items using the org, app, role details
                        var userAccessItemId = await accessService.AddAppOrganisationAccessForUser(org.CodeName, app, user.Email, rolePermission.Id, ipAddress);
                        if (!string.IsNullOrEmpty(userAccessItemId))
                        {
                            //log migration?
                            logger.Log(LogLevel.Debug, "UserAccess item created for user {email}, AccessItemId:{AccessItemId}, org:{org}, app:{app}, role:{role}", user.Email, userAccessItemId,
                                org.CodeName, app, rolePermission.Id);
                        }
                        else
                        {
                            logger.Log(LogLevel.Debug, "UserAccess item was not created for user {email}, org:{org}, app:{app}, role:{role}", user.Email, org.CodeName, app, rolePermission.Id);
                        }
                    }
                }
            }

            logger.Log(LogLevel.Debug, "UserRoleMigration completed for user user {email}", user.Email);
            return true;
        }
    }
}
