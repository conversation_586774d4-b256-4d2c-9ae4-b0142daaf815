api:
  tag: dev

grpc:
  tag: dev

ingress:
  tls:
    - tlsSecretName: **********************************
      hosts:
        - app-integration.smartphlex.com
  hosts:
    - host: app-integration.smartphlex.com
      paths:
        - path: /api/core/(.*)
          pathType: ImplementationSpecific

newrelic_api_app_name: axon-core-int-api
newrelic_grpc_app_name: axon-core-int-grpc
keyVaultName: axn-int-kv-eun
clientId: 38a74d6a-86ad-460a-997e-d6b3384eba63
azureIssuer: https://login.microsoftonline.com/common/v2.0
azureUseCustomRefresh: false


gigyaClientId: cUDSdI53tU5LgmVJH2AkCH-8
gigyaIssuer: https://tst.aaas.cencora.com/oidc/op/v1.0/4_Pv18t6XTOc51PxyYytQzHA/authorize
gigyaUseCustomRefresh: true


corsOriginUrl0: https://app-integration.smartphlex.com
corsOriginUrl1: https://localhost:4000

BlobStorageConnectionString: https://phcgvsharedstaticeun.blob.core.windows.net/

NamespaceName: "axn-int-servicebus-eun"

cosmosdbName: Axon-Core-ApiDb
cosmosdbUrl: 'https://axn-int-cosmos-eun.documents.azure.com:443/'

managedIdentityClientId: 7b182614-48d8-4cc8-8883-6f8e37671004
azureWorkload:
  clientId: 7b182614-48d8-4cc8-8883-6f8e37671004
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AzureBlobStorageContainersAppAvatarsFolderPrefix: int/axon-avatar
AzureBlobStorageContainersOrganisationAvatarsFolderPrefix: int/organisations
AzureBlobStorageContainersThemesFolderPrefix: int/axon-theme

DataProtectionBlobStorageUri: 'https://axnintstorageeun.blob.core.windows.net/'
DataProtectionKeyVaultKey: 'https://axn-int-kv-eun.vault.azure.net/keys/AxonDataProtection'

GoodDataBaseUri: 'https://phlexglobal-test.cloud.gooddata.com/'
GoodDataEnvironment: 'int'