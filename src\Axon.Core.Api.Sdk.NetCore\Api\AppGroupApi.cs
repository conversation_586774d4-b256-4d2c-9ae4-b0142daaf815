/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Axon.Core.Api.Sdk.NetCore.Client;
using Axon.Core.Api.Sdk.NetCore.Model;

namespace Axon.Core.Api.Sdk.NetCore.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppGroupApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="createAppGroupBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse CreateAppGroup(string orgCodeName, string appCodeName, CreateAppGroupBody? createAppGroupBody = default(CreateAppGroupBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="createAppGroupBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> CreateAppGroupWithHttpInfo(string orgCodeName, string appCodeName, CreateAppGroupBody? createAppGroupBody = default(CreateAppGroupBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <returns></returns>
        void DeleteAppGroup(string id, string orgCodeName, string appCodeName);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> DeleteAppGroupWithHttpInfo(string id, string orgCodeName, string appCodeName);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="id"></param>
        /// <returns>AppGroupModelCommandResponse</returns>
        AppGroupModelCommandResponse GetAppGroupById(string orgCodeName, string appCodeName, string id);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="id"></param>
        /// <returns>ApiResponse of AppGroupModelCommandResponse</returns>
        ApiResponse<AppGroupModelCommandResponse> GetAppGroupByIdWithHttpInfo(string orgCodeName, string appCodeName, string id);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>AppGroupModelApiPagedListResultCommandResponse</returns>
        AppGroupModelApiPagedListResultCommandResponse GetFilteredAppGroups(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of AppGroupModelApiPagedListResultCommandResponse</returns>
        ApiResponse<AppGroupModelApiPagedListResultCommandResponse> GetFilteredAppGroupsWithHttpInfo(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <returns>UnassignedGroupModelApiListResultCommandResponse</returns>
        UnassignedGroupModelApiListResultCommandResponse GetUnassignedGroups(string orgCodeName, string appCodeName);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <returns>ApiResponse of UnassignedGroupModelApiListResultCommandResponse</returns>
        ApiResponse<UnassignedGroupModelApiListResultCommandResponse> GetUnassignedGroupsWithHttpInfo(string orgCodeName, string appCodeName);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="updateAppGroupBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse UpdateAppGroup(string id, string orgCodeName, string appCodeName, UpdateAppGroupBody? updateAppGroupBody = default(UpdateAppGroupBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="updateAppGroupBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> UpdateAppGroupWithHttpInfo(string id, string orgCodeName, string appCodeName, UpdateAppGroupBody? updateAppGroupBody = default(UpdateAppGroupBody?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppGroupApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="createAppGroupBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> CreateAppGroupAsync(string orgCodeName, string appCodeName, CreateAppGroupBody? createAppGroupBody = default(CreateAppGroupBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="createAppGroupBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> CreateAppGroupWithHttpInfoAsync(string orgCodeName, string appCodeName, CreateAppGroupBody? createAppGroupBody = default(CreateAppGroupBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task DeleteAppGroupAsync(string id, string orgCodeName, string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> DeleteAppGroupWithHttpInfoAsync(string id, string orgCodeName, string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppGroupModelCommandResponse</returns>
        System.Threading.Tasks.Task<AppGroupModelCommandResponse> GetAppGroupByIdAsync(string orgCodeName, string appCodeName, string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppGroupModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<AppGroupModelCommandResponse>> GetAppGroupByIdWithHttpInfoAsync(string orgCodeName, string appCodeName, string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppGroupModelApiPagedListResultCommandResponse</returns>
        System.Threading.Tasks.Task<AppGroupModelApiPagedListResultCommandResponse> GetFilteredAppGroupsAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppGroupModelApiPagedListResultCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<AppGroupModelApiPagedListResultCommandResponse>> GetFilteredAppGroupsWithHttpInfoAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UnassignedGroupModelApiListResultCommandResponse</returns>
        System.Threading.Tasks.Task<UnassignedGroupModelApiListResultCommandResponse> GetUnassignedGroupsAsync(string orgCodeName, string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UnassignedGroupModelApiListResultCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<UnassignedGroupModelApiListResultCommandResponse>> GetUnassignedGroupsWithHttpInfoAsync(string orgCodeName, string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="updateAppGroupBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> UpdateAppGroupAsync(string id, string orgCodeName, string appCodeName, UpdateAppGroupBody? updateAppGroupBody = default(UpdateAppGroupBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="updateAppGroupBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> UpdateAppGroupWithHttpInfoAsync(string id, string orgCodeName, string appCodeName, UpdateAppGroupBody? updateAppGroupBody = default(UpdateAppGroupBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppGroupApi : IAppGroupApiSync, IAppGroupApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class AppGroupApi : IDisposable, IAppGroupApi
    {
        private Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="AppGroupApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public AppGroupApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppGroupApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public AppGroupApi(string basePath)
        {
            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppGroupApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public AppGroupApi(Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppGroupApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppGroupApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppGroupApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppGroupApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppGroupApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppGroupApi(HttpClient client, Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppGroupApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public AppGroupApi(Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient client, Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient asyncClient, Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="createAppGroupBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse CreateAppGroup(string orgCodeName, string appCodeName, CreateAppGroupBody? createAppGroupBody = default(CreateAppGroupBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = CreateAppGroupWithHttpInfo(orgCodeName, appCodeName, createAppGroupBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="createAppGroupBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> CreateAppGroupWithHttpInfo(string orgCodeName, string appCodeName, CreateAppGroupBody? createAppGroupBody = default(CreateAppGroupBody?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->CreateAppGroup");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->CreateAppGroup");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.Data = createAppGroupBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateAppGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="createAppGroupBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> CreateAppGroupAsync(string orgCodeName, string appCodeName, CreateAppGroupBody? createAppGroupBody = default(CreateAppGroupBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await CreateAppGroupWithHttpInfoAsync(orgCodeName, appCodeName, createAppGroupBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="createAppGroupBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> CreateAppGroupWithHttpInfoAsync(string orgCodeName, string appCodeName, CreateAppGroupBody? createAppGroupBody = default(CreateAppGroupBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->CreateAppGroup");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->CreateAppGroup");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.Data = createAppGroupBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateAppGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <returns></returns>
        public void DeleteAppGroup(string id, string orgCodeName, string appCodeName)
        {
            DeleteAppGroupWithHttpInfo(id, orgCodeName, appCodeName);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<Object> DeleteAppGroupWithHttpInfo(string id, string orgCodeName, string appCodeName)
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppGroupApi->DeleteAppGroup");

            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->DeleteAppGroup");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->DeleteAppGroup");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteAppGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task DeleteAppGroupAsync(string id, string orgCodeName, string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            await DeleteAppGroupWithHttpInfoAsync(id, orgCodeName, appCodeName, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<Object>> DeleteAppGroupWithHttpInfoAsync(string id, string orgCodeName, string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppGroupApi->DeleteAppGroup");

            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->DeleteAppGroup");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->DeleteAppGroup");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteAppGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="id"></param>
        /// <returns>AppGroupModelCommandResponse</returns>
        public AppGroupModelCommandResponse GetAppGroupById(string orgCodeName, string appCodeName, string id)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppGroupModelCommandResponse> localVarResponse = GetAppGroupByIdWithHttpInfo(orgCodeName, appCodeName, id);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="id"></param>
        /// <returns>ApiResponse of AppGroupModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppGroupModelCommandResponse> GetAppGroupByIdWithHttpInfo(string orgCodeName, string appCodeName, string id)
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->GetAppGroupById");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->GetAppGroupById");

            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppGroupApi->GetAppGroupById");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<AppGroupModelCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppGroupById", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppGroupModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<AppGroupModelCommandResponse> GetAppGroupByIdAsync(string orgCodeName, string appCodeName, string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppGroupModelCommandResponse> localVarResponse = await GetAppGroupByIdWithHttpInfoAsync(orgCodeName, appCodeName, id, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppGroupModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppGroupModelCommandResponse>> GetAppGroupByIdWithHttpInfoAsync(string orgCodeName, string appCodeName, string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->GetAppGroupById");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->GetAppGroupById");

            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppGroupApi->GetAppGroupById");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<AppGroupModelCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppGroupById", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>AppGroupModelApiPagedListResultCommandResponse</returns>
        public AppGroupModelApiPagedListResultCommandResponse GetFilteredAppGroups(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppGroupModelApiPagedListResultCommandResponse> localVarResponse = GetFilteredAppGroupsWithHttpInfo(orgCodeName, appCodeName, filter, orderBy, offset, limit);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of AppGroupModelApiPagedListResultCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppGroupModelApiPagedListResultCommandResponse> GetFilteredAppGroupsWithHttpInfo(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->GetFilteredAppGroups");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->GetFilteredAppGroups");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<AppGroupModelApiPagedListResultCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetFilteredAppGroups", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppGroupModelApiPagedListResultCommandResponse</returns>
        public async System.Threading.Tasks.Task<AppGroupModelApiPagedListResultCommandResponse> GetFilteredAppGroupsAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppGroupModelApiPagedListResultCommandResponse> localVarResponse = await GetFilteredAppGroupsWithHttpInfoAsync(orgCodeName, appCodeName, filter, orderBy, offset, limit, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppGroupModelApiPagedListResultCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppGroupModelApiPagedListResultCommandResponse>> GetFilteredAppGroupsWithHttpInfoAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->GetFilteredAppGroups");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->GetFilteredAppGroups");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<AppGroupModelApiPagedListResultCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetFilteredAppGroups", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <returns>UnassignedGroupModelApiListResultCommandResponse</returns>
        public UnassignedGroupModelApiListResultCommandResponse GetUnassignedGroups(string orgCodeName, string appCodeName)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<UnassignedGroupModelApiListResultCommandResponse> localVarResponse = GetUnassignedGroupsWithHttpInfo(orgCodeName, appCodeName);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <returns>ApiResponse of UnassignedGroupModelApiListResultCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<UnassignedGroupModelApiListResultCommandResponse> GetUnassignedGroupsWithHttpInfo(string orgCodeName, string appCodeName)
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->GetUnassignedGroups");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->GetUnassignedGroups");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<UnassignedGroupModelApiListResultCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group/unassigned", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetUnassignedGroups", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UnassignedGroupModelApiListResultCommandResponse</returns>
        public async System.Threading.Tasks.Task<UnassignedGroupModelApiListResultCommandResponse> GetUnassignedGroupsAsync(string orgCodeName, string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<UnassignedGroupModelApiListResultCommandResponse> localVarResponse = await GetUnassignedGroupsWithHttpInfoAsync(orgCodeName, appCodeName, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UnassignedGroupModelApiListResultCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<UnassignedGroupModelApiListResultCommandResponse>> GetUnassignedGroupsWithHttpInfoAsync(string orgCodeName, string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->GetUnassignedGroups");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->GetUnassignedGroups");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<UnassignedGroupModelApiListResultCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group/unassigned", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetUnassignedGroups", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="updateAppGroupBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse UpdateAppGroup(string id, string orgCodeName, string appCodeName, UpdateAppGroupBody? updateAppGroupBody = default(UpdateAppGroupBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = UpdateAppGroupWithHttpInfo(id, orgCodeName, appCodeName, updateAppGroupBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="updateAppGroupBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> UpdateAppGroupWithHttpInfo(string id, string orgCodeName, string appCodeName, UpdateAppGroupBody? updateAppGroupBody = default(UpdateAppGroupBody?))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppGroupApi->UpdateAppGroup");

            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->UpdateAppGroup");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->UpdateAppGroup");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.Data = updateAppGroupBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<CommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="updateAppGroupBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> UpdateAppGroupAsync(string id, string orgCodeName, string appCodeName, UpdateAppGroupBody? updateAppGroupBody = default(UpdateAppGroupBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await UpdateAppGroupWithHttpInfoAsync(id, orgCodeName, appCodeName, updateAppGroupBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="updateAppGroupBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> UpdateAppGroupWithHttpInfoAsync(string id, string orgCodeName, string appCodeName, UpdateAppGroupBody? updateAppGroupBody = default(UpdateAppGroupBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling AppGroupApi->UpdateAppGroup");

            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppGroupApi->UpdateAppGroup");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppGroupApi->UpdateAppGroup");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.Data = updateAppGroupBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<CommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/group/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
