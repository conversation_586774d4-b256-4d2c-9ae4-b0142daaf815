﻿using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Role;
using Axon.Core.Domain.Interfaces.Persistence;
using MediatR;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.RoleDefinition.RoleDefinitionListQuery
{
    internal sealed class GetRoleDefinitionQueryHandler : IRequestHandler<GetRoleDefinitionQueryRequest, CommandResponse<IEnumerable<AppRoleDefinitionPermission>>>
    {
        private readonly IRoleDefinitionRepository roleDefinitionRepository;
        private readonly IMapper mapper;
        public GetRoleDefinitionQueryHandler(IRoleDefinitionRepository roleDefinitionRepository, IMapper mapper)
        {
            this.roleDefinitionRepository = roleDefinitionRepository;
            this.mapper = mapper;
        }
        public async Task<CommandResponse<IEnumerable<AppRoleDefinitionPermission>>> Handle(GetRoleDefinitionQueryRequest request, CancellationToken cancellationToken)
        {
            var roleDefinition = await roleDefinitionRepository.GetItemByAppCodeAsync(request.AppCode);
            if (roleDefinition == null) { return CommandResponse<IEnumerable<AppRoleDefinitionPermission>>.NotFound("RoleDefinition not found", request.AppCode); }

            IEnumerable<AppRoleDefinitionPermission> roleDefinitionsMapped = roleDefinition.Permissions.Select(s => mapper.Map<AppRoleDefinitionPermission>(s));
            return new CommandResponse<IEnumerable<AppRoleDefinitionPermission>> { data = roleDefinitionsMapped };

        }
    }
}
