﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.AddAppTo;

[UsedImplicitly]
public class AddAppToOrganisationCommandRequestValidator : AbstractValidator<AddAppToOrganisationCommandRequest>
{
    public AddAppToOrganisationCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationId)
            .MustBeAValidGuid();
        RuleFor(x => x.AppId)
            .MustBeAValidGuid();
    }
}