﻿using System;
using System.Text.Json;
using Axon.Core.Api.Mappers.AppOrganisationSettings;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;

namespace Axon.Core.Api.Validators.AppOrganisationSettings
{
    public class BooleanValidator : ISettingValidator
    {
        private readonly IAppOrganisationSettingsMapperFactory appOrganisationSettingsMapperFactory;

        public BooleanValidator(IAppOrganisationSettingsMapperFactory appOrganisationSettingsMapperFactory)
        {
            this.appOrganisationSettingsMapperFactory = appOrganisationSettingsMapperFactory;
        }

        public bool Validate(JsonElement settingValue, out string errorMessage, AppSettingsEntity.Setting setting)
        {
            errorMessage = string.Empty;
            try
            {
                var mapper = appOrganisationSettingsMapperFactory.Create(SettingDataType.Boolean);
                mapper.Map(settingValue);
                return true;
            }
            catch (Exception e)
            {
                errorMessage = e.Message;
                return false;
            }
        }
    }
}
