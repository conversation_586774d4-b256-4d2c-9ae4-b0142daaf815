﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Services.UserOrganisation;
using Axon.Core.Api.Validators.ManageOrganisationUser;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Phlex.Core.MessageBus;
using Axon.Core.Domain.Enums;
using CommunityToolkit.Diagnostics;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser.UpdateUserInOrganisation;

internal class UpdateUserInOrganisationCommandHandler : BaseCommandHandler<AccessEntity, UpdateUserInOrganisationCommandRequest, CommandResponse>
{
    private readonly IAccessRepository accessRepository;
    private readonly IOrganisationRepository organisationRepository;
    private readonly IClientDetailsProvider clientDetailsProvider;
    private readonly ICorrelationIdProvider correlationIdProvider;
    private readonly IManageOrganisationUserValidator manageOrganisationUserValidator;
    private readonly IOrganisationUserManager organisationUserManager;
    private readonly IUserRepository userRepository;

    public UpdateUserInOrganisationCommandHandler(IAccessRepository accessRepository,
        IMapper mapper,
        IMessageBus messageBus,
        IOrganisationRepository organisationRepository,
        IClientDetailsProvider clientDetailsProvider,
        ICorrelationIdProvider correlationIdProvider,
        IManageOrganisationUserValidator manageOrganisationUserValidator,
        IOrganisationUserManager organisationUserManager,
        IUserRepository userRepository)
        : base(accessRepository, mapper, messageBus)
    {
        Guard.IsNotNull(accessRepository);
        this.accessRepository = accessRepository;
        Guard.IsNotNull(manageOrganisationUserValidator);
        this.manageOrganisationUserValidator = manageOrganisationUserValidator;
        Guard.IsNotNull(clientDetailsProvider);
        this.clientDetailsProvider = clientDetailsProvider;
        Guard.IsNotNull(correlationIdProvider);
        this.correlationIdProvider = correlationIdProvider;
        Guard.IsNotNull(organisationRepository);
        this.organisationRepository = organisationRepository;
        Guard.IsNotNull(organisationUserManager);
        this.organisationUserManager = organisationUserManager;
        this.userRepository = userRepository;
    }

    public override async Task<CommandResponse> Handle(UpdateUserInOrganisationCommandRequest request, CancellationToken cancellationToken)
    {
        try
        {
            var validateResponse = await Validate(request);
            if (validateResponse != null)
            {
                return validateResponse;
            }

            var correlationId = correlationIdProvider.Provide();
            var clientDetails = clientDetailsProvider.Provide();

            var organisationEntity = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);

            var userStatus = Enum.Parse<UserStatus>(request.UpdateUserInOrganisationCommandBody.Status, true);
            var (userEntity, isNew, isUpdated) = await organisationUserManager.CreateOrUpdateUser(request.UpdateUserInOrganisationCommandBody.Email, request.UpdateUserInOrganisationCommandBody.Name, userStatus, organisationEntity.Id, organisationEntity.CodeName, organisationEntity.IdentityProviderId, correlationId,
                clientDetails, request.UserId);

            await organisationUserManager.AssignUser(userEntity, request.UpdateUserInOrganisationCommandBody.Groups, correlationId, clientDetails, organisationEntity);

            foreach (var childOrganisation in request.UpdateUserInOrganisationCommandBody.ChildOrganisations ?? [])
            {
                var childOrganisationEntity = await organisationRepository.GetItemByCodeNameAsync(childOrganisation.OrganisationCodeName);
                await organisationUserManager.AssignUser(userEntity, childOrganisation.Groups, correlationId, clientDetails, childOrganisationEntity);
            }

            var childOrganisationCodeNames = (request.UpdateUserInOrganisationCommandBody.ChildOrganisations ?? [])
                .Select(groupOrganisation => groupOrganisation.OrganisationCodeName)
                .ToArray();

            await organisationUserManager.UnlinkUserFromUnspecifiedChildOrganisations(userEntity, correlationId, clientDetails, organisationEntity, childOrganisationCodeNames);

            if (!isNew && isUpdated)
            {
                await accessRepository.SynchroniseAccessItemsForUser(request.UserId, request.UpdateUserInOrganisationCommandBody.Name, request.UpdateUserInOrganisationCommandBody.Email);
            }

            return isNew ? CommandResponse.Created(nameof(UserEntity), userEntity.Id) : CommandResponse.Success();
        }
        catch (UnauthorizedAccessException)
        {
            return CommandResponse.Forbidden(nameof(UserEntity), request.UserId);
        }
    }

    private async Task<CommandResponse> Validate(UpdateUserInOrganisationCommandRequest request)
    {
        var user = await userRepository.GetUserByEmailAsync(request.UpdateUserInOrganisationCommandBody.Email);
        if (user != null && !string.Equals(user.Id, request.UserId, StringComparison.OrdinalIgnoreCase))
        {
            return CommandResponse.Conflict(nameof(UserEntity), user.Id, user.Email);
        }

        var organisationValidationResponse = await manageOrganisationUserValidator.ValidateOrganisation(request.OrganisationCodeName);
        if (organisationValidationResponse != null)
        {
            return organisationValidationResponse;
        }

        var (groupValidationResponse, _) = await manageOrganisationUserValidator.ValidateGroups(request.UpdateUserInOrganisationCommandBody.Groups, request.OrganisationCodeName);
        if (groupValidationResponse != null)
        {
            return groupValidationResponse;
        }

        var (childOrganisationValidationResponse, _) = await manageOrganisationUserValidator.ValidateChildOrganisations(request.UpdateUserInOrganisationCommandBody.ChildOrganisations, request.OrganisationCodeName);
        if (childOrganisationValidationResponse != null)
        {
            return childOrganisationValidationResponse;
        }

        return null;
    }
}