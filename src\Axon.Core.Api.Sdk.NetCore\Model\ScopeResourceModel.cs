/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// ScopeResourceModel
    /// </summary>
    [DataContract(Name = "ScopeResourceModel")]
    public partial class ScopeResourceModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ScopeResourceModel" /> class.
        /// </summary>
        /// <param name="appCodeName">appCodeName.</param>
        /// <param name="organisationCodeName">organisationCodeName.</param>
        /// <param name="permission">permission.</param>
        /// <param name="scope">scope.</param>
        /// <param name="resourceName">resourceName.</param>
        /// <param name="resourceId">resourceId.</param>
        public ScopeResourceModel(string appCodeName = default(string), string organisationCodeName = default(string), string permission = default(string), string scope = default(string), string resourceName = default(string), string resourceId = default(string))
        {
            this.AppCodeName = appCodeName;
            this.OrganisationCodeName = organisationCodeName;
            this.Permission = permission;
            this.Scope = scope;
            this.ResourceName = resourceName;
            this.ResourceId = resourceId;
        }

        /// <summary>
        /// Gets or Sets AppCodeName
        /// </summary>
        [DataMember(Name = "appCodeName", EmitDefaultValue = true)]
        public string AppCodeName { get; set; }

        /// <summary>
        /// Gets or Sets OrganisationCodeName
        /// </summary>
        [DataMember(Name = "organisationCodeName", EmitDefaultValue = true)]
        public string OrganisationCodeName { get; set; }

        /// <summary>
        /// Gets or Sets Permission
        /// </summary>
        [DataMember(Name = "permission", EmitDefaultValue = true)]
        public string Permission { get; set; }

        /// <summary>
        /// Gets or Sets Scope
        /// </summary>
        [DataMember(Name = "scope", EmitDefaultValue = true)]
        public string Scope { get; set; }

        /// <summary>
        /// Gets or Sets ResourceName
        /// </summary>
        [DataMember(Name = "resourceName", EmitDefaultValue = true)]
        public string ResourceName { get; set; }

        /// <summary>
        /// Gets or Sets ResourceId
        /// </summary>
        [DataMember(Name = "resourceId", EmitDefaultValue = true)]
        public string ResourceId { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class ScopeResourceModel {\n");
            sb.Append("  AppCodeName: ").Append(AppCodeName).Append("\n");
            sb.Append("  OrganisationCodeName: ").Append(OrganisationCodeName).Append("\n");
            sb.Append("  Permission: ").Append(Permission).Append("\n");
            sb.Append("  Scope: ").Append(Scope).Append("\n");
            sb.Append("  ResourceName: ").Append(ResourceName).Append("\n");
            sb.Append("  ResourceId: ").Append(ResourceId).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
