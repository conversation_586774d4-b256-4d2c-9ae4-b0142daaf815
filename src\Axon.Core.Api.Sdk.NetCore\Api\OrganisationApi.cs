/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Axon.Core.Api.Sdk.NetCore.Client;
using Axon.Core.Api.Sdk.NetCore.Model;

namespace Axon.Core.Api.Sdk.NetCore.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IOrganisationApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="requestBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse AddAppListToOrganisation(string id, List<string>? requestBody = default(List<string>?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="requestBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> AddAppListToOrganisationWithHttpInfo(string id, List<string>? requestBody = default(List<string>?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <returns>CommandResponse</returns>
        CommandResponse AddAppToOrganisation(string id, string appId);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> AddAppToOrganisationWithHttpInfo(string id, string appId);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <returns>OrganisationGroupModelCommandResponse</returns>
        OrganisationGroupModelCommandResponse AddOrganisationGroup(string orgCodeName, AddOrganisationGroupCommandRequestBody? addOrganisationGroupCommandRequestBody = default(AddOrganisationGroupCommandRequestBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <returns>ApiResponse of OrganisationGroupModelCommandResponse</returns>
        ApiResponse<OrganisationGroupModelCommandResponse> AddOrganisationGroupWithHttpInfo(string orgCodeName, AddOrganisationGroupCommandRequestBody? addOrganisationGroupCommandRequestBody = default(AddOrganisationGroupCommandRequestBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationUserCommandRequestBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse AddUsersToOrganisation(string orgCodeName, AddOrganisationUserCommandRequestBody? addOrganisationUserCommandRequestBody = default(AddOrganisationUserCommandRequestBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationUserCommandRequestBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> AddUsersToOrganisationWithHttpInfo(string orgCodeName, AddOrganisationUserCommandRequestBody? addOrganisationUserCommandRequestBody = default(AddOrganisationUserCommandRequestBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="addOrganisationGroupUserCommandRequestBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse AddUsersToOrganisationGroup(string orgCodeName, string groupId, AddOrganisationGroupUserCommandRequestBody? addOrganisationGroupUserCommandRequestBody = default(AddOrganisationGroupUserCommandRequestBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="addOrganisationGroupUserCommandRequestBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> AddUsersToOrganisationGroupWithHttpInfo(string orgCodeName, string groupId, AddOrganisationGroupUserCommandRequestBody? addOrganisationGroupUserCommandRequestBody = default(AddOrganisationGroupUserCommandRequestBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="organisationBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse CreateOrganisation(OrganisationBody? organisationBody = default(OrganisationBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="organisationBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> CreateOrganisationWithHttpInfo(OrganisationBody? organisationBody = default(OrganisationBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <returns></returns>
        void DeleteAppConfig(string id, string appId);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> DeleteAppConfigWithHttpInfo(string id, string appId);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <returns>CommandResponse</returns>
        CommandResponse DeleteGroupFromOrganisation(string orgCodeName, string groupId);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> DeleteGroupFromOrganisationWithHttpInfo(string orgCodeName, string groupId);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns></returns>
        void DeleteOrganisation(string id);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> DeleteOrganisationWithHttpInfo(string id);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        void DeleteOrganisationUser(string orgCodeName, string userId);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> DeleteOrganisationUserWithHttpInfo(string orgCodeName, string userId);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="editOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <returns>OrganisationGroupModelCommandResponse</returns>
        OrganisationGroupModelCommandResponse EditOrganisationGroup(string orgCodeName, string groupId, EditOrganisationGroupCommandRequestBody? editOrganisationGroupCommandRequestBody = default(EditOrganisationGroupCommandRequestBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="editOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <returns>ApiResponse of OrganisationGroupModelCommandResponse</returns>
        ApiResponse<OrganisationGroupModelCommandResponse> EditOrganisationGroupWithHttpInfo(string orgCodeName, string groupId, EditOrganisationGroupCommandRequestBody? editOrganisationGroupCommandRequestBody = default(EditOrganisationGroupCommandRequestBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>OrganisationModelIEnumerableCommandResponse</returns>
        OrganisationModelIEnumerableCommandResponse GetAdminOrganisationList(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of OrganisationModelIEnumerableCommandResponse</returns>
        ApiResponse<OrganisationModelIEnumerableCommandResponse> GetAdminOrganisationListWithHttpInfo(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>OrganisationModelCommandResponse</returns>
        OrganisationModelCommandResponse GetOrganisation(Guid id);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of OrganisationModelCommandResponse</returns>
        ApiResponse<OrganisationModelCommandResponse> GetOrganisationWithHttpInfo(Guid id);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <returns>OrganisationModelCommandResponse</returns>
        OrganisationModelCommandResponse GetOrganisationByCodeName(string orgCodeName);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <returns>ApiResponse of OrganisationModelCommandResponse</returns>
        ApiResponse<OrganisationModelCommandResponse> GetOrganisationByCodeNameWithHttpInfo(string orgCodeName);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>OrganisationGroupPagedModelCommandResponse</returns>
        OrganisationGroupPagedModelCommandResponse GetOrganisationGroup(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of OrganisationGroupPagedModelCommandResponse</returns>
        ApiResponse<OrganisationGroupPagedModelCommandResponse> GetOrganisationGroupWithHttpInfo(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>OrganisationUserModelApiPagedListResultCommandResponse</returns>
        OrganisationUserModelApiPagedListResultCommandResponse GetOrganisationGroupEligibleUsers(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of OrganisationUserModelApiPagedListResultCommandResponse</returns>
        ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse> GetOrganisationGroupEligibleUsersWithHttpInfo(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>OrganisationGroupModelApiPagedListResultCommandResponse</returns>
        OrganisationGroupModelApiPagedListResultCommandResponse GetOrganisationGroups(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of OrganisationGroupModelApiPagedListResultCommandResponse</returns>
        ApiResponse<OrganisationGroupModelApiPagedListResultCommandResponse> GetOrganisationGroupsWithHttpInfo(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>OrganisationModelIEnumerableCommandResponse</returns>
        OrganisationModelIEnumerableCommandResponse GetOrganisationList(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of OrganisationModelIEnumerableCommandResponse</returns>
        ApiResponse<OrganisationModelIEnumerableCommandResponse> GetOrganisationListWithHttpInfo(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="takeUserFromChildOrganisations"> (optional, default to false)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>OrganisationUserModelCommandResponse</returns>
        OrganisationUserModelCommandResponse GetOrganisationUser(string orgCodeName, string userId, bool? takeUserFromChildOrganisations = default(bool?), string? embed = default(string?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="takeUserFromChildOrganisations"> (optional, default to false)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of OrganisationUserModelCommandResponse</returns>
        ApiResponse<OrganisationUserModelCommandResponse> GetOrganisationUserWithHttpInfo(string orgCodeName, string userId, bool? takeUserFromChildOrganisations = default(bool?), string? embed = default(string?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>OrganisationUserModelApiPagedListResultCommandResponse</returns>
        OrganisationUserModelApiPagedListResultCommandResponse GetOrganisationUsers(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of OrganisationUserModelApiPagedListResultCommandResponse</returns>
        ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse> GetOrganisationUsersWithHttpInfo(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="userId"></param>
        /// <returns>CommandResponse</returns>
        CommandResponse RemoveUserFromOrganisationGroup(string orgCodeName, string groupId, string userId);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="userId"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> RemoveUserFromOrganisationGroupWithHttpInfo(string orgCodeName, string groupId, string userId);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateOrganisationBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse UpdateOrganisation(string id, UpdateOrganisationBody? updateOrganisationBody = default(UpdateOrganisationBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateOrganisationBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> UpdateOrganisationWithHttpInfo(string id, UpdateOrganisationBody? updateOrganisationBody = default(UpdateOrganisationBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>CommandResponse</returns>
        CommandResponse UpdateOrganisationAvatar(string id);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> UpdateOrganisationAvatarWithHttpInfo(string id);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>CommandResponse</returns>
        CommandResponse UpdateOrganisationHeader(string id);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> UpdateOrganisationHeaderWithHttpInfo(string id);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="updateUserInOrganisationCommandRequestBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse UpdateUserInOrganisation(string orgCodeName, string userId, UpdateUserInOrganisationCommandRequestBody? updateUserInOrganisationCommandRequestBody = default(UpdateUserInOrganisationCommandRequestBody?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="updateUserInOrganisationCommandRequestBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> UpdateUserInOrganisationWithHttpInfo(string orgCodeName, string userId, UpdateUserInOrganisationCommandRequestBody? updateUserInOrganisationCommandRequestBody = default(UpdateUserInOrganisationCommandRequestBody?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ValidationResultModelCommandResponse</returns>
        ValidationResultModelCommandResponse ValidateOrganisation(string? name = default(string?), string? codeName = default(string?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ApiResponse of ValidationResultModelCommandResponse</returns>
        ApiResponse<ValidationResultModelCommandResponse> ValidateOrganisationWithHttpInfo(string? name = default(string?), string? codeName = default(string?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ValidationResultModelCommandResponse</returns>
        ValidationResultModelCommandResponse ValidateOrganisationById(string id, string? name = default(string?), string? codeName = default(string?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ApiResponse of ValidationResultModelCommandResponse</returns>
        ApiResponse<ValidationResultModelCommandResponse> ValidateOrganisationByIdWithHttpInfo(string id, string? name = default(string?), string? codeName = default(string?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IOrganisationApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="requestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> AddAppListToOrganisationAsync(string id, List<string>? requestBody = default(List<string>?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="requestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> AddAppListToOrganisationWithHttpInfoAsync(string id, List<string>? requestBody = default(List<string>?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> AddAppToOrganisationAsync(string id, string appId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> AddAppToOrganisationWithHttpInfoAsync(string id, string appId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationGroupModelCommandResponse</returns>
        System.Threading.Tasks.Task<OrganisationGroupModelCommandResponse> AddOrganisationGroupAsync(string orgCodeName, AddOrganisationGroupCommandRequestBody? addOrganisationGroupCommandRequestBody = default(AddOrganisationGroupCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationGroupModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<OrganisationGroupModelCommandResponse>> AddOrganisationGroupWithHttpInfoAsync(string orgCodeName, AddOrganisationGroupCommandRequestBody? addOrganisationGroupCommandRequestBody = default(AddOrganisationGroupCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationUserCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> AddUsersToOrganisationAsync(string orgCodeName, AddOrganisationUserCommandRequestBody? addOrganisationUserCommandRequestBody = default(AddOrganisationUserCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationUserCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> AddUsersToOrganisationWithHttpInfoAsync(string orgCodeName, AddOrganisationUserCommandRequestBody? addOrganisationUserCommandRequestBody = default(AddOrganisationUserCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="addOrganisationGroupUserCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> AddUsersToOrganisationGroupAsync(string orgCodeName, string groupId, AddOrganisationGroupUserCommandRequestBody? addOrganisationGroupUserCommandRequestBody = default(AddOrganisationGroupUserCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="addOrganisationGroupUserCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> AddUsersToOrganisationGroupWithHttpInfoAsync(string orgCodeName, string groupId, AddOrganisationGroupUserCommandRequestBody? addOrganisationGroupUserCommandRequestBody = default(AddOrganisationGroupUserCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="organisationBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> CreateOrganisationAsync(OrganisationBody? organisationBody = default(OrganisationBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="organisationBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> CreateOrganisationWithHttpInfoAsync(OrganisationBody? organisationBody = default(OrganisationBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task DeleteAppConfigAsync(string id, string appId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> DeleteAppConfigWithHttpInfoAsync(string id, string appId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> DeleteGroupFromOrganisationAsync(string orgCodeName, string groupId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> DeleteGroupFromOrganisationWithHttpInfoAsync(string orgCodeName, string groupId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task DeleteOrganisationAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> DeleteOrganisationWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task DeleteOrganisationUserAsync(string orgCodeName, string userId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> DeleteOrganisationUserWithHttpInfoAsync(string orgCodeName, string userId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="editOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationGroupModelCommandResponse</returns>
        System.Threading.Tasks.Task<OrganisationGroupModelCommandResponse> EditOrganisationGroupAsync(string orgCodeName, string groupId, EditOrganisationGroupCommandRequestBody? editOrganisationGroupCommandRequestBody = default(EditOrganisationGroupCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="editOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationGroupModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<OrganisationGroupModelCommandResponse>> EditOrganisationGroupWithHttpInfoAsync(string orgCodeName, string groupId, EditOrganisationGroupCommandRequestBody? editOrganisationGroupCommandRequestBody = default(EditOrganisationGroupCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationModelIEnumerableCommandResponse</returns>
        System.Threading.Tasks.Task<OrganisationModelIEnumerableCommandResponse> GetAdminOrganisationListAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationModelIEnumerableCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<OrganisationModelIEnumerableCommandResponse>> GetAdminOrganisationListWithHttpInfoAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationModelCommandResponse</returns>
        System.Threading.Tasks.Task<OrganisationModelCommandResponse> GetOrganisationAsync(Guid id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<OrganisationModelCommandResponse>> GetOrganisationWithHttpInfoAsync(Guid id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationModelCommandResponse</returns>
        System.Threading.Tasks.Task<OrganisationModelCommandResponse> GetOrganisationByCodeNameAsync(string orgCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<OrganisationModelCommandResponse>> GetOrganisationByCodeNameWithHttpInfoAsync(string orgCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationGroupPagedModelCommandResponse</returns>
        System.Threading.Tasks.Task<OrganisationGroupPagedModelCommandResponse> GetOrganisationGroupAsync(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationGroupPagedModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<OrganisationGroupPagedModelCommandResponse>> GetOrganisationGroupWithHttpInfoAsync(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationUserModelApiPagedListResultCommandResponse</returns>
        System.Threading.Tasks.Task<OrganisationUserModelApiPagedListResultCommandResponse> GetOrganisationGroupEligibleUsersAsync(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationUserModelApiPagedListResultCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse>> GetOrganisationGroupEligibleUsersWithHttpInfoAsync(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationGroupModelApiPagedListResultCommandResponse</returns>
        System.Threading.Tasks.Task<OrganisationGroupModelApiPagedListResultCommandResponse> GetOrganisationGroupsAsync(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationGroupModelApiPagedListResultCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<OrganisationGroupModelApiPagedListResultCommandResponse>> GetOrganisationGroupsWithHttpInfoAsync(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationModelIEnumerableCommandResponse</returns>
        System.Threading.Tasks.Task<OrganisationModelIEnumerableCommandResponse> GetOrganisationListAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationModelIEnumerableCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<OrganisationModelIEnumerableCommandResponse>> GetOrganisationListWithHttpInfoAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="takeUserFromChildOrganisations"> (optional, default to false)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationUserModelCommandResponse</returns>
        System.Threading.Tasks.Task<OrganisationUserModelCommandResponse> GetOrganisationUserAsync(string orgCodeName, string userId, bool? takeUserFromChildOrganisations = default(bool?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="takeUserFromChildOrganisations"> (optional, default to false)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationUserModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<OrganisationUserModelCommandResponse>> GetOrganisationUserWithHttpInfoAsync(string orgCodeName, string userId, bool? takeUserFromChildOrganisations = default(bool?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationUserModelApiPagedListResultCommandResponse</returns>
        System.Threading.Tasks.Task<OrganisationUserModelApiPagedListResultCommandResponse> GetOrganisationUsersAsync(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationUserModelApiPagedListResultCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse>> GetOrganisationUsersWithHttpInfoAsync(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="userId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> RemoveUserFromOrganisationGroupAsync(string orgCodeName, string groupId, string userId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="userId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> RemoveUserFromOrganisationGroupWithHttpInfoAsync(string orgCodeName, string groupId, string userId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateOrganisationBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> UpdateOrganisationAsync(string id, UpdateOrganisationBody? updateOrganisationBody = default(UpdateOrganisationBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateOrganisationBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> UpdateOrganisationWithHttpInfoAsync(string id, UpdateOrganisationBody? updateOrganisationBody = default(UpdateOrganisationBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> UpdateOrganisationAvatarAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> UpdateOrganisationAvatarWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> UpdateOrganisationHeaderAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> UpdateOrganisationHeaderWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="updateUserInOrganisationCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> UpdateUserInOrganisationAsync(string orgCodeName, string userId, UpdateUserInOrganisationCommandRequestBody? updateUserInOrganisationCommandRequestBody = default(UpdateUserInOrganisationCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="updateUserInOrganisationCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> UpdateUserInOrganisationWithHttpInfoAsync(string orgCodeName, string userId, UpdateUserInOrganisationCommandRequestBody? updateUserInOrganisationCommandRequestBody = default(UpdateUserInOrganisationCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ValidationResultModelCommandResponse</returns>
        System.Threading.Tasks.Task<ValidationResultModelCommandResponse> ValidateOrganisationAsync(string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ValidationResultModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<ValidationResultModelCommandResponse>> ValidateOrganisationWithHttpInfoAsync(string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ValidationResultModelCommandResponse</returns>
        System.Threading.Tasks.Task<ValidationResultModelCommandResponse> ValidateOrganisationByIdAsync(string id, string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ValidationResultModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<ValidationResultModelCommandResponse>> ValidateOrganisationByIdWithHttpInfoAsync(string id, string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IOrganisationApi : IOrganisationApiSync, IOrganisationApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class OrganisationApi : IDisposable, IOrganisationApi
    {
        private Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="OrganisationApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public OrganisationApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OrganisationApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public OrganisationApi(string basePath)
        {
            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OrganisationApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public OrganisationApi(Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OrganisationApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public OrganisationApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OrganisationApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public OrganisationApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OrganisationApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public OrganisationApi(HttpClient client, Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OrganisationApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public OrganisationApi(Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient client, Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient asyncClient, Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="requestBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse AddAppListToOrganisation(string id, List<string>? requestBody = default(List<string>?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = AddAppListToOrganisationWithHttpInfo(id, requestBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="requestBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> AddAppListToOrganisationWithHttpInfo(string id, List<string>? requestBody = default(List<string>?))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->AddAppListToOrganisation");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.Data = requestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CommandResponse>("/v1/Organisation/{id}/App", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("AddAppListToOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="requestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> AddAppListToOrganisationAsync(string id, List<string>? requestBody = default(List<string>?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await AddAppListToOrganisationWithHttpInfoAsync(id, requestBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="requestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> AddAppListToOrganisationWithHttpInfoAsync(string id, List<string>? requestBody = default(List<string>?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->AddAppListToOrganisation");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.Data = requestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CommandResponse>("/v1/Organisation/{id}/App", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("AddAppListToOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <returns>CommandResponse</returns>
        public CommandResponse AddAppToOrganisation(string id, string appId)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = AddAppToOrganisationWithHttpInfo(id, appId);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> AddAppToOrganisationWithHttpInfo(string id, string appId)
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->AddAppToOrganisation");

            // verify the required parameter 'appId' is set
            if (appId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appId' when calling OrganisationApi->AddAppToOrganisation");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("appId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appId)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CommandResponse>("/v1/Organisation/{id}/App/{appId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("AddAppToOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> AddAppToOrganisationAsync(string id, string appId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await AddAppToOrganisationWithHttpInfoAsync(id, appId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> AddAppToOrganisationWithHttpInfoAsync(string id, string appId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->AddAppToOrganisation");

            // verify the required parameter 'appId' is set
            if (appId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appId' when calling OrganisationApi->AddAppToOrganisation");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("appId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appId)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CommandResponse>("/v1/Organisation/{id}/App/{appId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("AddAppToOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <returns>OrganisationGroupModelCommandResponse</returns>
        public OrganisationGroupModelCommandResponse AddOrganisationGroup(string orgCodeName, AddOrganisationGroupCommandRequestBody? addOrganisationGroupCommandRequestBody = default(AddOrganisationGroupCommandRequestBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelCommandResponse> localVarResponse = AddOrganisationGroupWithHttpInfo(orgCodeName, addOrganisationGroupCommandRequestBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <returns>ApiResponse of OrganisationGroupModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelCommandResponse> AddOrganisationGroupWithHttpInfo(string orgCodeName, AddOrganisationGroupCommandRequestBody? addOrganisationGroupCommandRequestBody = default(AddOrganisationGroupCommandRequestBody?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->AddOrganisationGroup");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.Data = addOrganisationGroupCommandRequestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<OrganisationGroupModelCommandResponse>("/v1/Organisation/{orgCodeName}/groups", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("AddOrganisationGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationGroupModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<OrganisationGroupModelCommandResponse> AddOrganisationGroupAsync(string orgCodeName, AddOrganisationGroupCommandRequestBody? addOrganisationGroupCommandRequestBody = default(AddOrganisationGroupCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelCommandResponse> localVarResponse = await AddOrganisationGroupWithHttpInfoAsync(orgCodeName, addOrganisationGroupCommandRequestBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationGroupModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelCommandResponse>> AddOrganisationGroupWithHttpInfoAsync(string orgCodeName, AddOrganisationGroupCommandRequestBody? addOrganisationGroupCommandRequestBody = default(AddOrganisationGroupCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->AddOrganisationGroup");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.Data = addOrganisationGroupCommandRequestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<OrganisationGroupModelCommandResponse>("/v1/Organisation/{orgCodeName}/groups", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("AddOrganisationGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationUserCommandRequestBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse AddUsersToOrganisation(string orgCodeName, AddOrganisationUserCommandRequestBody? addOrganisationUserCommandRequestBody = default(AddOrganisationUserCommandRequestBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = AddUsersToOrganisationWithHttpInfo(orgCodeName, addOrganisationUserCommandRequestBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationUserCommandRequestBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> AddUsersToOrganisationWithHttpInfo(string orgCodeName, AddOrganisationUserCommandRequestBody? addOrganisationUserCommandRequestBody = default(AddOrganisationUserCommandRequestBody?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->AddUsersToOrganisation");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.Data = addOrganisationUserCommandRequestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CommandResponse>("/v1/Organisation/{orgCodeName}/users", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("AddUsersToOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationUserCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> AddUsersToOrganisationAsync(string orgCodeName, AddOrganisationUserCommandRequestBody? addOrganisationUserCommandRequestBody = default(AddOrganisationUserCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await AddUsersToOrganisationWithHttpInfoAsync(orgCodeName, addOrganisationUserCommandRequestBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="addOrganisationUserCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> AddUsersToOrganisationWithHttpInfoAsync(string orgCodeName, AddOrganisationUserCommandRequestBody? addOrganisationUserCommandRequestBody = default(AddOrganisationUserCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->AddUsersToOrganisation");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.Data = addOrganisationUserCommandRequestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CommandResponse>("/v1/Organisation/{orgCodeName}/users", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("AddUsersToOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="addOrganisationGroupUserCommandRequestBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse AddUsersToOrganisationGroup(string orgCodeName, string groupId, AddOrganisationGroupUserCommandRequestBody? addOrganisationGroupUserCommandRequestBody = default(AddOrganisationGroupUserCommandRequestBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = AddUsersToOrganisationGroupWithHttpInfo(orgCodeName, groupId, addOrganisationGroupUserCommandRequestBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="addOrganisationGroupUserCommandRequestBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> AddUsersToOrganisationGroupWithHttpInfo(string orgCodeName, string groupId, AddOrganisationGroupUserCommandRequestBody? addOrganisationGroupUserCommandRequestBody = default(AddOrganisationGroupUserCommandRequestBody?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->AddUsersToOrganisationGroup");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->AddUsersToOrganisationGroup");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter
            localVarRequestOptions.Data = addOrganisationGroupUserCommandRequestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}/users", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("AddUsersToOrganisationGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="addOrganisationGroupUserCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> AddUsersToOrganisationGroupAsync(string orgCodeName, string groupId, AddOrganisationGroupUserCommandRequestBody? addOrganisationGroupUserCommandRequestBody = default(AddOrganisationGroupUserCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await AddUsersToOrganisationGroupWithHttpInfoAsync(orgCodeName, groupId, addOrganisationGroupUserCommandRequestBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="addOrganisationGroupUserCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> AddUsersToOrganisationGroupWithHttpInfoAsync(string orgCodeName, string groupId, AddOrganisationGroupUserCommandRequestBody? addOrganisationGroupUserCommandRequestBody = default(AddOrganisationGroupUserCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->AddUsersToOrganisationGroup");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->AddUsersToOrganisationGroup");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter
            localVarRequestOptions.Data = addOrganisationGroupUserCommandRequestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}/users", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("AddUsersToOrganisationGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="organisationBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse CreateOrganisation(OrganisationBody? organisationBody = default(OrganisationBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = CreateOrganisationWithHttpInfo(organisationBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="organisationBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> CreateOrganisationWithHttpInfo(OrganisationBody? organisationBody = default(OrganisationBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = organisationBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CommandResponse>("/v1/Organisation", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="organisationBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> CreateOrganisationAsync(OrganisationBody? organisationBody = default(OrganisationBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await CreateOrganisationWithHttpInfoAsync(organisationBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="organisationBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> CreateOrganisationWithHttpInfoAsync(OrganisationBody? organisationBody = default(OrganisationBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = organisationBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CommandResponse>("/v1/Organisation", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <returns></returns>
        public void DeleteAppConfig(string id, string appId)
        {
            DeleteAppConfigWithHttpInfo(id, appId);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<Object> DeleteAppConfigWithHttpInfo(string id, string appId)
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->DeleteAppConfig");

            // verify the required parameter 'appId' is set
            if (appId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appId' when calling OrganisationApi->DeleteAppConfig");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("appId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appId)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/v1/Organisation/{id}/Config/{appId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteAppConfig", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task DeleteAppConfigAsync(string id, string appId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            await DeleteAppConfigWithHttpInfoAsync(id, appId, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="appId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<Object>> DeleteAppConfigWithHttpInfoAsync(string id, string appId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->DeleteAppConfig");

            // verify the required parameter 'appId' is set
            if (appId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appId' when calling OrganisationApi->DeleteAppConfig");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("appId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appId)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/v1/Organisation/{id}/Config/{appId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteAppConfig", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <returns>CommandResponse</returns>
        public CommandResponse DeleteGroupFromOrganisation(string orgCodeName, string groupId)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = DeleteGroupFromOrganisationWithHttpInfo(orgCodeName, groupId);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> DeleteGroupFromOrganisationWithHttpInfo(string orgCodeName, string groupId)
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->DeleteGroupFromOrganisation");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->DeleteGroupFromOrganisation");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<CommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteGroupFromOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> DeleteGroupFromOrganisationAsync(string orgCodeName, string groupId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await DeleteGroupFromOrganisationWithHttpInfoAsync(orgCodeName, groupId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> DeleteGroupFromOrganisationWithHttpInfoAsync(string orgCodeName, string groupId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->DeleteGroupFromOrganisation");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->DeleteGroupFromOrganisation");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<CommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteGroupFromOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns></returns>
        public void DeleteOrganisation(string id)
        {
            DeleteOrganisationWithHttpInfo(id);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<Object> DeleteOrganisationWithHttpInfo(string id)
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->DeleteOrganisation");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/v1/Organisation/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task DeleteOrganisationAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            await DeleteOrganisationWithHttpInfoAsync(id, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<Object>> DeleteOrganisationWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->DeleteOrganisation");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/v1/Organisation/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public void DeleteOrganisationUser(string orgCodeName, string userId)
        {
            DeleteOrganisationUserWithHttpInfo(orgCodeName, userId);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<Object> DeleteOrganisationUserWithHttpInfo(string orgCodeName, string userId)
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->DeleteOrganisationUser");

            // verify the required parameter 'userId' is set
            if (userId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'userId' when calling OrganisationApi->DeleteOrganisationUser");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("userId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(userId)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/v1/Organisation/{orgCodeName}/users/{userId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteOrganisationUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task DeleteOrganisationUserAsync(string orgCodeName, string userId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            await DeleteOrganisationUserWithHttpInfoAsync(orgCodeName, userId, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<Object>> DeleteOrganisationUserWithHttpInfoAsync(string orgCodeName, string userId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->DeleteOrganisationUser");

            // verify the required parameter 'userId' is set
            if (userId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'userId' when calling OrganisationApi->DeleteOrganisationUser");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("userId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(userId)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/v1/Organisation/{orgCodeName}/users/{userId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteOrganisationUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="editOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <returns>OrganisationGroupModelCommandResponse</returns>
        public OrganisationGroupModelCommandResponse EditOrganisationGroup(string orgCodeName, string groupId, EditOrganisationGroupCommandRequestBody? editOrganisationGroupCommandRequestBody = default(EditOrganisationGroupCommandRequestBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelCommandResponse> localVarResponse = EditOrganisationGroupWithHttpInfo(orgCodeName, groupId, editOrganisationGroupCommandRequestBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="editOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <returns>ApiResponse of OrganisationGroupModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelCommandResponse> EditOrganisationGroupWithHttpInfo(string orgCodeName, string groupId, EditOrganisationGroupCommandRequestBody? editOrganisationGroupCommandRequestBody = default(EditOrganisationGroupCommandRequestBody?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->EditOrganisationGroup");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->EditOrganisationGroup");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter
            localVarRequestOptions.Data = editOrganisationGroupCommandRequestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<OrganisationGroupModelCommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("EditOrganisationGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="editOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationGroupModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<OrganisationGroupModelCommandResponse> EditOrganisationGroupAsync(string orgCodeName, string groupId, EditOrganisationGroupCommandRequestBody? editOrganisationGroupCommandRequestBody = default(EditOrganisationGroupCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelCommandResponse> localVarResponse = await EditOrganisationGroupWithHttpInfoAsync(orgCodeName, groupId, editOrganisationGroupCommandRequestBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="editOrganisationGroupCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationGroupModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelCommandResponse>> EditOrganisationGroupWithHttpInfoAsync(string orgCodeName, string groupId, EditOrganisationGroupCommandRequestBody? editOrganisationGroupCommandRequestBody = default(EditOrganisationGroupCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->EditOrganisationGroup");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->EditOrganisationGroup");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter
            localVarRequestOptions.Data = editOrganisationGroupCommandRequestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<OrganisationGroupModelCommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("EditOrganisationGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>OrganisationModelIEnumerableCommandResponse</returns>
        public OrganisationModelIEnumerableCommandResponse GetAdminOrganisationList(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelIEnumerableCommandResponse> localVarResponse = GetAdminOrganisationListWithHttpInfo(filter, orderBy, offset, limit);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of OrganisationModelIEnumerableCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelIEnumerableCommandResponse> GetAdminOrganisationListWithHttpInfo(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<OrganisationModelIEnumerableCommandResponse>("/v1/Organisation/admin", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAdminOrganisationList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationModelIEnumerableCommandResponse</returns>
        public async System.Threading.Tasks.Task<OrganisationModelIEnumerableCommandResponse> GetAdminOrganisationListAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelIEnumerableCommandResponse> localVarResponse = await GetAdminOrganisationListWithHttpInfoAsync(filter, orderBy, offset, limit, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationModelIEnumerableCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelIEnumerableCommandResponse>> GetAdminOrganisationListWithHttpInfoAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<OrganisationModelIEnumerableCommandResponse>("/v1/Organisation/admin", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAdminOrganisationList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>OrganisationModelCommandResponse</returns>
        public OrganisationModelCommandResponse GetOrganisation(Guid id)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelCommandResponse> localVarResponse = GetOrganisationWithHttpInfo(id);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of OrganisationModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelCommandResponse> GetOrganisationWithHttpInfo(Guid id)
        {
            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<OrganisationModelCommandResponse>("/v1/Organisation/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<OrganisationModelCommandResponse> GetOrganisationAsync(Guid id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelCommandResponse> localVarResponse = await GetOrganisationWithHttpInfoAsync(id, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelCommandResponse>> GetOrganisationWithHttpInfoAsync(Guid id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<OrganisationModelCommandResponse>("/v1/Organisation/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <returns>OrganisationModelCommandResponse</returns>
        public OrganisationModelCommandResponse GetOrganisationByCodeName(string orgCodeName)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelCommandResponse> localVarResponse = GetOrganisationByCodeNameWithHttpInfo(orgCodeName);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <returns>ApiResponse of OrganisationModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelCommandResponse> GetOrganisationByCodeNameWithHttpInfo(string orgCodeName)
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationByCodeName");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<OrganisationModelCommandResponse>("/v1/Organisation/{orgCodeName}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationByCodeName", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<OrganisationModelCommandResponse> GetOrganisationByCodeNameAsync(string orgCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelCommandResponse> localVarResponse = await GetOrganisationByCodeNameWithHttpInfoAsync(orgCodeName, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelCommandResponse>> GetOrganisationByCodeNameWithHttpInfoAsync(string orgCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationByCodeName");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<OrganisationModelCommandResponse>("/v1/Organisation/{orgCodeName}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationByCodeName", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>OrganisationGroupPagedModelCommandResponse</returns>
        public OrganisationGroupPagedModelCommandResponse GetOrganisationGroup(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupPagedModelCommandResponse> localVarResponse = GetOrganisationGroupWithHttpInfo(orgCodeName, groupId, filter, orderBy, offset, limit);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of OrganisationGroupPagedModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupPagedModelCommandResponse> GetOrganisationGroupWithHttpInfo(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationGroup");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->GetOrganisationGroup");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<OrganisationGroupPagedModelCommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationGroupPagedModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<OrganisationGroupPagedModelCommandResponse> GetOrganisationGroupAsync(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupPagedModelCommandResponse> localVarResponse = await GetOrganisationGroupWithHttpInfoAsync(orgCodeName, groupId, filter, orderBy, offset, limit, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationGroupPagedModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupPagedModelCommandResponse>> GetOrganisationGroupWithHttpInfoAsync(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationGroup");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->GetOrganisationGroup");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<OrganisationGroupPagedModelCommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>OrganisationUserModelApiPagedListResultCommandResponse</returns>
        public OrganisationUserModelApiPagedListResultCommandResponse GetOrganisationGroupEligibleUsers(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse> localVarResponse = GetOrganisationGroupEligibleUsersWithHttpInfo(orgCodeName, groupId, filter, orderBy, offset, limit, embed);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of OrganisationUserModelApiPagedListResultCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse> GetOrganisationGroupEligibleUsersWithHttpInfo(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationGroupEligibleUsers");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->GetOrganisationGroupEligibleUsers");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (embed != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "embed", embed));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<OrganisationUserModelApiPagedListResultCommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}/eligibleUsers", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationGroupEligibleUsers", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationUserModelApiPagedListResultCommandResponse</returns>
        public async System.Threading.Tasks.Task<OrganisationUserModelApiPagedListResultCommandResponse> GetOrganisationGroupEligibleUsersAsync(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse> localVarResponse = await GetOrganisationGroupEligibleUsersWithHttpInfoAsync(orgCodeName, groupId, filter, orderBy, offset, limit, embed, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationUserModelApiPagedListResultCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse>> GetOrganisationGroupEligibleUsersWithHttpInfoAsync(string orgCodeName, string groupId, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationGroupEligibleUsers");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->GetOrganisationGroupEligibleUsers");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (embed != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "embed", embed));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<OrganisationUserModelApiPagedListResultCommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}/eligibleUsers", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationGroupEligibleUsers", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>OrganisationGroupModelApiPagedListResultCommandResponse</returns>
        public OrganisationGroupModelApiPagedListResultCommandResponse GetOrganisationGroups(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelApiPagedListResultCommandResponse> localVarResponse = GetOrganisationGroupsWithHttpInfo(orgCodeName, filter, orderBy, offset, limit);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of OrganisationGroupModelApiPagedListResultCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelApiPagedListResultCommandResponse> GetOrganisationGroupsWithHttpInfo(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationGroups");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<OrganisationGroupModelApiPagedListResultCommandResponse>("/v1/Organisation/{orgCodeName}/groups", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationGroups", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationGroupModelApiPagedListResultCommandResponse</returns>
        public async System.Threading.Tasks.Task<OrganisationGroupModelApiPagedListResultCommandResponse> GetOrganisationGroupsAsync(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelApiPagedListResultCommandResponse> localVarResponse = await GetOrganisationGroupsWithHttpInfoAsync(orgCodeName, filter, orderBy, offset, limit, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationGroupModelApiPagedListResultCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationGroupModelApiPagedListResultCommandResponse>> GetOrganisationGroupsWithHttpInfoAsync(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationGroups");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<OrganisationGroupModelApiPagedListResultCommandResponse>("/v1/Organisation/{orgCodeName}/groups", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationGroups", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>OrganisationModelIEnumerableCommandResponse</returns>
        public OrganisationModelIEnumerableCommandResponse GetOrganisationList(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelIEnumerableCommandResponse> localVarResponse = GetOrganisationListWithHttpInfo(filter, orderBy, offset, limit);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of OrganisationModelIEnumerableCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelIEnumerableCommandResponse> GetOrganisationListWithHttpInfo(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<OrganisationModelIEnumerableCommandResponse>("/v1/Organisation", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationModelIEnumerableCommandResponse</returns>
        public async System.Threading.Tasks.Task<OrganisationModelIEnumerableCommandResponse> GetOrganisationListAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelIEnumerableCommandResponse> localVarResponse = await GetOrganisationListWithHttpInfoAsync(filter, orderBy, offset, limit, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationModelIEnumerableCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationModelIEnumerableCommandResponse>> GetOrganisationListWithHttpInfoAsync(string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<OrganisationModelIEnumerableCommandResponse>("/v1/Organisation", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="takeUserFromChildOrganisations"> (optional, default to false)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>OrganisationUserModelCommandResponse</returns>
        public OrganisationUserModelCommandResponse GetOrganisationUser(string orgCodeName, string userId, bool? takeUserFromChildOrganisations = default(bool?), string? embed = default(string?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelCommandResponse> localVarResponse = GetOrganisationUserWithHttpInfo(orgCodeName, userId, takeUserFromChildOrganisations, embed);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="takeUserFromChildOrganisations"> (optional, default to false)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of OrganisationUserModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelCommandResponse> GetOrganisationUserWithHttpInfo(string orgCodeName, string userId, bool? takeUserFromChildOrganisations = default(bool?), string? embed = default(string?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationUser");

            // verify the required parameter 'userId' is set
            if (userId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'userId' when calling OrganisationApi->GetOrganisationUser");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("userId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(userId)); // path parameter
            if (takeUserFromChildOrganisations != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "takeUserFromChildOrganisations", takeUserFromChildOrganisations));
            }
            if (embed != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "embed", embed));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<OrganisationUserModelCommandResponse>("/v1/Organisation/{orgCodeName}/users/{userId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="takeUserFromChildOrganisations"> (optional, default to false)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationUserModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<OrganisationUserModelCommandResponse> GetOrganisationUserAsync(string orgCodeName, string userId, bool? takeUserFromChildOrganisations = default(bool?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelCommandResponse> localVarResponse = await GetOrganisationUserWithHttpInfoAsync(orgCodeName, userId, takeUserFromChildOrganisations, embed, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="takeUserFromChildOrganisations"> (optional, default to false)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationUserModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelCommandResponse>> GetOrganisationUserWithHttpInfoAsync(string orgCodeName, string userId, bool? takeUserFromChildOrganisations = default(bool?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationUser");

            // verify the required parameter 'userId' is set
            if (userId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'userId' when calling OrganisationApi->GetOrganisationUser");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("userId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(userId)); // path parameter
            if (takeUserFromChildOrganisations != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "takeUserFromChildOrganisations", takeUserFromChildOrganisations));
            }
            if (embed != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "embed", embed));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<OrganisationUserModelCommandResponse>("/v1/Organisation/{orgCodeName}/users/{userId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>OrganisationUserModelApiPagedListResultCommandResponse</returns>
        public OrganisationUserModelApiPagedListResultCommandResponse GetOrganisationUsers(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse> localVarResponse = GetOrganisationUsersWithHttpInfo(orgCodeName, filter, orderBy, offset, limit, embed);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of OrganisationUserModelApiPagedListResultCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse> GetOrganisationUsersWithHttpInfo(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationUsers");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (embed != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "embed", embed));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<OrganisationUserModelApiPagedListResultCommandResponse>("/v1/Organisation/{orgCodeName}/users", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationUsers", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of OrganisationUserModelApiPagedListResultCommandResponse</returns>
        public async System.Threading.Tasks.Task<OrganisationUserModelApiPagedListResultCommandResponse> GetOrganisationUsersAsync(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse> localVarResponse = await GetOrganisationUsersWithHttpInfoAsync(orgCodeName, filter, orderBy, offset, limit, embed, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="embed"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (OrganisationUserModelApiPagedListResultCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<OrganisationUserModelApiPagedListResultCommandResponse>> GetOrganisationUsersWithHttpInfoAsync(string orgCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), string? embed = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->GetOrganisationUsers");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (embed != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "embed", embed));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<OrganisationUserModelApiPagedListResultCommandResponse>("/v1/Organisation/{orgCodeName}/users", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOrganisationUsers", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="userId"></param>
        /// <returns>CommandResponse</returns>
        public CommandResponse RemoveUserFromOrganisationGroup(string orgCodeName, string groupId, string userId)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = RemoveUserFromOrganisationGroupWithHttpInfo(orgCodeName, groupId, userId);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="userId"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> RemoveUserFromOrganisationGroupWithHttpInfo(string orgCodeName, string groupId, string userId)
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->RemoveUserFromOrganisationGroup");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->RemoveUserFromOrganisationGroup");

            // verify the required parameter 'userId' is set
            if (userId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'userId' when calling OrganisationApi->RemoveUserFromOrganisationGroup");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter
            localVarRequestOptions.PathParameters.Add("userId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(userId)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<CommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}/users/{userId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("RemoveUserFromOrganisationGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="userId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> RemoveUserFromOrganisationGroupAsync(string orgCodeName, string groupId, string userId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await RemoveUserFromOrganisationGroupWithHttpInfoAsync(orgCodeName, groupId, userId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="groupId"></param>
        /// <param name="userId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> RemoveUserFromOrganisationGroupWithHttpInfoAsync(string orgCodeName, string groupId, string userId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->RemoveUserFromOrganisationGroup");

            // verify the required parameter 'groupId' is set
            if (groupId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'groupId' when calling OrganisationApi->RemoveUserFromOrganisationGroup");

            // verify the required parameter 'userId' is set
            if (userId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'userId' when calling OrganisationApi->RemoveUserFromOrganisationGroup");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("groupId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(groupId)); // path parameter
            localVarRequestOptions.PathParameters.Add("userId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(userId)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<CommandResponse>("/v1/Organisation/{orgCodeName}/groups/{groupId}/users/{userId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("RemoveUserFromOrganisationGroup", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateOrganisationBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse UpdateOrganisation(string id, UpdateOrganisationBody? updateOrganisationBody = default(UpdateOrganisationBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = UpdateOrganisationWithHttpInfo(id, updateOrganisationBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateOrganisationBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> UpdateOrganisationWithHttpInfo(string id, UpdateOrganisationBody? updateOrganisationBody = default(UpdateOrganisationBody?))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->UpdateOrganisation");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.Data = updateOrganisationBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<CommandResponse>("/v1/Organisation/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateOrganisationBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> UpdateOrganisationAsync(string id, UpdateOrganisationBody? updateOrganisationBody = default(UpdateOrganisationBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await UpdateOrganisationWithHttpInfoAsync(id, updateOrganisationBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="updateOrganisationBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> UpdateOrganisationWithHttpInfoAsync(string id, UpdateOrganisationBody? updateOrganisationBody = default(UpdateOrganisationBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->UpdateOrganisation");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.Data = updateOrganisationBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<CommandResponse>("/v1/Organisation/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>CommandResponse</returns>
        public CommandResponse UpdateOrganisationAvatar(string id)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = UpdateOrganisationAvatarWithHttpInfo(id);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> UpdateOrganisationAvatarWithHttpInfo(string id)
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->UpdateOrganisationAvatar");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<CommandResponse>("/v1/Organisation/{id}/avatar", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateOrganisationAvatar", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> UpdateOrganisationAvatarAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await UpdateOrganisationAvatarWithHttpInfoAsync(id, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> UpdateOrganisationAvatarWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->UpdateOrganisationAvatar");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<CommandResponse>("/v1/Organisation/{id}/avatar", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateOrganisationAvatar", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>CommandResponse</returns>
        public CommandResponse UpdateOrganisationHeader(string id)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = UpdateOrganisationHeaderWithHttpInfo(id);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> UpdateOrganisationHeaderWithHttpInfo(string id)
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->UpdateOrganisationHeader");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<CommandResponse>("/v1/Organisation/{id}/header", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateOrganisationHeader", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> UpdateOrganisationHeaderAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await UpdateOrganisationHeaderWithHttpInfoAsync(id, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> UpdateOrganisationHeaderWithHttpInfoAsync(string id, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->UpdateOrganisationHeader");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<CommandResponse>("/v1/Organisation/{id}/header", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateOrganisationHeader", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="updateUserInOrganisationCommandRequestBody"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse UpdateUserInOrganisation(string orgCodeName, string userId, UpdateUserInOrganisationCommandRequestBody? updateUserInOrganisationCommandRequestBody = default(UpdateUserInOrganisationCommandRequestBody?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = UpdateUserInOrganisationWithHttpInfo(orgCodeName, userId, updateUserInOrganisationCommandRequestBody);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="updateUserInOrganisationCommandRequestBody"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> UpdateUserInOrganisationWithHttpInfo(string orgCodeName, string userId, UpdateUserInOrganisationCommandRequestBody? updateUserInOrganisationCommandRequestBody = default(UpdateUserInOrganisationCommandRequestBody?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->UpdateUserInOrganisation");

            // verify the required parameter 'userId' is set
            if (userId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'userId' when calling OrganisationApi->UpdateUserInOrganisation");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("userId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(userId)); // path parameter
            localVarRequestOptions.Data = updateUserInOrganisationCommandRequestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<CommandResponse>("/v1/Organisation/{orgCodeName}/users/{userId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateUserInOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="updateUserInOrganisationCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> UpdateUserInOrganisationAsync(string orgCodeName, string userId, UpdateUserInOrganisationCommandRequestBody? updateUserInOrganisationCommandRequestBody = default(UpdateUserInOrganisationCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await UpdateUserInOrganisationWithHttpInfoAsync(orgCodeName, userId, updateUserInOrganisationCommandRequestBody, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="userId"></param>
        /// <param name="updateUserInOrganisationCommandRequestBody"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> UpdateUserInOrganisationWithHttpInfoAsync(string orgCodeName, string userId, UpdateUserInOrganisationCommandRequestBody? updateUserInOrganisationCommandRequestBody = default(UpdateUserInOrganisationCommandRequestBody?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling OrganisationApi->UpdateUserInOrganisation");

            // verify the required parameter 'userId' is set
            if (userId == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'userId' when calling OrganisationApi->UpdateUserInOrganisation");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("userId", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(userId)); // path parameter
            localVarRequestOptions.Data = updateUserInOrganisationCommandRequestBody;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<CommandResponse>("/v1/Organisation/{orgCodeName}/users/{userId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateUserInOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ValidationResultModelCommandResponse</returns>
        public ValidationResultModelCommandResponse ValidateOrganisation(string? name = default(string?), string? codeName = default(string?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> localVarResponse = ValidateOrganisationWithHttpInfo(name, codeName);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ApiResponse of ValidationResultModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> ValidateOrganisationWithHttpInfo(string? name = default(string?), string? codeName = default(string?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (name != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "Name", name));
            }
            if (codeName != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "CodeName", codeName));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<ValidationResultModelCommandResponse>("/v1/Organisation/validate", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ValidateOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ValidationResultModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<ValidationResultModelCommandResponse> ValidateOrganisationAsync(string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> localVarResponse = await ValidateOrganisationWithHttpInfoAsync(name, codeName, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ValidationResultModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse>> ValidateOrganisationWithHttpInfoAsync(string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (name != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "Name", name));
            }
            if (codeName != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "CodeName", codeName));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<ValidationResultModelCommandResponse>("/v1/Organisation/validate", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ValidateOrganisation", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ValidationResultModelCommandResponse</returns>
        public ValidationResultModelCommandResponse ValidateOrganisationById(string id, string? name = default(string?), string? codeName = default(string?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> localVarResponse = ValidateOrganisationByIdWithHttpInfo(id, name, codeName);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <returns>ApiResponse of ValidationResultModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> ValidateOrganisationByIdWithHttpInfo(string id, string? name = default(string?), string? codeName = default(string?))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->ValidateOrganisationById");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            if (name != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "Name", name));
            }
            if (codeName != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "CodeName", codeName));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<ValidationResultModelCommandResponse>("/v1/Organisation/{id}/validate", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ValidateOrganisationById", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ValidationResultModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<ValidationResultModelCommandResponse> ValidateOrganisationByIdAsync(string id, string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse> localVarResponse = await ValidateOrganisationByIdWithHttpInfoAsync(id, name, codeName, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="name"> (optional)</param>
        /// <param name="codeName"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ValidationResultModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<ValidationResultModelCommandResponse>> ValidateOrganisationByIdWithHttpInfoAsync(string id, string? name = default(string?), string? codeName = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'id' is set
            if (id == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'id' when calling OrganisationApi->ValidateOrganisationById");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(id)); // path parameter
            if (name != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "Name", name));
            }
            if (codeName != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "CodeName", codeName));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<ValidationResultModelCommandResponse>("/v1/Organisation/{id}/validate", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ValidateOrganisationById", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
