﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Audit;
using Axon.Core.Api.Services.Audit;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using JetBrains.Annotations;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.Audit
{
    [UsedImplicitly]
    internal class GetAuditFiltersUrlQueryRequestHandler : IRequestHandler<GetAuditFiltersUrlQueryRequest, CommandResponse<string>>
    {
        public IAppRepository appRepository;
        public IAuditEndpointService auditODataEndpointService;
        private readonly ILogger<GetAuditFiltersUrlQueryRequestHandler> logger;

        public GetAuditFiltersUrlQueryRequestHandler(IAppRepository appRepository,
            IAuditEndpointService auditODataEndpointService,
            ILogger<GetAuditFiltersUrlQueryRequestHandler> logger)
        {
            Guard.IsNotNull(appRepository);
            this.appRepository = appRepository;
            Guard.IsNotNull(auditODataEndpointService);
            this.auditODataEndpointService = auditODataEndpointService;
            Guard.IsNotNull(logger);
            this.logger = logger;
        }

        public async Task<CommandResponse<string>> Handle(GetAuditFiltersUrlQueryRequest request, CancellationToken cancellationToken)
        {
            var appEntity = await appRepository.GetItemByAppCodeNameAsync(request.AppCodeName);

            if (!appEntity.ShowAudits)
            {
                logger.LogError("Auditing is not enabled for this application: {AppCodeName}.", request.AppCodeName);
                return new CommandResponse<string>() { status = HttpStatusCode.Forbidden, title = "Not available on this application" };
            }

            return await auditODataEndpointService.GetODataFilterUrl(request);
        }
    }
}
