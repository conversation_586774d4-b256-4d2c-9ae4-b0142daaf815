using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Domain.Entities.Base;
using Axon.Core.Domain.Interfaces.Persistence;
using MediatR;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands
{
#pragma warning disable S2436 // Generics enable reuse across entities of standard CRUD type features from the API down to the Repository
    internal abstract class BaseCommandHandler<TEntity, TRequest, TResponse> : IRequestHandler<TRequest, TResponse>
#pragma warning restore S2436 // Types and methods should not have too many generic parameters
        where TEntity : BaseEntity where TRequest : IRequest<TResponse>
    {
        protected IMessageBus MessageBus { get; }
        protected readonly IRepository<TEntity> Repo;
        protected readonly IMapper Mapper;

        protected BaseCommandHandler(IRepository<TEntity> repo,
            IMapper mapper, IMessageBus messageBus)
        {
            MessageBus = messageBus ?? throw new ArgumentNullException(nameof(messageBus));
            this.Repo = repo ?? throw new ArgumentNullException(nameof(repo));
            this.Mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public abstract Task<TResponse> Handle(TRequest request, CancellationToken cancellationToken);
    }
}