/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// ThemeConfigModel
    /// </summary>
    [DataContract(Name = "ThemeConfigModel")]
    public partial class ThemeConfigModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ThemeConfigModel" /> class.
        /// </summary>
        /// <param name="avatarUrl">avatarUrl.</param>
        /// <param name="headerUrl">headerUrl.</param>
        /// <param name="stylesheetUrl">stylesheetUrl.</param>
        /// <param name="defaultColours">defaultColours.</param>
        /// <param name="darkColours">darkColours.</param>
        public ThemeConfigModel(string avatarUrl = default(string), string headerUrl = default(string), string stylesheetUrl = default(string), OrgColourModel defaultColours = default(OrgColourModel), OrgColourModel darkColours = default(OrgColourModel))
        {
            this.AvatarUrl = avatarUrl;
            this.HeaderUrl = headerUrl;
            this.StylesheetUrl = stylesheetUrl;
            this.DefaultColours = defaultColours;
            this.DarkColours = darkColours;
        }

        /// <summary>
        /// Gets or Sets AvatarUrl
        /// </summary>
        [DataMember(Name = "avatarUrl", EmitDefaultValue = true)]
        public string AvatarUrl { get; set; }

        /// <summary>
        /// Gets or Sets HeaderUrl
        /// </summary>
        [DataMember(Name = "headerUrl", EmitDefaultValue = true)]
        public string HeaderUrl { get; set; }

        /// <summary>
        /// Gets or Sets StylesheetUrl
        /// </summary>
        [DataMember(Name = "stylesheetUrl", EmitDefaultValue = true)]
        public string StylesheetUrl { get; set; }

        /// <summary>
        /// Gets or Sets DefaultColours
        /// </summary>
        [DataMember(Name = "defaultColours", EmitDefaultValue = false)]
        public OrgColourModel DefaultColours { get; set; }

        /// <summary>
        /// Gets or Sets DarkColours
        /// </summary>
        [DataMember(Name = "darkColours", EmitDefaultValue = false)]
        public OrgColourModel DarkColours { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class ThemeConfigModel {\n");
            sb.Append("  AvatarUrl: ").Append(AvatarUrl).Append("\n");
            sb.Append("  HeaderUrl: ").Append(HeaderUrl).Append("\n");
            sb.Append("  StylesheetUrl: ").Append(StylesheetUrl).Append("\n");
            sb.Append("  DefaultColours: ").Append(DefaultColours).Append("\n");
            sb.Append("  DarkColours: ").Append(DarkColours).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
