﻿using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.UserPreference.Update;

internal class UpdateUserPreferenceCommandHandler : BaseCommandHandler<UserPreferenceEntity, UpdateUserPreferenceCommandRequest, CommandResponse>
{
    private readonly ICurrentUserProvider currentUserProvider;
    private readonly IUserPreferenceService userPreferenceService;

    public UpdateUserPreferenceCommandHandler(IUserPreferenceRepository repo, IMapper mapper, IMessageBus messageBus, ICurrentUserProvider currentUserProvider,
        IUserPreferenceService userPreferenceService) : base(repo, mapper, messageBus)
    {
        Guard.IsNotNull(currentUserProvider);
        this.currentUserProvider = currentUserProvider;
        Guard.IsNotNull(userPreferenceService);
        this.userPreferenceService = userPreferenceService;
    }

    public override async Task<CommandResponse> Handle(UpdateUserPreferenceCommandRequest request, CancellationToken cancellationToken)
    {
        var repo = (IUserPreferenceRepository) Repo;
        var userContext = currentUserProvider.GetUserContext();
        var preferences = await repo.GetItemAsync(userContext.ObjectId);

        if (preferences == null)
            return await userPreferenceService.CreatePreferences(userContext.ObjectId, request.Model, cancellationToken);

        foreach (var pref in request.Model.UserPreferences)
            if (preferences.UserPreferences.ContainsKey(pref.Key))
                preferences.UserPreferences[pref.Key] = request.Model.UserPreferences[pref.Key];
            else
                preferences.UserPreferences.Add(pref.Key, pref.Value);

        return await userPreferenceService.UpdatePreferences(preferences, request.Model.UserPreferences, cancellationToken);
    }
}