﻿using Axon.Core.Domain.Models;
using FluentValidation;

namespace Axon.Core.Api.Validators.AppOrganisationSettings;

public class DatabaseSettingValueValidator : AbstractValidator<DatabaseSettingValue>
{
    public DatabaseSettingValueValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty();
        RuleFor(x => x.Value)
            .NotEmpty();
        RuleFor(x => x.Type)
            .NotEmpty();
        RuleFor(x => x.ConnectionType)
            .NotEmpty();
    }
}