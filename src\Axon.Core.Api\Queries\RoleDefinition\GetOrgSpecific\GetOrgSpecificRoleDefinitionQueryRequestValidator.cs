﻿using Axon.Core.Api.Queries.RoleDefinition.GetByAppOrg;
using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.AppAccess.RoleDefinitionListQuery;

[UsedImplicitly]
public class GetOrgSpecificRoleDefinitionQueryRequestValidator : AbstractValidator<GetOrgSpecificRoleDefinitionQueryRequest>
{
    public GetOrgSpecificRoleDefinitionQueryRequestValidator()
    {
        RuleFor(x => x.AppCode)
            .MustBeAValidCodeName();
        RuleFor(x => x.OrgCodeName)
            .MustBeAValidCodeName();
    }
}