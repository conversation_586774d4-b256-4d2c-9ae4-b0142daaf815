﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.AppGroup.ListUnassignedGroups;

[UsedImplicitly]
public class GetUnassignedGroupsQueryRequestValidator : AbstractValidator<GetUnassignedGroupsQueryRequest>
{
    public GetUnassignedGroupsQueryRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
    }
}