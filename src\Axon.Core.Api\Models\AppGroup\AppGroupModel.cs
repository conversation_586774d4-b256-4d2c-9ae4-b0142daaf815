﻿using Axon.Core.Api.Models.Role;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.AppGroup
{
    public class AppGroupModel
    {
        [Required] public string Id { get; set; }
        [Required] public string AppCodeName { get; set; }
        [Required] public string OrganisationCodeName { get; set; }
        [Required] public string GroupName { get; set; }
        [Required] public string Role { get; set; }
        [Required]public RoleModel.ScopeResources[] Scopes { get; set; }
        [Required] public int MembersCount { get; set; }

    }
}
