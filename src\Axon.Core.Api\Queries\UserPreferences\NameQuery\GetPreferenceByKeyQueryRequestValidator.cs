﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.UserPreferences.NameQuery;

[UsedImplicitly]
public class GetPreferenceByKeyQueryRequestValidator : AbstractValidator<GetPreferenceByKeyQueryRequest>
{
    public GetPreferenceByKeyQueryRequestValidator()
    {
        RuleFor(x => x.Key)
            .MustBeAValidPreferencesKey();
    }
}