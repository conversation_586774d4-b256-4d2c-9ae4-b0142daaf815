﻿using Axon.Core.Api.Commands;
using JetBrains.Annotations;
using MediatR;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Models.Role;
using Axon.Core.Api.Services.Role;

namespace Axon.Core.Api.Queries.Role.ListQuery
{
    [UsedImplicitly]
    internal class GetAllRoleListQueryRequestHandler : IRequestHandler<GetAllRoleListQueryRequest, CommandResponse<IEnumerable<RoleModel>>>
    {
        private readonly IRoleModelService roleModelService;

        public GetAllRoleListQueryRequestHandler(IRoleModelService roleModelService)
        {
            this.roleModelService = roleModelService;
        }

        public async Task<CommandResponse<IEnumerable<RoleModel>>> Handle(GetAllRoleListQueryRequest request, CancellationToken cancellationToken)
        {
            var roleModelList = await roleModelService.GetRoleModelsAsync(request.OrganisationCodeName, request.AppCodeName);

            return CommandResponse<IEnumerable<RoleModel>>.Data(roleModelList);
        }
    }
}
