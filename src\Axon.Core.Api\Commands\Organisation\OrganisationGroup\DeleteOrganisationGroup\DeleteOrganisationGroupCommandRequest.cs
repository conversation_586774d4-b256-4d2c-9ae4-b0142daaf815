﻿using MediatR;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.DeleteOrganisationGroup;

public class DeleteOrganisationGroupCommandRequest : IRequest<CommandResponse>
{
    public string OrganisationCodeName { get; }
    public string GroupId { get; }

    public DeleteOrganisationGroupCommandRequest(string organisationCodeName, string groupId)
    {
        OrganisationCodeName = organisationCodeName;
        GroupId = groupId;
    }
}