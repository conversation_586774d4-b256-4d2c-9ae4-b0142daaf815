﻿using System;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using JetBrains.Annotations;
using MediatR;
using Axon.Core.Api.Models.Organisation;
using System.Collections.Generic;
using System.Threading;
using System.Linq;
using AutoMapper;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Models.Access;
using Axon.Core.Domain.Extensions;
using CommunityToolkit.Diagnostics;
using Axon.Core.Api.Services.Organisation;

namespace Axon.Core.Api.Queries.Admin.Organisation.ListQuery
{
    [UsedImplicitly]
    internal class GetAdminOrganisationListQueryHandler : IRequestHandler<GetAdminOrganisationQueryRequest, CommandResponse<IEnumerable<OrganisationModel>>>
    {
        private readonly IOrganisationRequestService listService;

        public GetAdminOrganisationListQueryHandler(IOrganisationRequestService listService)
        {
            this.listService = listService;
        }

        public async Task<CommandResponse<IEnumerable<OrganisationModel>>> Handle(GetAdminOrganisationQueryRequest request, CancellationToken cancellationToken)
        {
            request.ListParams.Filter = String.IsNullOrEmpty(request.ListParams.Filter) ? "IsDeleted eq false" : request.ListParams.Filter + "and IsDeleted eq false";

            return await listService.GetFilteredOrganisations(request.ListParams);
        }
    }
}