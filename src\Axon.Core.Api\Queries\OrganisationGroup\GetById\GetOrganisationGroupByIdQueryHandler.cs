﻿using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Infrastructure.Extensions;
using Axon.Core.Shared.Api;
using Phlex.Core.Api.Abstractions.Models;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Queries.OrganisationGroup.GetById;

internal class GetOrganisationGroupByIdQueryHandler : BaseCommandHandler<AccessEntity, GetOrganisationGroupByIdQueryRequest, CommandResponse<OrganisationGroupPagedModel>>
{
    private readonly IAccessRepository accessRepository;
    private readonly IOrganisationRepository organisationRepository;
    private readonly IGroupRepository groupRepository;

    public GetOrganisationGroupByIdQueryHandler(
        IAccessRepository accessRepository,
        IMapper mapper,
        IMessageBus messageBus,
        IOrganisationRepository organisationRepository,
        IGroupRepository groupRepository)
        : base(accessRepository, mapper, messageBus)
    {
        this.accessRepository = accessRepository;
        this.organisationRepository = organisationRepository;
        this.groupRepository = groupRepository;
    }

    public override async Task<CommandResponse<OrganisationGroupPagedModel>> Handle(GetOrganisationGroupByIdQueryRequest request, CancellationToken cancellationToken)
    {
        var organisationEntity = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);
        if (organisationEntity == null)
            return CommandResponse<OrganisationGroupPagedModel>.NotFound(nameof(request.OrganisationCodeName), request.OrganisationCodeName);

        var groupEntity = await groupRepository.GetItemAsync(request.OrganisationGroupId);
        if (groupEntity == null)
            return CommandResponse<OrganisationGroupPagedModel>.NotFound(nameof(request.OrganisationGroupId), request.OrganisationGroupId);

        if (!string.Equals(groupEntity.OrganisationCodeName, organisationEntity.CodeName, StringComparison.InvariantCultureIgnoreCase))
            return CommandResponse<OrganisationGroupPagedModel>.BadRequest(nameof(request.OrganisationGroupId), request.OrganisationGroupId);

        var accessEntities = (await accessRepository.GetAccessItemsForGroupAsync(groupEntity.Id, AccessType.GroupAccess))
            .Where(entity => entity.RoleId == null)
            .AsQueryable()
            .FilteredResult(request.ListParams.Filter);

        if (!request.ListParams.OrderBy.Any())
            request.ListParams.OrderBy = OrderByClauses.Parse($"{nameof(AccessEntity.User)}.{nameof(AccessEntity.User.Name)}");

        var pagedUsers = accessEntities
            .OrderedResult(request.ListParams.OrderBy)
            .PagedResult(request.ListParams.Offset, request.ListParams.Limit)
            .Select(accessEntity => new OrganisationGroupUserModel
            {
                UserId = accessEntity.User.Id,
                Email = accessEntity.User.Email,
                Name = accessEntity.User.Name,
            }).ToList();

        var organisationGroupModel = new OrganisationGroupPagedModel()
        {
            Id = groupEntity.Id,
            Name = groupEntity.Name,
            Users = new ApiPagedListResult<OrganisationGroupUserModel>(pagedUsers, request.ListParams.Offset, request.ListParams.Limit, accessEntities.Count()),
            CreatedAt = groupEntity.CreatedAt,
            LastUpdatedDate = groupEntity.LastUpdatedDate
        };

        return CommandResponse<OrganisationGroupPagedModel>.Data(organisationGroupModel);
    }
}
