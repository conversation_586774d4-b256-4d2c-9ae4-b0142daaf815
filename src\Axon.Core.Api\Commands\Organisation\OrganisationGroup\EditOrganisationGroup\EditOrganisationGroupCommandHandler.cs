﻿using AutoMapper;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Api.Services.OrganisationGroup;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.EditOrganisationGroup;

internal class EditOrganisationGroupCommandHandler : BaseCommandHandler<AccessEntity, EditOrganisationGroupCommandRequest, CommandResponse>
{
    private readonly IOrganisationRepository organisationRepository;
    private readonly IGroupRepository groupRepository;
    private readonly IClientDetailsProvider clientDetailsProvider;
    private readonly ICorrelationIdProvider correlationIdProvider;
    private readonly IAuditService<TenantAuditExtensions> auditService;
    private readonly IAccessRepository accessRepository;
    private readonly ILogger<EditOrganisationGroupCommandHandler> logger;
    private readonly IOrganisationGroupSynchroniser synchroniser;

    public EditOrganisationGroupCommandHandler(
        IMapper mapper,
        IMessageBus messageBus,
        IOrganisationRepository organisationRepository,
        IGroupRepository groupRepository,
        IClientDetailsProvider clientDetailsProvider,
        ICorrelationIdProvider correlationIdProvider,
        IAuditService<TenantAuditExtensions> auditService,
        IAccessRepository accessRepository,
        ILogger<EditOrganisationGroupCommandHandler> logger,
        IOrganisationGroupSynchroniser synchroniser)
        : base(accessRepository, mapper, messageBus)
    {
        this.organisationRepository = organisationRepository;
        this.groupRepository = groupRepository;
        this.clientDetailsProvider = clientDetailsProvider;
        this.correlationIdProvider = correlationIdProvider;
        this.auditService = auditService;
        this.accessRepository = accessRepository;
        this.logger = logger;
        this.synchroniser = synchroniser;
    }

    public override async Task<CommandResponse> Handle(EditOrganisationGroupCommandRequest request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.EditOrganisationGroupCommandRequestBody.Name))
            return CommandResponse.BadRequest(nameof(request.EditOrganisationGroupCommandRequestBody.Name), "Group name missing");

        var organisationEntity = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);
        if (organisationEntity == null)
        {
            logger.LogWarning("Organisation {organisationCodeName} does not exist.", request.OrganisationCodeName);
            return CommandResponse.NotFound(nameof(OrganisationEntity), request.OrganisationCodeName);
        }

        var organisationGroups = await groupRepository.GetForOrganisationAsync(request.OrganisationCodeName);
        
        var organisationGroup = organisationGroups.FirstOrDefault(x => string.Equals(x.Id, request.OrganisationGroupId, StringComparison.OrdinalIgnoreCase));
        if (organisationGroup == null)
        {
            logger.LogWarning("Group {organisationGroup} does not exist.", request.EditOrganisationGroupCommandRequestBody.Name);
            return CommandResponse.NotFound(nameof(GroupEntity), request.EditOrganisationGroupCommandRequestBody.Name);
        }

        var existingGroupEntity = organisationGroups.FirstOrDefault(x => string.Equals(x.Name, request.EditOrganisationGroupCommandRequestBody.Name, StringComparison.InvariantCultureIgnoreCase));
        if (existingGroupEntity != null && !string.Equals(existingGroupEntity.Id, request.OrganisationGroupId, StringComparison.OrdinalIgnoreCase))
            return CommandResponse.Conflict(nameof(existingGroupEntity), existingGroupEntity.Id, existingGroupEntity.Name);

        await RenameGroup(organisationGroup, request.EditOrganisationGroupCommandRequestBody.Name);

        var accessEntities = await accessRepository.GetAccessItemsForGroupAsync(organisationGroup.Id, AccessType.GroupAccess);

        var users = accessEntities
            .Select(accessEntity => new OrganisationGroupUserModel
            {
                UserId = accessEntity.User.Id,
                Email = accessEntity.User.Email,
                Name = accessEntity.User.Name,
            })
            .ToArray();

        return CommandResponse.Data(new OrganisationGroupModel()
        {
            Id = organisationGroup.Id,
            Name = organisationGroup.Name,
            Users = users,
            CreatedAt = organisationGroup.CreatedAt,
            LastUpdatedDate = organisationGroup.LastUpdatedDate
        });
    }

    private async Task RenameGroup(GroupEntity organisationGroup, string groupName)
    {
        var renameGroup = !organisationGroup.Name.Equals(groupName, StringComparison.OrdinalIgnoreCase);
        if (!renameGroup) 
            return;

        var correlationId = correlationIdProvider.Provide();
        var clientDetails = clientDetailsProvider.Provide(); 
        var tenantAuditExtensions = new TenantAuditExtensions(
            AuditEventCategories.Group,
            AuditEventDescriptions.GroupRenamed(organisationGroup.Name, groupName),
            correlationId,
            clientDetails,
            organisationGroup.OrganisationCodeName);

        await auditService.LogAsync(AuditEventTypes.GroupUpdated, tenantAuditExtensions, organisationGroup,
            async () =>
            {
                organisationGroup.Name = groupName;
                organisationGroup.LastUpdatedDate = DateTime.UtcNow;

                await groupRepository.UpdateItemAsync(organisationGroup.Id, organisationGroup);
            });

        await synchroniser.SynchroniseGroupRenameWithAppGroups(organisationGroup.Id);

    }
}