using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Extensions;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using Phlex.Core.MessageBus;
using static Axon.Core.Domain.Entities.OrganisationEntity;

namespace Axon.Core.Api.Commands.Organisation.AddAppTo;

internal class AddAppToOrganisationCommandHandler : BaseCommandHandler<OrganisationEntity, AddAppToOrganisationCommandRequest, CommandResponse>
{
    private readonly IAppRepository appRepo;

    public AddAppToOrganisationCommandHandler(IOrganisationRepository orgRepo, IAppRepository appRepo, IMapper mapper, IMessageBus messageBus)
        : base(orgRepo, mapper, messageBus)
    {
        Guard.IsNotNull(appRepo);
        this.appRepo = appRepo;
    }

    public override async Task<CommandResponse> Handle(AddAppToOrganisationCommandRequest request, CancellationToken cancellationToken)
    {
        if (!await Repo.TryGetItemAsync(request.OrganisationId, out var org)) return CommandResponse.NotFound(nameof(OrganisationEntity), request.OrganisationId);

        if (!await appRepo.TryGetItemAsync(request.AppId, out var app)) return CommandResponse.NotFound(nameof(AppEntity), request.AppId);

        var apps = new List<AppConfigEntity>();
        if (org.Apps != null) apps.AddRange(org.Apps);

        if (apps.Any(x => x.AppId == app.Id))
            return CommandResponse.Failed(nameof(request.AppId), $"Application with id  `{request.AppId}` already exists in organisation with id `{request.OrganisationId}`");

        apps.Add(new AppConfigEntity
        {
            AppId = app.Id,
            DisplayName = app.DisplayName,
            Enabled = true,
            AppCodeName = app.AppCodeName,
            Status = AppStatus.ConfigComplete
        });

        org.Apps = apps;

        await Repo.UpdateItemAsync(org.Id, org);

        var mappedEvent = Mapper.Map<OrgUpdatedEvent>(org);
        await MessageBus.PublishAsync(mappedEvent, cancellationToken: cancellationToken);

        return CommandResponse.Success();
    }
}