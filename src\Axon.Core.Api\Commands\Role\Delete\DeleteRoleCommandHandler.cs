﻿using AutoMapper;
using Axon.Core.Api.Models.Role;
using Axon.Core.Contracts;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Exceptions;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Phlex.Core.MessageBus;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using System;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Extensions;
using Phlex.Core.FunctionalExtensions.Results;
using Axon.Core.Shared.Audit.Services;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;

namespace Axon.Core.Api.Commands.Role.Delete
{
    internal class DeleteRoleCommandHandler : DeleteCommandHandler<RoleEntity, RoleModel, AppRoleUpdatedEvent>
    {
        private readonly IAppGroupRepository appGroupRepository;
        private readonly IAccessRepository accessRepository;
        private readonly IClientDetailsProvider clientDetailsProvider;
        private readonly ICorrelationIdProvider correlationIdProvider;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAppRepository appRepository;
        private readonly IAuditService<TenantAuditExtensions> auditService;
        private readonly ILogger<DeleteRoleCommandHandler> logger;

        public DeleteRoleCommandHandler(
            IAppGroupRepository appGroupRepository,
            IAccessRepository accessRepository,
            IRoleRepository repo, 
            IMapper mapper, 
            IMessageBus messageBus,
            IClientDetailsProvider clientDetailsProvider,
            ICorrelationIdProvider correlationIdProvider,
            IOrganisationRepository organisationRepository,
            IAppRepository appRepository,
            IAuditService<TenantAuditExtensions> auditService,
            ILogger<DeleteRoleCommandHandler> logger) 
            : base(repo, mapper, messageBus)
        {
            this.appGroupRepository = appGroupRepository;
            this.accessRepository = accessRepository;
            this.clientDetailsProvider = clientDetailsProvider;
            this.correlationIdProvider = correlationIdProvider;
            this.organisationRepository = organisationRepository;
            this.appRepository = appRepository;
            this.auditService = auditService;
            this.logger = logger;
        }

        public override async Task<Result> Handle(DeleteCommandRequest<RoleModel> request, CancellationToken cancellationToken)
        {
            var roleRepository = (IRoleRepository)Repo;
            var existingEntity = await roleRepository.GetItemAsync(request.Id);
            if (existingEntity == null)
            {
                throw new EntityNotFoundException(nameof(RoleEntity), request.Id);
            }

            if (existingEntity.RoleType != RoleType.Custom)
            {
                throw new BadRequestException("Unable to delete a system role");
            }

            var correlationId = correlationIdProvider.Provide();
            var clientDetails = clientDetailsProvider.Provide();

            var org = await organisationRepository.GetItemByCodeNameAsync(existingEntity.OrganisationCodeName);
            if (org == default(OrganisationEntity))
            {
                return Result.Failure($"Organisation `{existingEntity.OrganisationCodeName}` does not exist");
            }

            var appId = await appRepository.GetAppIdByAppCode(existingEntity.AppCodeName);
            if (appId == default(string))
            {
                return Result.Failure($"Unable to delete, App `{existingEntity.AppCodeName}` does not exist");
            }

            await DeleteRole(request, existingEntity, correlationId, clientDetails, org, appId);
            await DeleteAppGroups(existingEntity, correlationId, clientDetails, org, appId);
            await DeleteGroupAccess(existingEntity, correlationId, clientDetails);

            return Result.Success();
        }

        private async Task DeleteRole(DeleteCommandRequest<RoleModel> request, RoleEntity roleEntity, Guid correlationId, ClientDetails clientDetails, OrganisationEntity org, string appId)
        {
            var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Role, AuditEventDescriptions.RoleDeleted, correlationId, clientDetails, roleEntity.OrganisationCodeName);


            await auditService.LogAsync(AuditEventTypes.RoleDeleted, tenantAuditExtensions, roleEntity,
                async () =>
                {
                    await Repo.DeleteItemAsync(request.Id);
                });

            var mappedEvent = Mapper.Map<AppRoleUpdatedEvent>(roleEntity);
            mappedEvent.OrgId = org.Id;
            mappedEvent.AppId = appId;
            mappedEvent.Action = EventActionType.Deleted;
            await MessageBus.PublishAsync(mappedEvent);
        }

        private async Task DeleteAppGroups(RoleEntity roleEntity, Guid correlationId, ClientDetails clientDetails, OrganisationEntity org, string appId)
        {
            var appGroupEntities = await appGroupRepository.GetAppGroupsByRoleIdAsync(roleEntity.OrganisationCodeName, roleEntity.AppCodeName, roleEntity.Id);

            if (appGroupEntities.Any())
            {
                var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.AppGroup, AuditEventDescriptions.AppGroupDeleted, correlationId, clientDetails, roleEntity.OrganisationCodeName);

                await auditService.LogAsync(AuditEventTypes.AppGroupDeleted, tenantAuditExtensions, appGroupEntities,
                    async () =>
                    {
                        await appGroupRepository.BulkDeleteItemsAsync(appGroupEntities.ToList());
                    });

                foreach (var groupEntity in appGroupEntities)
                {
                    var mappedEvent = Mapper.Map<AppGroupUpdatedEvent>(groupEntity);
                    mappedEvent.OrgId = org.Id;
                    mappedEvent.AppId = appId;
                    mappedEvent.Action = EventActionType.Deleted;
                    await MessageBus.PublishAsync(mappedEvent);
                }
            }
        }

        private async Task DeleteGroupAccess(RoleEntity existingEntity, Guid correlationId, ClientDetails clientDetails)
        {
            var accessEntities = await accessRepository.GetAccessItemsForRoleAsync(existingEntity.OrganisationCodeName, existingEntity.AppCodeName, existingEntity.Id);

            if (accessEntities.Any())
            {
                await LogAppRoleUsers(accessEntities.ToList());
                var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Access, AuditEventDescriptions.AccessDeleted, correlationId, clientDetails,
                    existingEntity.OrganisationCodeName); 

                await auditService.LogAsync(AuditEventTypes.AccessDeleted, tenantAuditExtensions, accessEntities,
                    async () => { await accessRepository.BulkDeleteItemsAsync(accessEntities.ToList()); });
            }
        }

        private async Task LogAppRoleUsers(List<AccessEntity> accessEntities)
        {
            foreach(var user in accessEntities)
            {
                var appUserEvent = new AppUserUpdatedEvent()
                {
                    OrgCodeName = user.OrganisationCodeName,
                    AppCodeName = user.AppCodeName,
                    UserId = user.Id,
                    Action = EventActionType.Deleted,
                    AppId = user.AppId,
                    OrgId = user.OrganisationId
                };
                await MessageBus.PublishAsync(appUserEvent);
                logger.LogInformation("""
                    AppUserUpdatedEvent sent - 
                    OrgCodeName:{orgCodeName}, 
                    AppCodeName:{appCodeName},
                    UserId:{userId},
                    EventAction:{action},
                    OrgId:{orgId}
                    AppId:{appId}
                    """, [appUserEvent.OrgCodeName, appUserEvent.AppCodeName, appUserEvent.UserId, appUserEvent.Action, appUserEvent.OrgId, appUserEvent.AppId]
                    );
            }
        }
    }
}
