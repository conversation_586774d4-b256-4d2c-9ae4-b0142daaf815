﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.UserAccess.CurrentUserPermissionsQuery;

[UsedImplicitly]
public class GetCurrentUserPermissionAccessQueryValidator : AbstractValidator<GetCurrentUserPermissionAccessQuery>
{
    public GetCurrentUserPermissionAccessQueryValidator()
    {
        RuleFor(x => x.Permission)
            .MustBeAValidPermission();
    }
}