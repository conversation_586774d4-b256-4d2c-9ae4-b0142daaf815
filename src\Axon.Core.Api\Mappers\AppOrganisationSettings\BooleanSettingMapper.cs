﻿using System.IO;
using System.Text.Json;

namespace Axon.Core.Api.Mappers.AppOrganisationSettings;

public class BooleanSettingMapper : ISettingMapper
{
    private const string CANNOT_PARSE_EXCEPTION_MESSAGE = "Cannot convert setting value to boolean.";

    public object Map(string settingValue)
    {
        if (!bool.TryParse(settingValue, out var result))
            throw new InvalidDataException(CANNOT_PARSE_EXCEPTION_MESSAGE);
        return result;
    }

    public object Map(JsonElement settingValue)
    {
        if (settingValue.ValueKind is JsonValueKind.String)
            return GetBooleanFromString(settingValue);

        try
        {
            return settingValue.GetBoolean();
        }
        catch
        {
            throw new InvalidDataException(CANNOT_PARSE_EXCEPTION_MESSAGE);
        }
    }

    private static bool GetBooleanFromString(JsonElement settingValue)
    {
        var stringValue = settingValue.ToString()!;
        if (bool.TryParse(stringValue, out var result))
            return result;
        throw new InvalidDataException(CANNOT_PARSE_EXCEPTION_MESSAGE);
    }
}