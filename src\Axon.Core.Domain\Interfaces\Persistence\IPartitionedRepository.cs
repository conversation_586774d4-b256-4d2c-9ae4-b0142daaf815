﻿using Axon.Core.Domain.Entities.Base;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;

namespace Axon.Core.Domain.Interfaces.Persistence
{
    /// <summary>
    /// Repository which relies on being passed the partiton key
    /// </summary>
    /// <typeparam name="T">The type that the repository manages</typeparam>
    public interface IPartitionedRepository<T> where T : BaseEntity
    {
        Task<T> GetItemAsync(string id, PartitionKey partitionKey);
        Task<string> AddItemAsync(T item, PartitionKey partitionKey);
        Task UpdateItemAsync(string id, T item, PartitionKey partitionKey);
        Task DeleteItemAsync(string id, PartitionKey partitionKey);
        IOrderedQueryable<T> GetAllLinqQueryable();
    }
}
