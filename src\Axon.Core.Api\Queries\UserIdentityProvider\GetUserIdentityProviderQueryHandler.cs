﻿using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.UserIdentityProvider;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.UserIdentityProvider
{
    internal sealed class GetUserIdentityProviderQueryHandler : IRequestHandler<GetUserIdentityProviderQueryRequest, CommandResponse<UserIdentityProviderModel>>
    {
        private readonly IUserRequestContext userRequestContext;
        private readonly IIdentityProviderRepository identityProviderRepository;
        private readonly IMapper mapper;
        private readonly ILogger<GetUserIdentityProviderQueryHandler> logger;

        public GetUserIdentityProviderQueryHandler(IUserRequestContext userRequestContext, IIdentityProviderRepository identityProviderRepository,
            IMapper mapper, ILogger<GetUserIdentityProviderQueryHandler> logger)
        {
            Guard.IsNotNull(userRequestContext);
            this.userRequestContext = userRequestContext;
            Guard.IsNotNull(identityProviderRepository);
            this.identityProviderRepository = identityProviderRepository;
            Guard.IsNotNull(mapper);
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<CommandResponse<UserIdentityProviderModel>> Handle(GetUserIdentityProviderQueryRequest request, CancellationToken cancellationToken)
        {
            var usersIdentityProviderId = userRequestContext.GetUserIdentityProviderId();

            if (usersIdentityProviderId == null)
            {
                logger.LogError("Could not find users identity provider id, this should have been present for them to pass authorization checks");
                return CommandResponse<UserIdentityProviderModel>.NotFound("IdentityProvider", null);
            }

            var identityProvider = await identityProviderRepository.GetItemAsync(usersIdentityProviderId);

            if (identityProvider == null)
            {
                logger.LogWarning("Users identity provider: {IdentityProviderId} could not be found", usersIdentityProviderId);
                return CommandResponse<UserIdentityProviderModel>.NotFound("IdentityProvider", usersIdentityProviderId);
            }

            return CommandResponse<UserIdentityProviderModel>.Data(mapper.Map<IdentityProviderEntity, UserIdentityProviderModel>(identityProvider));
        }
    }
}
