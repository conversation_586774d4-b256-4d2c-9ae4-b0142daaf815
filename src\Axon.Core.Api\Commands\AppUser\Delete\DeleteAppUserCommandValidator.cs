﻿using Axon.Core.Api.Commands.AppUser.Create;
using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.AppUser.Delete
{
    [UsedImplicitly]
    public class DeleteAppUserCommandValidator : AbstractValidator<DeleteAppUserCommand>
    {
        public DeleteAppUserCommandValidator()
        {
            RuleFor(x => x.OrgCodeName)
                .MustBeAValidCodeName();
            RuleFor(x => x.AppCodeName)
                .MustBeAValidCodeName();
            RuleFor(x => x.EmailAddress)
                .MustBeAValidEmail();
        }
    }
}
