﻿using System;
using System.IO;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Axon.Core.Api.Infrastructure.OpenApi.Filters;
using Microsoft.Extensions.Configuration;
using Asp.Versioning.ApiExplorer;
using CommunityToolkit.Diagnostics;

namespace Axon.Core.Api.Infrastructure.OpenApi
{
    public class ConfigureSwaggerOptions : IConfigureOptions<SwaggerGenOptions>
    {
        private readonly string titleText;
        private readonly string descriptionText;
        private readonly string deprecatedText;
        private readonly IApiVersionDescriptionProvider provider;
        private readonly IConfiguration configuration;

        public ConfigureSwaggerOptions(IApiVersionDescriptionProvider provider, string titleText, string descriptionText, string deprecatedText,
            IConfiguration configuration)
        {
            this.configuration = configuration;
            Guard.IsNotNull(provider);
            this.provider = provider;
            this.deprecatedText = deprecatedText;
            this.descriptionText = descriptionText;
            this.titleText = titleText;
        }

        public void Configure(SwaggerGenOptions options)
        {
            options.SchemaFilter<IgnoreReadOnlySchemaFilter>();
            // Add a swagger document for each discovered API version
            // note: you might choose to skip or document deprecated API versions differently
            foreach (var description in provider.ApiVersionDescriptions)
                options.SwaggerDoc(description.GroupName, CreateSwaggerInfoForApiVersion(description, titleText, descriptionText, deprecatedText));

            // Set the comments path for the Swagger JSON and UI.
            foreach (var docFile in Directory.GetFiles(AppContext.BaseDirectory, "*Documentation.xml")) options.IncludeXmlComments(docFile);

            var domain = configuration.GetSection("AzureAd")["Domain"] ?? "login.microsoftonline.com";
            var instance = configuration.GetSection("AzureAd")["Instance"];
            var openApiSecurityScheme = new OpenApiSecurityScheme
            {
                Type = SecuritySchemeType.OpenIdConnect,
                OpenIdConnectUrl = new Uri($"https://{domain}{instance}/v2.0/.well-known/openid-configuration"),
            };
            options.AddSecurityDefinition("OpenID", openApiSecurityScheme);
            options.AddSecurityRequirement(new OpenApiSecurityRequirement()
            {
                {openApiSecurityScheme, Array.Empty<string>()}
            });
        }


        /// <summary>
        /// Creates the swagger information for API version description given
        /// </summary>
        /// <param name="versionDescription">The version description.</param>
        /// <param name="titleText">The title text.</param>
        /// <param name="descriptionText">The description text.</param>
        /// <param name="deprecatedText">The deprecated text.</param>
        /// <returns></returns>
        private static OpenApiInfo CreateSwaggerInfoForApiVersion(ApiVersionDescription versionDescription, string titleText, string descriptionText, string deprecatedText)
        {
            var info = new OpenApiInfo
            {
                Version = versionDescription.ApiVersion.ToString(),
                Title = titleText,
                Description = descriptionText
            };
            if (versionDescription.IsDeprecated) info.Description += deprecatedText;

            return info;
        }
    }
}
