﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.App.ValidateApp;

[UsedImplicitly]
public class ValidateAppQueryRequestValidator : AbstractValidator<ValidateAppQueryRequest>
{

    public ValidateAppQueryRequestValidator()
    {
        RuleFor(o => o.Name)
            .MustBeAValidAppName();
        RuleFor(o => o.CodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.Id)
            .MustBeAValidGuid();
    }
}