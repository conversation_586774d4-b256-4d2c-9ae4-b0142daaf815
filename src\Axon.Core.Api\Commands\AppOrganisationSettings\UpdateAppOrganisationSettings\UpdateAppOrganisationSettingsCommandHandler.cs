﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Mappers.AppOrganisationSettings;
using Axon.Core.Api.Validators.AppOrganisationSettings;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Exceptions;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services.KeyVault;
using Axon.Core.Infrastructure.Extensions;
using CommunityToolkit.Diagnostics;
using JetBrains.Annotations;
using MediatR;

namespace Axon.Core.Api.Commands.AppOrganisationSettings.UpdateAppOrganisationSettings;

[UsedImplicitly]
internal class UpdateAppOrganisationSettingsCommandHandler : IRequestHandler<UpdateAppOrganisationSettingsCommandRequest, CommandResponse>
{
    private readonly IAppOrganisationSettingsMapperFactory appOrganisationSettingsMapperFactory;
    private readonly IAppOrganisationSettingsRepository appOrganisationSettingsRepository;
    private readonly IAppOrganisationSettingsValidatorFactory appOrganisationSettingsValidatorFactory;
    private readonly IAppSettingsRepository appSettingsRepository;
    private readonly ISecretClientProvider secretClientProvider;

    public UpdateAppOrganisationSettingsCommandHandler(
        IAppSettingsRepository appSettingsRepository,
        IAppOrganisationSettingsRepository appOrganisationSettingsRepository,
        IAppOrganisationSettingsValidatorFactory appOrganisationSettingsValidatorFactory,
        ISecretClientProvider secretClientProvider,
        IAppOrganisationSettingsMapperFactory appOrganisationSettingsMapperFactory)
    {
        this.appOrganisationSettingsMapperFactory = appOrganisationSettingsMapperFactory;
        Guard.IsNotNull(appSettingsRepository);
        this.appSettingsRepository = appSettingsRepository;
        Guard.IsNotNull(appOrganisationSettingsValidatorFactory);
        this.appOrganisationSettingsValidatorFactory = appOrganisationSettingsValidatorFactory;
        Guard.IsNotNull(appOrganisationSettingsRepository);
        this.appOrganisationSettingsRepository = appOrganisationSettingsRepository;
        Guard.IsNotNull(secretClientProvider);
        this.secretClientProvider = secretClientProvider;
    }

    public async Task<CommandResponse> Handle(UpdateAppOrganisationSettingsCommandRequest request, CancellationToken cancellationToken)
    {
        var appSettings = await appSettingsRepository.GetByAppCodeNameAsync(request.AppCodeName);
        if (appSettings == null)
            throw new EntityNotFoundException($"Settings for AppCodeName {request.AppCodeName} do not exist.");

        if (!appSettings.Settings.TryGetValue(request.SettingName, out var setting))
            throw new EntityNotFoundException($"Setting {request.SettingName} not found in the {request.AppCodeName} application settings.");

        var validator = appOrganisationSettingsValidatorFactory.Create(setting.DataType);
        var result = validator.Validate(request.Value, out var errorMessage, setting);
        if (result == false)
            throw new BadRequestException(errorMessage);


        var mapper = appOrganisationSettingsMapperFactory.Create(setting.DataType);
        var requestValue = mapper.Map(request.Value);

        if (setting.IsKeyVault)
            return await SaveInKeyVault(request.OrganisationCodeName, request.SettingName, appSettings.KeyVault, requestValue, cancellationToken);

        return await SaveInDatabase(request.OrganisationCodeName, request.AppCodeName, request.SettingName, requestValue);
    }

    private async Task<CommandResponse> SaveInKeyVault(string organisationCodeName, string settingName, string keyVault,
        object requestValue, CancellationToken cancellationToken)
    {
        var sc = secretClientProvider.GetAppSecretClient(keyVault);
        await sc.SetSecretAsync($"{organisationCodeName}-{settingName}", requestValue.ToJson(), cancellationToken);

        return CommandResponse.Success();
    }

    private async Task<CommandResponse> SaveInDatabase(string organisationCodeName, string appCodeName, string settingName, object requestValue)
    {
        var appOrgSettings = await appOrganisationSettingsRepository.GetAppOrganisationSettingsAsync(organisationCodeName, appCodeName);

        if (appOrgSettings == null)
        {
            var entity = new AppOrganisationSettingsEntity
            {
                AppCodeName = appCodeName,
                OrganisationCodeName = organisationCodeName,
                Settings = new Dictionary<string, object>
                {
                    { settingName, requestValue }
                }
            };

            await appOrganisationSettingsRepository.AddItemAsync(entity);

            return CommandResponse.Created(nameof(AppOrganisationSettingsEntity), entity.Id);
        }

        if (appOrgSettings.Settings.TryGetValue(settingName, out _))
            appOrgSettings.Settings[settingName] = requestValue;
        else
            appOrgSettings.Settings.Add(settingName, requestValue);

        await appOrganisationSettingsRepository.UpdateItemAsync(appOrgSettings.Id, appOrgSettings);

        return CommandResponse.Success();
    }
}