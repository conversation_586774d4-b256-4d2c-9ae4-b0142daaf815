﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>5c6785fd-5bd8-41af-a4e4-d3e93c31a640</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <Company>Phlexglobal Ltd</Company>
    <DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
    <DockerfileContext>..\..</DockerfileContext>
    <DockerfileFile>.\Dockerfile.develop</DockerfileFile>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Services\Authentication\**" />
    <Content Remove="Services\Authentication\**" />
    <EmbeddedResource Remove="Services\Authentication\**" />
    <None Remove="Services\Authentication\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageReference Include="Autofac" Version="8.2.0" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Azure.Identity" Version="1.13.1" />
    <PackageReference Include="CommunityToolkit.Diagnostics" Version="8.4.0" />
    <PackageReference Include="Grpc.AspNetCore" Version="2.66.0" />
    <PackageReference Include="JetBrains.Annotations" Version="2024.3.0" />
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Cookies" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.OData" Version="9.1.0" />
    <PackageReference Include="NewRelic.Agent.Api" Version="10.34.0" />
    <PackageReference Include="NLog" Version="5.3.4" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.14" />
    <PackageReference Include="Phlex.Core.Api.Abstractions" Version="23.0.0.2" />
    <PackageReference Include="Phlex.Core.Messagebus" Version="23.0.0.78" />
    <PackageReference Include="Phlex.Core.Multitenancy" Version="23.0.0.135" />
    <PackageReference Include="Scrutor" Version="5.0.2" />
    <PackageReference Include="SkiaSharp" Version="2.88.9" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.88.9" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
    <PackageReference Include="Phlex.Core.FunctionalExtensions" Version="23.0.0.101" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.8.4" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.2" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.11" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.0.2" />
  </ItemGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>x64</PlatformTarget>
    <DefineConstants>TRACE</DefineConstants>
    <NoWarn>1701;1702;CA2254</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>x64</PlatformTarget>
    <NoWarn>1701;1702;CA2254</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\Axon.Core.Contracts\Axon.Core.Contracts.csproj" />
    <ProjectReference Include="..\Axon.Core.Domain\Axon.Core.Domain.csproj" />
    <ProjectReference Include="..\Axon.Core.Infrastructure\Axon.Core.Infrastructure.csproj" />
    <ProjectReference Include="..\Axon.Core.Shared\Axon.Core.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Commands\Admin\Organisation\" />
    <Folder Include="Exceptions\" />
  </ItemGroup>
  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="nlog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties appsettings_1json__JsonSchema="" />
    </VisualStudio>
  </ProjectExtensions>
</Project>