﻿using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.OrganisationAccess;
using Axon.Core.Api.Models.ScopeResource;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.ScopeResource
{
    internal sealed class GetScopeResourcesForRoleQueryHandler : IRequestHandler<GetScopeResourcesForRoleQuery, CommandResponse<IEnumerable<ScopeDataModel>>>
    {
        private readonly IScopeResourceProvider scopeResourceProvider;
        private readonly IRoleRepository roleRepository;
        private readonly IRoleDefinitionRepository roleDefinitionRepository;

        public GetScopeResourcesForRoleQueryHandler(IScopeResourceProvider scopeResourceProvider,
                                                    IRoleRepository roleRepository,
                                                    IRoleDefinitionRepository roleDefinitionRepository)
        {
            this.scopeResourceProvider = scopeResourceProvider;
            this.roleRepository = roleRepository;
            this.roleDefinitionRepository = roleDefinitionRepository;
        }

        public async Task<CommandResponse<IEnumerable<ScopeDataModel>>> Handle(GetScopeResourcesForRoleQuery request, CancellationToken cancellationToken)
        {
            var role = await roleRepository.GetItemAsync(request.RoleId);

            if (role == null)
            {
                return CommandResponse<IEnumerable<ScopeDataModel>>.NotFound("Role", $"{request.RoleId}");
            }

            var roleOverride = role.RoleType == RoleType.System ? await roleRepository.GetOverrideAsync(request.RoleId, request.OrgCodeName, request.AppCodeName) : null;

            var userGroupScopes = roleOverride?.UserGroupScopes ?? role.UserGroupScopes;

            if (userGroupScopes == null || !userGroupScopes.Any())
            {
                return new CommandResponse<IEnumerable<ScopeDataModel>>();
            }

            var definition = await roleDefinitionRepository.GetItemByAppCodeAsync(role.AppCodeName);

            if (definition == null)
            {
                return CommandResponse<IEnumerable<ScopeDataModel>>.NotFound("RoleDefinition", $"{role.AppCodeName}");
            }

            var scopeResources = await scopeResourceProvider.GetAvailableScopeResourcesByOrgAsync(request.AppCodeName, request.OrgCodeName);
            var resultList = scopeResources.Where(w => userGroupScopes.Select(s => s.Scope).Contains(w.Scope)).Select(sr => new ScopeDataModel(sr.Scope, sr.Resources.Select(r => new ScopeResourceDataModel(r.Id, r.Name))));

            return CommandResponse<IEnumerable<ScopeDataModel>>.Data(resultList);
        }
    }
}
