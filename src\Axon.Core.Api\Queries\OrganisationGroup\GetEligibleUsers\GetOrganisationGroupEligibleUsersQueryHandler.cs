﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Api.Queries.AppAccess.OrganisationUserQuery;
using Axon.Core.Api.Services.UserOrganisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Infrastructure.Extensions;
using Axon.Core.Shared.Api;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.OrganisationGroup.GetEligibleUsers;

internal class GetOrganisationGroupEligibleUsersQueryHandler : IRequestHandler<GetOrganisationGroupEligibleUsersQueryRequest, CommandResponse<ApiPagedListResult<OrganisationUserModel>>>
{
    private readonly IOrganisationRepository organisationRepository;
    private readonly IAccessRepository accessRepository;
    private readonly IUserRepository userRepository;
    private readonly IOrganisationUserGroupAccessProvider organisationUserGroupAccessProvider;
    private readonly IOrganisationUserProvider organisationUserProvider;

    public GetOrganisationGroupEligibleUsersQueryHandler(IOrganisationRepository organisationRepository, IAccessRepository accessRepository, IUserRepository userRepository, IOrganisationUserGroupAccessProvider organisationUserGroupAccessProvider, IOrganisationUserProvider organisationUserProvider)
    {
        this.organisationRepository = organisationRepository;
        this.accessRepository = accessRepository;
        this.userRepository = userRepository;
        this.organisationUserGroupAccessProvider = organisationUserGroupAccessProvider;
        this.organisationUserProvider = organisationUserProvider;
    }

    public async Task<CommandResponse<ApiPagedListResult<OrganisationUserModel>>> Handle(GetOrganisationGroupEligibleUsersQueryRequest request, CancellationToken cancellationToken)
    {
        var organisation = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);
        if (organisation == null)
            return CommandResponse<ApiPagedListResult<OrganisationUserModel>>.NotFound(nameof(OrganisationModel), request.OrganisationCodeName);

        var accessEntities = await accessRepository.GetAccessItemsForOrganisationAsync(organisation.Id, AccessType.OrganisationAccess);

        var filteredAccessEntities = accessEntities.AsQueryable().FilteredResult(request.ListParams.Filter);

        if (!request.ListParams.OrderBy.Any())
            request.ListParams.OrderBy = OrderByClauses.Parse($"{nameof(AccessEntity.User)}.{nameof(AccessEntity.User.Name)}");
        var pagedAccessEntities = filteredAccessEntities
            .OrderedResult(request.ListParams.OrderBy);

        var userEmails = pagedAccessEntities
            .Select(accessEntity => accessEntity.User.Email)
            .ToArray();
        var users = await userRepository.GetUsersAsync(userEmails);
        var embedOptions = request.Embed?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? [];

        var organisationUserModels = await GetOrganisationUserModelsNotInGroup(request.OrganisationGroupId, pagedAccessEntities, users, organisation, embedOptions);
        
        var pagedOrganisationUserModels = organisationUserModels.Skip(request.ListParams.Offset).Take(request.ListParams.Limit);

        return CommandResponse<ApiPagedListResult<OrganisationUserModel>>.Data(
            new ApiPagedListResult<OrganisationUserModel>(
                pagedOrganisationUserModels,
                request.ListParams.Offset,
                request.ListParams.Limit,
                filteredAccessEntities.Count()));
    }

    private async Task<List<OrganisationUserModel>> GetOrganisationUserModelsNotInGroup(string organisationGroupId, IQueryable<AccessEntity> pagedAccessEntities, IList<UserEntity> users, OrganisationEntity organisation, string[] embedOptions)
    {
        var organisationUserModels = new List<OrganisationUserModel>();
        var userOrganisations = new Dictionary<string, OrganisationEntity>();
        var includeGroups = embedOptions.Contains("groups", StringComparer.OrdinalIgnoreCase);
        var includeChildOrganisations = embedOptions.Contains("childOrganisations", StringComparer.OrdinalIgnoreCase);

        foreach (var accessEntity in pagedAccessEntities)
        {
            var (user, userOrganisation) = await organisationUserProvider.ProvideAsync(users, organisation, accessEntity, userOrganisations);

            var (groups, childOrganisations) = await organisationUserGroupAccessProvider.ProvideAsync(organisation.Id, accessEntity.User.Id, true, includeChildOrganisations);

            if (groups == null || groups.Length == 0 || !Array.Exists(groups, x => x.Id == organisationGroupId))
            {
                var item = new OrganisationUserModel
                {
                    Id = accessEntity.Id,
                    UserId = user?.Id ?? accessEntity.User.Id,
                    Email = user?.Email ?? accessEntity.User.Email,
                    Name = user?.Name ?? accessEntity.User.Name,
                    Status = (user?.Status ?? UserStatus.Inactive).ToString(),
                    LastAccessed = user?.LastAccessed ?? DateTime.MinValue,
                    OrganisationName = userOrganisation?.DisplayName,
                    OrganisationCodeName = userOrganisation?.CodeName,
                    CreatedAt = accessEntity.CreatedAt,
                    Groups = includeGroups ? groups : [],
                    ChildOrganisations = includeChildOrganisations ? childOrganisations : []
                };
                organisationUserModels.Add(item);
            }
        }

        return organisationUserModels;
    }
}
