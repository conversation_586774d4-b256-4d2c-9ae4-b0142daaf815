﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.EditOrganisationGroup;

[UsedImplicitly]
public class EditOrganisationGroupCommandRequestValidator : AbstractValidator<EditOrganisationGroupCommandRequest>
{
    public EditOrganisationGroupCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
        RuleFor(x => x.OrganisationGroupId)
            .MustBeAValidGuid()
            .NotEmpty();
    }
}