﻿using MediatR;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser.AddUserOrganisation;

public class AddOrganisationUserCommandRequest : IRequest<CommandResponse>
{
    public string OrganisationCodeName { get; }
    public AddOrganisationUserCommandRequestBody AddOrganisationUserCommandRequestBody { get; }

    public AddOrganisationUserCommandRequest(string organisationCodeName, AddOrganisationUserCommandRequestBody addOrganisationUserCommandRequest)
    {
        OrganisationCodeName = organisationCodeName;
        AddOrganisationUserCommandRequestBody = addOrganisationUserCommandRequest;
    }
}