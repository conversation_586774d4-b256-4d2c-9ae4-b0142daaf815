﻿using Axon.Core.Api.Attributes;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Queries.Audit;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Shared.Audit;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using System.Threading.Tasks;

namespace Axon.Core.Api.Controllers
{
    [Authorize]
    [Route("v{version:apiVersion}/odata/Organisation/{orgCodeName}")]
    [ApiExplorerSettings(IgnoreApi=true)]
    public class AuditsOdataController : ODataController
    {
        private readonly IMediator mediator;
        private readonly IValidator<GetOrganisationAuditsQueryRequest> getIdentityProviderAuditsQueryRequestValidator;

        public AuditsOdataController(IMediator mediator, IValidator<GetOrganisationAuditsQueryRequest> getIdentityProviderAuditsQueryRequestValidator)
        {
            this.mediator = mediator;
            this.getIdentityProviderAuditsQueryRequestValidator = getIdentityProviderAuditsQueryRequestValidator;
        }

        [HasOrganisationPermissions(nameof(CorePermissions.ViewAudit))]
        [HttpGet("Audits")]
        public async Task<IActionResult> Get([FromRoute] string orgCodeName, ODataQueryOptions<TenantAudit> odataOptions)
        {
            var request = new GetOrganisationAuditsQueryRequest(orgCodeName, odataOptions);
            var validationResult = await getIdentityProviderAuditsQueryRequestValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(validationResult.ToErrorDictionary());
            }

            var result = await mediator.Send(request);

            switch (result.status)
            {
                case System.Net.HttpStatusCode.OK:
                    return Ok(result.data);
                case System.Net.HttpStatusCode.NotFound:
                    return NotFound(result.data);
                case System.Net.HttpStatusCode.Forbidden:
                    return Unauthorized(result.errors);
                default:
                    return new StatusCodeResult((int)result.status);
            }
        }
    }
}