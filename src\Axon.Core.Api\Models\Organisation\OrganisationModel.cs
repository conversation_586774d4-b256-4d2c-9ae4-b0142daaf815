﻿using JetBrains.Annotations;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.Organisation
{
    //NOTE: CAL 23/06/2021 - After extensive discussion with TK I found that [Required] attributes are only used to avoid problems
    // with the generated typescript SDK and having to deal with "string | undefined is not assignable to string" errors.
    public class OrganisationModel
    {
        [Required]
        public string Id { get; set; }
        [Required]
        public string DisplayName { get; set; }
        [Required]
        public string Description { get; set; }
        [Required]
        public string Icon { get; set; }
        [Required]
        public string CodeName { get; set; }
        [Required]
        public bool IsDeleted { get; set; }
        [Required]
        public bool IsEnabled { get; set; }
        [Required]
        public string AccessLevel { get; set; }
        [Required]
        public string[] AllowedRoles { get; set; }
        [Required]
        public bool ShowAudits { get; set; }
        [Required]
        public bool CanManage { get; set; }

        [Required]
        public IEnumerable<AppConfigModel> Apps { get; set; }

        public ThemeConfigModel Theme { get; set; }

        [CanBeNull]
        public string ParentOrganisationId { get; set; }
    }
}
