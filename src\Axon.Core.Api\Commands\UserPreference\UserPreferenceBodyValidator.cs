﻿using System.Collections.Generic;
using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.UserPreference
{
    [UsedImplicitly]
    public class UserPreferenceBodyValidator : BaseCommandValidator<UserPreferenceBody>
    {
        public UserPreferenceBodyValidator()
        {
            RuleForEach(model => model.UserPreferences)
                .SetValidator(new UserPreferenceValidator());
        }

        public class UserPreferenceValidator : AbstractValidator<KeyValuePair<string, string>>
        {
            public UserPreferenceValidator()
            {
                RuleFor(model => model.Key)
                    .NotEmpty()
                    .MustBeAValidPreferencesKey();

                RuleFor(model => model.Value)
                    .NotNull()
                    .MustBeAValidPreferencesValue();
            }
        }
    }

}