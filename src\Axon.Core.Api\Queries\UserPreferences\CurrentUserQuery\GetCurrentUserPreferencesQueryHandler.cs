﻿using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.UserPreference;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using MediatR;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.UserPreferences.CurrentUserQuery
{
    internal class GetCurrentUserPreferencesQueryHandler : IRequestHandler<GetCurrentUserPreferencesQueryRequest, CommandResponse<UserPreferencesModel>>
    {
        private readonly IUserPreferenceRepository repo;
        private readonly IMapper mapper;
        private readonly ICurrentUserProvider currentUserProvider;

        public GetCurrentUserPreferencesQueryHandler(IUserPreferenceRepository repo, IMapper mapper, ICurrentUserProvider currentUserProvider)
        {
            Guard.IsNotNull(repo);
            this.repo = repo;
            Guard.IsNotNull(mapper);
            this.mapper = mapper;
            Guard.IsNotNull(currentUserProvider);
            this.currentUserProvider = currentUserProvider;
        }

        public async Task<CommandResponse<UserPreferencesModel>> Handle(GetCurrentUserPreferencesQueryRequest request, CancellationToken cancellationToken)
        {
            var userContext = currentUserProvider.GetUserContext();
            var preferences = await repo.GetItemAsync(userContext.ObjectId);
            return CommandResponse<UserPreferencesModel>.Data(mapper.Map<UserPreferencesModel>(preferences));
        }
    }
}
