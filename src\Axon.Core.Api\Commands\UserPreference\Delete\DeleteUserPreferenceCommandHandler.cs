﻿using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Exceptions;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.UserPreference.Delete;

internal class DeleteUserPreferenceCommandHandler : BaseCommandHandler<UserPreferenceEntity, DeleteUserPreferenceCommandRequest, CommandResponse>
{
    private readonly ICurrentUserProvider currentUserProvider;

    public DeleteUserPreferenceCommandHandler(IUserPreferenceRepository repo, IMapper mapper, IMessageBus messageBus, ICurrentUserProvider currentUserProvider)
        : base(repo, mapper, messageBus)
    {
        Guard.IsNotNull(currentUserProvider);
        this.currentUserProvider = currentUserProvider;
    }

    public override async Task<CommandResponse> Handle(DeleteUserPreferenceCommandRequest request, CancellationToken cancellationToken)
    {
        var repo = (IUserPreferenceRepository) Repo;
        var userContext = currentUserProvider.GetUserContext();
        var entity = await repo.GetItemAsync(userContext.ObjectId);

        if (!entity.UserPreferences.ContainsKey(request.Key))
            throw new EntityNotFoundException(nameof(UserPreferenceEntity), request.Key);

        entity.UserPreferences.Remove(request.Key);
        await repo.UpdateItemAsync(entity.Id, entity);
        var msg = new UserPreferenceDeleted(request.Key);

        await MessageBus.PublishAsync(msg, cancellationToken: CancellationToken.None);
        return CommandResponse.Success();
    }
}