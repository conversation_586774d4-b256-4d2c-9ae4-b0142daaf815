﻿using System;
using System.Threading.Tasks;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Interfaces.Persistence;

namespace Axon.Core.Domain.Extensions
{
    public static class AppRepositoryExtensions
    {
        public static async Task<string> GetAppIdByAppCode(this IAppRepository repository, string appCode)
        {
            string appId = null;
            if (!appCode.Equals(AppNameConstants.AxonCoreCodeName, StringComparison.OrdinalIgnoreCase))
            {
                var app = await repository.GetItemByAppCodeNameAsync(appCode);
                if (app == null)
                {
                    return null;
                }
                appId = app.Id;
            }
            else
            {
                appId = AppNameConstants.AxonCoreCodeName;
            }

            return appId;
        }
    }
}
