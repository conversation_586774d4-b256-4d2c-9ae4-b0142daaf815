﻿using System;
using Phlex.Core.Caching.Abstractions;
using Phlex.Core.Caching.Memory;

namespace Axon.Core.Domain.Caches
{
    public class SettingCache : ISettingCache
    {
        private readonly IMemoryCacheStore memoryCacheStore;

        /// <summary>
        /// Constructor for the setting cache class
        /// </summary>
        /// <param name="memoryCacheStore"></param>
        public SettingCache(IMemoryCacheStore memoryCacheStore)
        {
            this.memoryCacheStore = memoryCacheStore;
        }

        /// <summary>
        /// composite cache key item
        /// </summary>
        public struct SettingCacheKey : ICacheKey
        {
            public SettingCacheKey(string orgCodeName, string appCodeName, string settingName)
            {
                OrgCodeName = orgCodeName;
                AppCodeName = appCodeName;
                SettingName = settingName;
            }

            public string OrgCodeName { get; set; }
            public string AppCodeName { get; set; }
            public string SettingName { get; set; }
            public string Name => $"{OrgCodeName}_{AppCodeName}_{SettingName}";
        }

        /// <summary>
        /// method to get a item from the memory cache
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public string Get(SettingCacheKey key)
        {
            return memoryCacheStore.Get<SettingCacheKey, string>(key);
        }

        /// <summary>
        /// Method to add a item to the memory cache
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public string Add(SettingCacheKey key, string value)
        {
            return memoryCacheStore.Set(key, value,absoluteExpiration:TimeSpan.FromMinutes(5));
        }
    }
}
