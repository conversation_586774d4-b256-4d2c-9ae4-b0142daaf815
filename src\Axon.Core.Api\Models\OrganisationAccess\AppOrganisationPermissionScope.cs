﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.OrganisationAccess;

public class AppOrganisationPermissionScope
{
    public AppOrganisationPermissionScope(string scope, bool? permitted, IReadOnlyCollection<AppOrganisationPermissionScopeResource> resources = null)
    {
        Scope = scope;
        Permitted = permitted;
        Resources = resources;
    }

    [Required]
    public string Scope { get; }

    [Required]
    public bool? Permitted { get; }

    [Required]
    public IReadOnlyCollection<AppOrganisationPermissionScopeResource> Resources { get; }
}
