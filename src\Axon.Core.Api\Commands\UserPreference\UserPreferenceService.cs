﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.UserPreference;

public interface IUserPreferenceService
{
    Task<CommandResponse> CreatePreferences(string userId, UserPreferenceBody model, CancellationToken cancellationToken);
    Task<CommandResponse> UpdatePreferences(UserPreferenceEntity preferences, Dictionary<string, string> updatedPreferences, CancellationToken cancellationToken);
}

public class UserPreferenceService : IUserPreferenceService
{
    private readonly IMapper mapper;
    private readonly IMessageBus messageBus;
    private readonly IUserPreferenceRepository repo;

    public UserPreferenceService(IUserPreferenceRepository repo, IMapper mapper, IMessageBus messageBus)
    {
        Guard.IsNotNull(repo);
        this.repo = repo;
        Guard.IsNotNull(mapper);
        this.mapper = mapper;
        Guard.IsNotNull(messageBus);
        this.messageBus = messageBus;
    }

    public async Task<CommandResponse> CreatePreferences(string userId, UserPreferenceBody model, CancellationToken cancellationToken)
    {
        var pref = mapper.Map<UserPreferenceEntity>(model);
        pref.Id = userId;
        await repo.AddItemAsync(pref);
        var createdMsg = mapper.Map<UserPreferenceCreated>(pref);
        await messageBus.PublishAsync(createdMsg, cancellationToken: cancellationToken);
        return CommandResponse.Created(nameof(UserPreferenceEntity), userId);
    }

    public async Task<CommandResponse> UpdatePreferences(UserPreferenceEntity preferences, Dictionary<string, string> updatedPreferences, CancellationToken cancellationToken)
    {
        await repo.UpdateItemAsync(preferences.Id, preferences);
        var updatedMsg = new UserPreferenceUpdated(updatedPreferences);
        await messageBus.PublishAsync(updatedMsg, cancellationToken: cancellationToken);
        return CommandResponse.Created(nameof(UserPreferenceEntity), preferences.Id);
    }
}