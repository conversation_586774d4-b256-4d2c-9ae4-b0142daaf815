﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.EditOrganisationGroup;

[UsedImplicitly]
public class EditOrganisationGroupCommandRequestBodyValidator : AbstractValidator<EditOrganisationGroupCommandRequestBody>
{
    public EditOrganisationGroupCommandRequestBodyValidator()
    {
        RuleFor(x => x.Name)
            .MustBeAValidGroupName()
            .NotEmpty();
    }
}