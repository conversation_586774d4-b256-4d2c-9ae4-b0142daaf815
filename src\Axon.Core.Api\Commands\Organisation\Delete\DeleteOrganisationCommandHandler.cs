using AutoMapper;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.Organisation.Delete;

[UsedImplicitly]
internal class DeleteOrganisationCommandHandler : DeleteCommandHandler<OrganisationEntity, OrganisationModel, OrganisationDeleted>
{
    public DeleteOrganisationCommandHandler(IOrganisationRepository repo, IMapper mapper, IMessageBus messageBus) : base(repo, mapper, messageBus)
    {
    }
}