﻿using System.Collections.Generic;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.OrganisationAccess;
using MediatR;

namespace Axon.Core.Api.Queries.OrganisationAccess.OrgUserPermissionQuery
{
    public class GetAppOrgUserPermissionsQueryRequest : IRequest<CommandResponse<IEnumerable<AppOrganisationPermission>>>
    {
        public GetAppOrgUserPermissionsQueryRequest(string orgCodeName, string appCodeName, string userEmail)
        {
            OrganisationCodeName = orgCodeName;
            AppCodeName = appCodeName;
            UserEmail = userEmail;
        }

        public string OrganisationCodeName { get; }
        public string AppCodeName { get; }
        public string UserEmail { get; }
    }
}