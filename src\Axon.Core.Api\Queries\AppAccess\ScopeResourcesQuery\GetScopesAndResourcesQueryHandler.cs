﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.OrganisationAccess;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Services;
using CommunityToolkit.Diagnostics;
using MediatR;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Queries.AppAccess.ScopeResourcesQuery
{
    internal sealed class GetScopesAndResourcesQueryHandler : IRequestHandler<GetScopesAndResourcesQueryRequest, CommandResponse<IEnumerable<ScopeDataModel>>>
    {
        private readonly IScopeResourceProvider scopeResourceProvider;
        private readonly IRoleDefinitionRepository roleDefinitionRepository;

        public GetScopesAndResourcesQueryHandler(IScopeResourceProvider scopeResourceProvider, IRoleDefinitionRepository roleDefinitionRepository)
        {
            Guard.IsNotNull(scopeResourceProvider);
            this.scopeResourceProvider = scopeResourceProvider;
            Guard.IsNotNull(roleDefinitionRepository);
            this.roleDefinitionRepository = roleDefinitionRepository;
        }

        public async Task<CommandResponse<IEnumerable<ScopeDataModel>>> Handle(GetScopesAndResourcesQueryRequest request, CancellationToken cancellationToken)
        {
            var definition = await roleDefinitionRepository.GetItemByAppCodeAsync(request.AppCodeName);

            if (definition == null)
            {
                return CommandResponse<IEnumerable<ScopeDataModel>>.NotFound($"{typeof(RoleDefinitionEntity)}", request.AppCodeName);
            }

            if(!definition.Permissions.Exists(x=> x.HasScopes))
            {
                return CommandResponse<IEnumerable<ScopeDataModel>>.Data(Enumerable.Empty<ScopeDataModel>());
            }

            var scopeResource = await scopeResourceProvider.GetAvailableScopeResourcesByOrgAsync(request.AppCodeName,
                                                                                                 request.OrgCodeName);

            var resultList = scopeResource.Select(s => new ScopeDataModel(s.Scope,
                                                                          s.Resources.Select(sr => new ScopeResourceDataModel(sr.Id,
                                                                                                                              sr.Name))));

            return CommandResponse<IEnumerable<ScopeDataModel>>.Data(resultList);
        }
    }
}
