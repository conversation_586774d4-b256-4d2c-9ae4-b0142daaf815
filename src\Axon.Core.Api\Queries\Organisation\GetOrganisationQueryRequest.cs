﻿using System.Collections.Generic;
using System.Security.Claims;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Filters;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Shared.Api;
using MediatR;

namespace Axon.Core.Api.Queries.Organisation
{

    public class GetOrganisationQueryRequest : IRequest<CommandResponse<IEnumerable<OrganisationModel>>>
    {
        public ListParams ListParams { get; }

        public GetOrganisationQueryRequest(ListParams listParams)
        {
            ListParams = listParams;
        }
    }
}
