/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// ValidateAppQueryRequest
    /// </summary>
    [DataContract(Name = "ValidateAppQueryRequest")]
    public partial class ValidateAppQueryRequest : IEquatable<ValidateAppQueryRequest>, IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ValidateAppQueryRequest" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected ValidateAppQueryRequest() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="ValidateAppQueryRequest" /> class.
        /// </summary>
        /// <param name="name">name (required).</param>
        /// <param name="codeName">codeName (required).</param>
        /// <param name="id">id.</param>
        public ValidateAppQueryRequest(string name = default(string), string codeName = default(string), string id = default(string))
        {
            // to ensure "name" is required (not null)
            this.Name = name ?? throw new ArgumentNullException("name is a required property for ValidateAppQueryRequest and cannot be null");
            // to ensure "codeName" is required (not null)
            this.CodeName = codeName ?? throw new ArgumentNullException("codeName is a required property for ValidateAppQueryRequest and cannot be null");
            this.Id = id;
        }

        /// <summary>
        /// Gets or Sets Name
        /// </summary>
        [DataMember(Name = "name", IsRequired = true, EmitDefaultValue = false)]
        public string Name { get; set; }

        /// <summary>
        /// Gets or Sets CodeName
        /// </summary>
        [DataMember(Name = "codeName", IsRequired = true, EmitDefaultValue = false)]
        public string CodeName { get; set; }

        /// <summary>
        /// Gets or Sets Id
        /// </summary>
        [DataMember(Name = "id", EmitDefaultValue = true)]
        public string Id { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append("class ValidateAppQueryRequest {\n");
            sb.Append("  Name: ").Append(Name).Append("\n");
            sb.Append("  CodeName: ").Append(CodeName).Append("\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="input">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object input)
        {
            return this.Equals(input as ValidateAppQueryRequest);
        }

        /// <summary>
        /// Returns true if ValidateAppQueryRequest instances are equal
        /// </summary>
        /// <param name="input">Instance of ValidateAppQueryRequest to be compared</param>
        /// <returns>Boolean</returns>
        public bool Equals(ValidateAppQueryRequest input)
        {
            if (input == null)
                return false;

            return 
                (
                    this.Name == input.Name ||
                    (this.Name != null &&
                    this.Name.Equals(input.Name))
                ) && 
                (
                    this.CodeName == input.CodeName ||
                    (this.CodeName != null &&
                    this.CodeName.Equals(input.CodeName))
                ) && 
                (
                    this.Id == input.Id ||
                    (this.Id != null &&
                    this.Id.Equals(input.Id))
                );
        }

        /// <summary>
        /// Gets the hash code
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                int hashCode = 41;
                if (this.Name != null)
                    hashCode = hashCode * 59 + this.Name.GetHashCode();
                if (this.CodeName != null)
                    hashCode = hashCode * 59 + this.CodeName.GetHashCode();
                if (this.Id != null)
                    hashCode = hashCode * 59 + this.Id.GetHashCode();
                return hashCode;
            }
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
