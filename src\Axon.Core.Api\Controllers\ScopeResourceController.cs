﻿using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.OrganisationAccess;
using Axon.Core.Api.Queries.ScopeResource;
using Axon.Core.Api.Services.Authorisation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Api.Controllers
{
    [ApiController]
    [Produces("application/json", "application/xml")]
    [ProducesResponseType(401, Type = typeof(CommandResponse))]
    [ProducesResponseType(403, Type = typeof(CommandResponse))]
    [ProducesResponseType(500, Type = typeof(CommandResponse))]
    [Route("v{version:apiVersion}/ScopeResource")]
    [Authorize]
    public class ScopeResourceController : ApiControllerBase
    {
        
        public ScopeResourceController(IMediator mediator) : base(mediator)
        {
                
        }

        [HttpGet("organisation/{orgCodeName}/app/{appCodeName}/role/{roleId}", Name = "GetScopeResourcesForRole")]
        [HasOrganisationPermissions(nameof(CorePermissions.EditOrganisation))]
        [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<ScopeDataModel>>))]
        [ProducesResponseType(404, Type = typeof(CommandResponse))]
        [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
        public async Task<IActionResult> GetRoleScopes(string roleId, string orgCodeName, string appCodeName)
        {
            return await Send(new GetScopeResourcesForRoleQuery(roleId, appCodeName, orgCodeName));
        }
    }
}
