﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppSetting;
using MediatR;

namespace Axon.Core.Api.Queries.AppOrganisationSettings
{
    public class GetOrganisationAppSettingsQueryRequest : IRequest<CommandResponse<SettingsValueModel[]>>
    {
        public string OrgCodeName { get; }
        public string AppCodeName { get; }
        public GetOrganisationAppSettingsQueryRequest(string orgCodeName, string appCodeName)
        {
            OrgCodeName = orgCodeName;
            AppCodeName = appCodeName;
        }
    }
}
