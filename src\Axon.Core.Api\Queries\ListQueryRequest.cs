﻿using System.Collections.Generic;
using Axon.Core.Api.Commands;
using Axon.Core.Shared.Api;
using MediatR;

namespace Axon.Core.Api.Queries
{
    public class ListQueryRequest<TModel> : IRequest<CommandResponse<IEnumerable<TModel>>>
    {
        public ListParams ListParams { get; }

        public ListQueryRequest(ListParams listParams)
        {
            this.ListParams = listParams;
        }
    }
}