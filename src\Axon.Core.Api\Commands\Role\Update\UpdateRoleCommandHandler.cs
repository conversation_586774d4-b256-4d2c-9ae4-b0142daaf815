﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Models.Role;
using Axon.Core.Api.Validators.Role;
using Axon.Core.Contracts;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Extensions;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.Role.Update
{
    internal class UpdateRoleCommandHandler : BaseUpdateCommandHandler<UpdateRoleBody, RoleEntity, AppRoleUpdatedEvent>
    {
        private readonly IAppGroupRepository appGroupRepository;
        private readonly IRoleBodyValidator rolePermissionBodyValidator;
        private readonly IClientDetailsProvider clientDetailsProvider;
        private readonly ICorrelationIdProvider correlationIdProvider;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAppRepository appRepository;
        private readonly IAuditService<TenantAuditExtensions> auditService;

        private const string userGroupScopesValidationErrorMessage = "UserGroupScopes can not be removed / changed once it has been set";

        public UpdateRoleCommandHandler(
            IAppGroupRepository appGroupRepository,
            IRoleBodyValidator rolePermissionBodyValidator,
            IRoleRepository repo,
            IMapper mapper,
            IMessageBus messageBus,
            IClientDetailsProvider clientDetailsProvider,
            ICorrelationIdProvider correlationIdProvider,
            IOrganisationRepository organisationRepository,
            IAppRepository appRepository,
            IAuditService<TenantAuditExtensions> auditService)
            : base(repo, mapper, messageBus)
        {
            this.appGroupRepository = appGroupRepository;
            this.rolePermissionBodyValidator = rolePermissionBodyValidator;
            this.clientDetailsProvider = clientDetailsProvider;
            this.correlationIdProvider = correlationIdProvider;
            this.organisationRepository = organisationRepository;
            this.appRepository = appRepository;
            this.auditService = auditService;
        }

        public override async Task<CommandResponse> Handle(UpdateCommandRequest<UpdateRoleBody> request, CancellationToken cancellationToken)
        {
            var roleRepository = (IRoleRepository)Repo;

            var existingEntity = await roleRepository.GetItemAsync(request.Id);
            if (existingEntity == null)
            {
                return CommandResponse.NotFound(nameof(RoleEntity), request.Id);
            }

            //The permission check was performed based on the org/app code in the url. If the document id does not match that then we can't be sure
            //the user has permission to affect this document.
            if ((existingEntity.OrganisationCodeName != null &&
                !existingEntity.OrganisationCodeName.Equals(request.Model.OrganisationCodeName, System.StringComparison.CurrentCultureIgnoreCase)) ||
                !existingEntity.AppCodeName.Equals(request.Model.AppCodeName, System.StringComparison.CurrentCultureIgnoreCase))
            {
                return CommandResponse.Forbidden(nameof(RoleEntity), request.Id);
            }

            var rolePermissionsValidationResult = await rolePermissionBodyValidator.IsValid(request.Model.OrganisationCodeName, request.Model.AppCodeName, request.Model.RoleBody);
            if (!rolePermissionsValidationResult.isValid)
            {
                return CommandResponse.Failed(nameof(request.Model.RoleBody.Permissions), rolePermissionsValidationResult.errorMessage);
            }

            //For system roles, an update is an update of the matching override
            //if one does not exist then it needs to be created. 
            if (existingEntity.RoleType == Domain.Enums.RoleType.System && existingEntity.InheritRoleId == null)
            {
                var existingOverride = await roleRepository.GetOverrideAsync(existingEntity.Id, request.Model.OrganisationCodeName, request.Model.AppCodeName);

                if (existingOverride != null)
                {
                    if (!ValidateUserGroupScopesUpdate(existingOverride, request.Model))
                    {
                        return CommandResponse.BadRequest(nameof(RoleEntity), userGroupScopesValidationErrorMessage);
                    }

                    return await UpsertRole(existingOverride, request.Model);
                }

                var newOverride = new RoleEntity()
                {
                    AppCodeName = request.Model.AppCodeName,
                    OrganisationCodeName = request.Model.OrganisationCodeName,
                    RoleType = Domain.Enums.RoleType.System,
                    InheritRoleId = existingEntity.Id,
                    IsEnabled = true,
                    Id = $"{Guid.NewGuid()}"
                };

                return await UpsertRole(newOverride, request.Model, true);
            }
            else if(existingEntity.RoleType == Domain.Enums.RoleType.Custom)
            {
                var customValidateResult = await ValidateCustomRole(roleRepository, existingEntity, request);

                if (!customValidateResult.valid)
                {
                    return customValidateResult.errorResponse;
                }
            }

            return await UpsertRole(existingEntity, request.Model);
        }

        private async Task<CommandResponse> UpsertRole(RoleEntity roleToUpsert, UpdateRoleBody updateRoleBody, bool isCreate = false)
        {
            var correlationId = correlationIdProvider.Provide();
            var clientDetails = clientDetailsProvider.Provide();

            var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Role, 
                                                                  isCreate ? AuditEventDescriptions.RoleCreated :
                                                                             AuditEventDescriptions.RoleUpdated,
                                                                  correlationId,
                                                                  clientDetails,
                                                                  updateRoleBody.OrganisationCodeName);

            await auditService.LogAsync(isCreate? AuditEventTypes.RoleCreated : AuditEventTypes.RoleUpdated, tenantAuditExtensions, roleToUpsert,
                async () =>
                {
                    //Name is only relevant for non system roles
                    if (roleToUpsert.RoleType == Domain.Enums.RoleType.Custom)
                    {
                        roleToUpsert.RoleName = updateRoleBody.RoleBody.RoleName;
                    }
                    roleToUpsert.IsEnabled = updateRoleBody.RoleBody.IsEnabled;
                    roleToUpsert.Permissions = Mapper.Map<RoleEntity.Permission[]>(updateRoleBody.RoleBody.Permissions);
                    roleToUpsert.UserGroupScopes = Mapper.Map<RoleEntity.UserGroupScope[]>(updateRoleBody.RoleBody.UserGroupScopes);
                    await Repo.UpdateItemAsync(roleToUpsert.Id, roleToUpsert);
                });

            var mappedEvent = Mapper.Map<AppRoleUpdatedEvent>(roleToUpsert);
            var org = await organisationRepository.GetItemByCodeNameAsync(updateRoleBody.OrganisationCodeName);
            if (org == default(OrganisationEntity))
            {
                return CommandResponse.Failed(nameof(updateRoleBody.OrganisationCodeName), $"Organisation `{updateRoleBody.OrganisationCodeName}` does not exist");
            }

            var appId = await appRepository.GetAppIdByAppCode(updateRoleBody.AppCodeName);
            if (appId == null)
            {
                return CommandResponse.NotFound(nameof(AppEntity), updateRoleBody.AppCodeName);
            }

            mappedEvent.OrgId = org.Id;
            mappedEvent.AppId = appId;
            mappedEvent.Action = EventActionType.Updated;
            await MessageBus.PublishAsync(mappedEvent);

            if (roleToUpsert.RoleType == Domain.Enums.RoleType.Custom)
            {
                await UpdateAppGroupRoleNames(roleToUpsert, updateRoleBody, correlationId, clientDetails);
            }

            return isCreate? CommandResponse.Created(nameof(RoleEntity), roleToUpsert.Id) : CommandResponse.Success();
        }

        private async Task UpdateAppGroupRoleNames(RoleEntity roleEntity, UpdateRoleBody updateRoleBody, Guid correlationId, ClientDetails clientDetails)
        {
            var appGroupsByRoleId = await appGroupRepository.GetAppGroupsByRoleIdAsync(roleEntity.OrganisationCodeName, roleEntity.AppCodeName, roleEntity.Id);

            if (appGroupsByRoleId.Any())
            {
                foreach (var appGroup in appGroupsByRoleId)
                {
                    appGroup.Role = updateRoleBody.RoleBody.RoleName;
                    appGroup.LastUpdatedDate = DateTime.UtcNow;
                }

                var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.AppGroup, AuditEventDescriptions.AppGroupUpdated, correlationId, clientDetails, roleEntity.OrganisationCodeName);

                await auditService.LogAsync(AuditEventTypes.AppGroupUpdated, tenantAuditExtensions, appGroupsByRoleId,
                    async () =>
                    {
                        await appGroupRepository.BulkUpdateItemsAsync(appGroupsByRoleId.ToList());
                    });
            }
        }

        private static async Task<(bool valid, CommandResponse errorResponse)> ValidateCustomRole(IRoleRepository roleRepository, RoleEntity existingEntity, UpdateCommandRequest<UpdateRoleBody> request)
        {
            var newNameInUse = await roleRepository.RoleNameExistsAsync(request.Model.OrganisationCodeName, request.Model.AppCodeName, request.Model.RoleBody.RoleName, request.Id);
            if (newNameInUse)
            {
                return (false, CommandResponse.Failed(nameof(request.Model.RoleBody.RoleName), $"Role `{request.Model.RoleBody.RoleName}` already exists"));
            }

            if (!ValidateUserGroupScopesUpdate(existingEntity, request.Model))
            {
                return (false, CommandResponse.BadRequest(nameof(RoleEntity), userGroupScopesValidationErrorMessage));
            }

            return (true, null);
        }

        private static bool ValidateUserGroupScopesUpdate(RoleEntity existingEntity, UpdateRoleBody updateRoleBody)
        {
            if (existingEntity.UserGroupScopes?.Any() == true)
            {
                if (updateRoleBody.RoleBody?.UserGroupScopes.Any() == false)
                {
                    //Can't remove user group scopes
                    return false;
                }
                var newScopes = updateRoleBody.RoleBody.UserGroupScopes.Select(x => x.Scope).ToList();
                return newScopes.Count == existingEntity.UserGroupScopes.Count() &&
                       existingEntity.UserGroupScopes.Select(es => es.Scope).All(es => newScopes.Contains(es));
            }

            return true;
        }
    }
}
