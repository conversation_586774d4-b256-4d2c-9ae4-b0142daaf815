﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Access;
using Microsoft.Extensions.Logging;

namespace Axon.Core.Api.Services.Migration
{
    public class UserOrganisationAccessMigrationCleanupService : IUserOrganisationAccessMigrationCleanupService
    {

        private readonly IUserRequestContext userRequestContext;
        private readonly IAccessService accessService;
        private readonly IUserRepository userRepository;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAccessRepository accessRepository;
        private readonly ILogger<UserOrganisationAccessMigrationCleanupService> logger;

        public UserOrganisationAccessMigrationCleanupService(ILogger<UserOrganisationAccessMigrationCleanupService> logger,IAccessRepository accessRepository,
            IUserRequestContext userRequestContext, IAccessService accessService, IUserRepository userRepository, IOrganisationRepository organisationRepository)
        {
            this.userRequestContext = userRequestContext;
            this.accessService = accessService;
            this.userRepository = userRepository;
            this.logger = logger;
            this.organisationRepository = organisationRepository;
            this.accessRepository = accessRepository;
        }

        public async Task<bool> Migrate()
        {
            var user = await userRepository.GetByIdentityProviderObjectIdAsync(userRequestContext.GetClaimsData().UserOid);
            if (user == null)
            {
                logger.Log(LogLevel.Error, "UserOrganisationAccessMigrationCleanupService unable to continue, unable to retrieve user by useroid, UserOid:{UserOid}", userRequestContext.GetClaimsData().UserOid);
                return false;
            }

            var roles = userRequestContext.GetUserRoles();
            if (!roles.Any())
            {
                logger.Log(LogLevel.Error, "UserOrganisationAccessMigrationCleanupService unable to continue, no roles found for user, User Id:{UserId}, User Email:{email}", user.Id, user.Email);
                return false;
            }

            //does our users already have user access items?
            var orgAccessItems = await accessService.GetOrganisationAccessForUser(user.Id);
            // if none return
            if (!orgAccessItems.Any())
            {
                //user doesn't have and org access
                logger.Log(LogLevel.Debug, "UserOrganisationAccessMigrationCleanupService not continuing, no organisation access items found for user, User Id:{UserId}, User Email:{email}", user.Id, user.Email);
                return false;
            }

            //get organisation items for the access items we have
            IList<OrganisationEntity> organisations = await organisationRepository.GetAllItemsInCodenameSetAsync(orgAccessItems.Select(item => item.OrganisationCodeName).ToList());


            //do we have any organisation access items for organisations we don't have an allowed role for?
            List<OrganisationEntity> unAuthorisedOrganisationEntity = organisations.Where(item => !item.AllowedRoles.Intersect(roles).Any()).ToList();

            if (!unAuthorisedOrganisationEntity.Any())
            {
                logger.Log(LogLevel.Debug, "UserOrganisationAccessMigrationCleanupService complete, user has correct roles for all orgs, User Id:{UserId}, User Email:{email}, Orgs:{organisations}", user.Id, user.Email, string.Join(',',organisations.Select(item => item.CodeName)));
                return true;
            }

            //get access items ready to remove
            List<UserOrganisationAccess> accessToRemove = orgAccessItems.Where(accessItem => unAuthorisedOrganisationEntity.Select(item => item.Id).Contains(accessItem.OrganisationId)).ToList();

            logger.Log(LogLevel.Debug, "UserOrganisationAccessMigrationCleanupService, user missing roles for some organisations, will remove the following, User Id:{UserId}, User Email:{email}, Orgs:{organisations}", user.Id, user.Email, string.Join(',', accessToRemove.Select(item => item.OrganisationCodeName)));
            
            foreach (var access in accessToRemove)
            {
                await accessRepository.DeleteItemAsync(access.Id);
            }

            logger.Log(LogLevel.Debug, "UserOrganisationAccessMigrationCleanupService, user missing roles for some organisations, organisations removal complete, User Id:{UserId}, User Email:{email}, Orgs:{organisations}", user.Id, user.Email, string.Join(',', accessToRemove.Select(item => item.OrganisationCodeName)));

            logger.Log(LogLevel.Debug, "UserOrganisationAccessMigrationCleanupService complete, user has org access cleanup correctly, User Id:{UserId}, User Email:{email}", user.Id, user.Email);
            return true;
        }
    }
}
