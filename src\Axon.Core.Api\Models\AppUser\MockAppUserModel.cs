﻿using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace Axon.Core.Api.Models.AppUser
{
    //TODO remove class and usages on completion of all endpoints that rely on this
    [ExcludeFromCodeCoverage(Justification = "Just for mock data to allow frontend integration")]
    internal static class MockAppUserModel
    {
        public static IEnumerable<AppUserModel> Generate(string appCodeName, string orgCodeName)
        {
            if (appCodeName == "trialmatch" && orgCodeName == "phlexglobal")
            {
                return [
                    new AppUserModel
                {
                    AppCodeName = appCodeName,
                    OrganisationCodeName = orgCodeName,
                    UserId = "ef69ee35-499d-4b3b-b018-24b479825044",
                    UserName = "<PERSON>",
                    Email = "<EMAIL>",
                    Roles = [
                            new AppUserRoleModel
                            {
                                Id = "524aad2d-f879-47d6-af57-c31f594f108c",
                                RoleName = "Contributor"
                            }
                        ],
                    Groups = [
                        new AppUserGroupModel
                        {
                            Id = "16f9f613-ba30-492a-a1fe-017de519400e",
                            GroupName = "Mock Group 1"
                        }
                        ]
                },
                new AppUserModel
                {
                    AppCodeName = appCodeName,
                    OrganisationCodeName = orgCodeName,
                    UserId = "77099bd3-89f9-4989-a11e-5ad479ef6e9d",
                    UserName = "Smith Johnson",
                    Email = "<EMAIL>",
                    Roles = [
                            new AppUserRoleModel
                            {
                                Id = "cec61c5d-9db5-46ba-89f9-44afeccbd999",
                                RoleName = "Contributor"
                            },
                            new AppUserRoleModel
                            {
                                Id = "7b7e2b87-4a6c-47e4-a53d-46ff05dfdf93",
                                RoleName = "App.Administrator"
                            }
                        ],
                    Groups = [
                           new AppUserGroupModel
                            {
                                Id = "16f9f613-ba30-492a-a1fe-017de519400e",
                                GroupName = "Mock Group 1"
                            }
                        ]
                },
                new AppUserModel
                {
                    AppCodeName = appCodeName,
                    OrganisationCodeName = orgCodeName,
                    UserId = "27658227-83dd-4009-a224-d9ac795a1590",
                    UserName = "Jonathan Smithson",
                    Email = "<EMAIL>",
                    Roles = [
                            new AppUserRoleModel
                            {
                                Id = "cec61c5d-9db5-46ba-89f9-44afeccbd999",
                                RoleName = "Contributor"
                            }
                        ],
                    Groups = [
                        new AppUserGroupModel
                        {
                            Id = "16f9f613-ba30-492a-a1fe-017de519400e",
                            GroupName = "Mock Group 1"
                        },
                        new AppUserGroupModel
                        {
                            Id = "ec2643da-9b67-4249-9c47-6a214d9e1ff4",
                            GroupName = "Mock Group 2"
                        }
                        ]
                }
                    ];
            }

            if (appCodeName == "trialmatch" && orgCodeName == "cencora")
            {
                return [
                    new AppUserModel
                {
                    AppCodeName = appCodeName,
                    OrganisationCodeName = orgCodeName,
                    UserId = "d72456c4-c556-4724-9114-1b58d320097f",
                    UserName = "Jane Smith",
                    Email = "<EMAIL>",
                    Roles = [
                            new AppUserRoleModel
                            {
                                Id = "cec61c5d-9db5-46ba-89f9-44afeccbd999",
                                RoleName = "Contributor"
                            },
                        ],
                    Groups = [
                        new AppUserGroupModel
                        {
                            Id = "4cd44366-93ad-4e66-a1b7-cd4b78b214bb",
                            GroupName = "Mock Group 2"
                        }
                        ]
                },
                new AppUserModel
                {
                    AppCodeName = appCodeName,
                    OrganisationCodeName = orgCodeName,
                    UserId = "f090dc86-fc9b-4992-9c0b-e92e441b9fc6",
                    UserName = "Geoff Nobody",
                    Email = "<EMAIL>",
                    Roles = [
                            new AppUserRoleModel
                            {
                                Id = "f7317445-a320-41c4-9fdc-4110e813966e",
                                RoleName = "Reader"
                            },
                            new AppUserRoleModel
                            {
                                Id = "a4638dd0-413c-4c1f-ba14-0b2263578f5c",
                                RoleName = "Custom Role 1"
                            }
                        ],
                    Groups = [
                            new AppUserGroupModel
                            {
                                Id = "c8e9e73c-7f5f-443c-ac84-cde91417ede1",
                                GroupName = "Mock Group 1"
                            }
                        ]
                },
                new AppUserModel
                {
                    AppCodeName = appCodeName,
                    OrganisationCodeName = orgCodeName,
                    UserId = "9fabcd44-be6f-40d9-aa3f-2ac9770ec40a",
                    UserName = "Jane Doe",
                    Email = "<EMAIL>",
                    Roles = [
                            new AppUserRoleModel
                            {
                                Id = "f7317445-a320-41c4-9fdc-4110e813966e",
                                RoleName = "Reader"
                            }
                        ],
                    Groups = [
                        ]
                }
                    ];
            }

            return [];
        }
    }
}
