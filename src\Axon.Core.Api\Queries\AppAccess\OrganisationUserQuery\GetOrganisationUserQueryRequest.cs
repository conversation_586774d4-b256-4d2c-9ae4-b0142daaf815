﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using MediatR;

namespace Axon.Core.Api.Queries.AppAccess.OrganisationUserQuery;

public class GetOrganisationUserQueryRequest : IRequest<CommandResponse<OrganisationUserModel>>
{
    public string OrgCodeName { get; }
    public string UserId { get; }
    public string Embed { get; }
    public bool TakeUserFromChildOrganisations { get; }

    public GetOrganisationUserQueryRequest(string orgCodeName, string userId, bool takeUserFromChildOrganisations, string embed)
    {
        OrgCodeName = orgCodeName;
        UserId = userId;
        TakeUserFromChildOrganisations = takeUserFromChildOrganisations;
        Embed = embed;
    }
}