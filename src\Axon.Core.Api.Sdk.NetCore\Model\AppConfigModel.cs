/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// AppConfigModel
    /// </summary>
    [DataContract(Name = "AppConfigModel")]
    public partial class AppConfigModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="AppConfigModel" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected AppConfigModel() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="AppConfigModel" /> class.
        /// </summary>
        /// <param name="appId">appId (required).</param>
        /// <param name="displayName">displayName (required).</param>
        /// <param name="appCodeName">appCodeName (required).</param>
        /// <param name="enabled">enabled (required).</param>
        /// <param name="deleted">deleted (required).</param>
        /// <param name="appSetting">appSetting (required).</param>
        /// <param name="status">status (required).</param>
        /// <param name="hasAccess">hasAccess (required).</param>
        public AppConfigModel(string appId = default(string), string displayName = default(string), string appCodeName = default(string), bool enabled = default(bool), bool deleted = default(bool), string appSetting = default(string), string status = default(string), bool hasAccess = default(bool))
        {
            // to ensure "appId" is required (not null)
            if (appId == null)
            {
                throw new ArgumentNullException("appId is a required property for AppConfigModel and cannot be null");
            }
            this.AppId = appId;
            // to ensure "displayName" is required (not null)
            if (displayName == null)
            {
                throw new ArgumentNullException("displayName is a required property for AppConfigModel and cannot be null");
            }
            this.DisplayName = displayName;
            // to ensure "appCodeName" is required (not null)
            if (appCodeName == null)
            {
                throw new ArgumentNullException("appCodeName is a required property for AppConfigModel and cannot be null");
            }
            this.AppCodeName = appCodeName;
            this.Enabled = enabled;
            this.Deleted = deleted;
            // to ensure "appSetting" is required (not null)
            if (appSetting == null)
            {
                throw new ArgumentNullException("appSetting is a required property for AppConfigModel and cannot be null");
            }
            this.AppSetting = appSetting;
            // to ensure "status" is required (not null)
            if (status == null)
            {
                throw new ArgumentNullException("status is a required property for AppConfigModel and cannot be null");
            }
            this.Status = status;
            this.HasAccess = hasAccess;
        }

        /// <summary>
        /// Gets or Sets AppId
        /// </summary>
        [DataMember(Name = "appId", IsRequired = true, EmitDefaultValue = true)]
        public string AppId { get; set; }

        /// <summary>
        /// Gets or Sets DisplayName
        /// </summary>
        [DataMember(Name = "displayName", IsRequired = true, EmitDefaultValue = true)]
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or Sets AppCodeName
        /// </summary>
        [DataMember(Name = "appCodeName", IsRequired = true, EmitDefaultValue = true)]
        public string AppCodeName { get; set; }

        /// <summary>
        /// Gets or Sets Enabled
        /// </summary>
        [DataMember(Name = "enabled", IsRequired = true, EmitDefaultValue = true)]
        public bool Enabled { get; set; }

        /// <summary>
        /// Gets or Sets Deleted
        /// </summary>
        [DataMember(Name = "deleted", IsRequired = true, EmitDefaultValue = true)]
        public bool Deleted { get; set; }

        /// <summary>
        /// Gets or Sets AppSetting
        /// </summary>
        [DataMember(Name = "appSetting", IsRequired = true, EmitDefaultValue = true)]
        public string AppSetting { get; set; }

        /// <summary>
        /// Gets or Sets Status
        /// </summary>
        [DataMember(Name = "status", IsRequired = true, EmitDefaultValue = true)]
        public string Status { get; set; }

        /// <summary>
        /// Gets or Sets HasAccess
        /// </summary>
        [DataMember(Name = "hasAccess", IsRequired = true, EmitDefaultValue = true)]
        public bool HasAccess { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class AppConfigModel {\n");
            sb.Append("  AppId: ").Append(AppId).Append("\n");
            sb.Append("  DisplayName: ").Append(DisplayName).Append("\n");
            sb.Append("  AppCodeName: ").Append(AppCodeName).Append("\n");
            sb.Append("  Enabled: ").Append(Enabled).Append("\n");
            sb.Append("  Deleted: ").Append(Deleted).Append("\n");
            sb.Append("  AppSetting: ").Append(AppSetting).Append("\n");
            sb.Append("  Status: ").Append(Status).Append("\n");
            sb.Append("  HasAccess: ").Append(HasAccess).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            // AppId (string) minLength
            if (this.AppId != null && this.AppId.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for AppId, length must be greater than 1.", new [] { "AppId" });
            }

            // DisplayName (string) minLength
            if (this.DisplayName != null && this.DisplayName.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for DisplayName, length must be greater than 1.", new [] { "DisplayName" });
            }

            // AppCodeName (string) minLength
            if (this.AppCodeName != null && this.AppCodeName.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for AppCodeName, length must be greater than 1.", new [] { "AppCodeName" });
            }

            // AppSetting (string) minLength
            if (this.AppSetting != null && this.AppSetting.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for AppSetting, length must be greater than 1.", new [] { "AppSetting" });
            }

            // Status (string) minLength
            if (this.Status != null && this.Status.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for Status, length must be greater than 1.", new [] { "Status" });
            }

            yield break;
        }
    }

}
