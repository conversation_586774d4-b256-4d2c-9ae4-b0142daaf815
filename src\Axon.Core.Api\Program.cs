using System;
using System.IO.Compression;
using System.Reflection;
using System.Runtime.CompilerServices;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Infrastructure;
using Axon.Core.Api.Infrastructure.IoC;
using Axon.Core.Api.Infrastructure.OpenApi;
using Axon.Core.Api.Infrastructure.OpenApi.Filters;
using Axon.Core.Api.Middleware;
using Axon.Core.Infrastructure.Cosmos.Extensions;
using Axon.Core.Infrastructure.Extensions;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Security.KeyVault.Secrets;
using FluentValidation.AspNetCore;
using JetBrains.Annotations;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using NewRelic.Api.Agent;
using NLog;
using NLog.Web;
using Axon.Core.Api.EventHandlers;
using Phlex.Core.MessageBus.Extensions;
using Swashbuckle.AspNetCore.SwaggerGen;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;
using System.Collections.Generic;
using Axon.Core.Api.Models.Cors;
using FluentValidation;
using Asp.Versioning.ApiExplorer;
using Axon.Core.Api.Formatters;
using Axon.Core.Domain;
using Microsoft.OData.ModelBuilder;
using Microsoft.AspNetCore.OData;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Extensions;
using Axon.Core.Domain.Extensions;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Infrastructure.Cosmos.Providers;
using Microsoft.AspNetCore.Http;
using Phlex.Core.Caching.Memory;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Services.User;
using Axon.Core.Shared.Services.Apps;
using Axon.Core.Shared.Services.Auth;
using Axon.Core.Shared.Interfaces.Auth;

[assembly: InternalsVisibleTo("Axon.Core.Tests")]
[assembly: InternalsVisibleTo("Axon.Core.ArchTests")]
[assembly: InternalsVisibleTo("Axon.Core.IntegrationTests")]
[assembly: InternalsVisibleTo("Axon.Core.Tests")]
[assembly: InternalsVisibleTo("DynamicProxyGenAssembly2")]

var logger = NLogBuilder.ConfigureNLog("nlog.config").GetCurrentClassLogger();
try
{
    var builder = WebApplication.CreateBuilder(args);

    builder.Host
        .ConfigureAppConfiguration((context, config) =>
        {
            if (!context.HostingEnvironment.IsProduction()) return;

            var builtConfig = config.Build();

            var credential = new AzureCredentialBuilder(builtConfig).Build(logger, "Secret Client Provider");

            var secretClient = new SecretClient(
                new Uri($"https://{builtConfig["KeyVaultName"]}.vault.azure.net/"),
                credential);
            config.AddAzureKeyVault(secretClient, new KeyVaultSecretManager());
        });

    // NLog: Setup NLog for Dependency injection
    builder.Logging.ClearProviders();
    builder.Logging.SetMinimumLevel(LogLevel.Trace);
    builder.Host.UseNLog();

    //Required for logs in context
    var nrAgent = new Lazy<IAgent>(NewRelic.Api.Agent.NewRelic.GetAgent());
    foreach (var (key, value) in nrAgent.Value.GetLinkingMetadata()) GlobalDiagnosticsContext.Set(key, value);

    builder.Services.AddFluentValidationAutoValidation().AddValidatorsFromAssemblyContaining<Program>();
    var cosmosDbSettingsProvider = new CosmosDbSettingsProvider(builder.Configuration);
    builder.Services
        .AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()))
        .AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>))
        .AddCosmosDb(cosmosDbSettingsProvider,builder.Environment, builder.Configuration, logger)
        .AddHealthChecks(builder.Configuration)
        .AddAutoMapper(typeof(Program).Assembly);

    builder.Services.AddMessageBus(builder.Configuration, configurator =>
    {
        configurator.UsingTransportFromConfiguration(builder.Configuration);
        configurator.AddCommandHandler<UpsertScopeCommandHandler>(builder.Configuration);
        configurator.AddCommandHandler<DeleteScopeCommandHandler>(builder.Configuration);
        configurator.AddEventHandler<ScopeResourceNameChangedEvenHandler>(builder.Configuration);
        configurator.AddEventHandler<ScopeDeletedEventHandler>(builder.Configuration);

        configurator.AddGoodDataEventHandlers(builder.Configuration);
    });
    // Add API Versioning to the Project
    builder.Services.AddApiVersioning(VersionConfiguration.VersioningOptions).AddApiExplorer(VersionConfiguration.ExplorerOptions);
    // Add Open API generation
    builder.Services.AddTransient<IConfigureOptions<SwaggerGenOptions>>(s => new ConfigureSwaggerOptions(s.GetService<IApiVersionDescriptionProvider>(), AppConstants.ApiTitle, AppConstants.ApiDescription, string.Empty, builder.Configuration));
    builder.Services.AddSwaggerGen(
        c =>
        {
#if !GENERATING_SDKS // See GenerateSDK.ps1 script for details
            c.OperationFilter<ListParamsSwaggerOperationFilter>();
#endif
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey
            });
            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });
        });

    // To compress the response data before sending back to the client
    // This reduces the amount of data transferred and helps faster data transfer
    builder.Services.Configure<GzipCompressionProviderOptions>(options =>
        options.Level = CompressionLevel.Fastest);
    builder.Services.AddResponseCompression();

    builder.Host
        .UseServiceProviderFactory(new AutofacServiceProviderFactory())
        .ConfigureContainer<ContainerBuilder>(b => { b.RegisterModule(new ApiModule(builder.Configuration)); });

    builder.Services
        .AddInfrastructureServices()
        .AddGenericMemoryCaches()
        .AddAccessServices()
        .AddTypedAppSettings(builder.Configuration)
        .AddAzureBlobStorageServices()
        .AddAuditServices(cosmosDbSettingsProvider, builder.Configuration, builder.Environment)
        .AddCoreServices()
        .AddCorrelation()
        .AddAxonAuthentication(builder.Configuration, builder.Environment)
        .AddCors()
        .AddGoodDataServices(builder.Environment, builder.Configuration, logger);

    var modelBuilder = new ODataConventionModelBuilder();

    modelBuilder.ComplexType<AxonAuditEnvironment>();
    modelBuilder.ComplexType<AxonAuditTarget>();
    modelBuilder.EntitySet<TenantAudit>("Audits");

    builder.Services.AddControllers(options =>
    {
        options.InputFormatters.Insert(0, new SafeJsonInputFormatter());
    })
        .AddOData(
        options => options.Filter().OrderBy().SetMaxTop(5000).AddRouteComponents("v{version:apiVersion}/odata/Organisation/{orgCodeName}",
            modelBuilder.GetEdmModel()));

    var app = builder.Build();

    if (builder.Environment.IsDevelopment())
    {
        app.UseDeveloperExceptionPage();
        app.UseSwaggerUI(c => c.SwaggerEndpoint($"/swagger/v{VersionConfiguration.ApiVersion.MajorVersion}/swagger.json", $"{AppConstants.ApiTitle} v{VersionConfiguration.ApiVersion.MajorVersion}"));
        app.UseODataRouteDebug();
    }
    else
    {
        app.UseHsts();
    }

    app.Use(async (context, next) =>
    {
        if (!context.Response.Headers.ContainsKey("X-Frame-Options"))
        {
            context.Response.Headers.Append("X-Frame-Options", "DENY");
        }

        if (!context.Response.Headers.ContainsKey("Content-Security-Policy"))
        {
            context.Response.Headers.Append("Content-Security-Policy", "default-src 'self'; "
                                                                       + "connect-src 'self' " + (builder.Environment.IsDevelopment() ? "wss: ws: http:" : "") + "; "
                                                                       + "frame-ancestors 'none'; "
                                                                       + "object-src 'none';");
        }

        await next();
    });

    app.UseHttpsRedirection();
    app.UseMiddleware<ExceptionMiddleware>();
    app.UseSwagger();
    app.UseRouting();
    app.UseCors(c =>
    {
        List<CorsOrigin> origins = new List<CorsOrigin>();
        builder.Configuration.GetSection("cors:origins").Bind(origins);
        foreach (var o in origins)
        {
            c.WithOrigins(o.Uri);
        }

        c.AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials();
            
    });

    app.UseResponseCompression();
    app.UseAuthentication();
    app.UseAuthorization();
    if (builder.Environment.IsDevelopment())
    {
        await app.EnsureSeedDataIsPlanted(cosmosDbSettingsProvider);
    }

    app.UseEndpoints(endpoints =>
    {
        endpoints.MapControllers();
        HealthCheckExtensions.MapHealthChecks(endpoints);
    });

    app.Run();
}
catch (Exception ex)
{
    //NLog: catch setup errors
    logger.Error(ex, "Stopped program because of exception");
    throw;
}
finally
{
    // Ensure to flush and stop internal timers/threads before application-exit (Avoid segmentation fault on Linux)
    LogManager.Shutdown();
}

public partial class Program
{
    [UsedImplicitly] // NOTE: Used as we can't user the static Program class for ILogger<>
    public class ProgramLogger
    {
    }
}