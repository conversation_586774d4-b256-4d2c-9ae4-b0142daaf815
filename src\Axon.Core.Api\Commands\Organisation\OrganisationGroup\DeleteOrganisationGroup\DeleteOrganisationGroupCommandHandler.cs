﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Services.AppGroup;
using Axon.Core.Api.Services.UserOrganisation;
using Axon.Core.Domain.Constants;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using JetBrains.Annotations;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.DeleteOrganisationGroup;

[UsedImplicitly]
internal class DeleteOrganisationGroupCommandHandler : BaseCommandHandler<GroupEntity, DeleteOrganisationGroupCommandRequest, CommandResponse>
{
    private readonly IOrganisationRepository organisationRepository;
    private readonly IOrganisationUserManager organisationUserManager;
    private readonly IAccessRepository accessRepository;
    private readonly IAppGroupRepository appGroupRepository;
    private readonly IClientDetailsProvider clientDetailsProvider;
    private readonly ICorrelationIdProvider correlationIdProvider;
    private readonly IAppGroupManager appGroupManager;
    private readonly IAuditService<TenantAuditExtensions> auditService;
    private readonly ILogger<DeleteOrganisationGroupCommandHandler> logger;

    public DeleteOrganisationGroupCommandHandler(IGroupRepository repo, 
        IMapper mapper, 
        IMessageBus messageBus, 
        IOrganisationRepository organisationRepository,
        IOrganisationUserManager organisationUserManager, 
        IClientDetailsProvider clientDetailsProvider, 
        ICorrelationIdProvider correlationIdProvider, 
        IAccessRepository accessRepository,
        IAppGroupRepository appGroupRepository,
        IAppGroupManager appGroupManager,
        IAuditService<TenantAuditExtensions> auditService, 
        ILogger<DeleteOrganisationGroupCommandHandler> logger) 
        : base(repo, mapper, messageBus)
    {
        this.organisationRepository = organisationRepository;
        this.organisationUserManager = organisationUserManager;
        this.clientDetailsProvider = clientDetailsProvider;
        this.correlationIdProvider = correlationIdProvider;
        this.accessRepository = accessRepository;
        this.appGroupRepository = appGroupRepository;
        this.appGroupManager = appGroupManager;
        this.auditService = auditService;
        this.logger = logger;
    }

    public override async Task<CommandResponse> Handle(DeleteOrganisationGroupCommandRequest request, CancellationToken cancellationToken)
    {
        var organisationEntity = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);
        if (organisationEntity == null)
        {
            logger.LogWarning("Organisation {organisationCodeName} does not exist.", request.OrganisationCodeName);
            return CommandResponse.NotFound(nameof(OrganisationEntity), request.OrganisationCodeName);
        }

        if (!await Repo.TryGetItemAsync(request.GroupId, out var groupEntity))
        {
            logger.LogWarning("Group with {groupId} does not exist.", request.GroupId);
            return CommandResponse.NotFound(nameof(GroupEntity), request.GroupId);
        }

        if (!string.Equals(groupEntity.OrganisationCodeName, request.OrganisationCodeName, StringComparison.InvariantCultureIgnoreCase))
        {
            logger.LogWarning("Group with {groupId} id in organisation with {organisationCodeName} codename doesn't exist.", request.GroupId, request.OrganisationCodeName);
            return CommandResponse.NotFound(nameof(GroupEntity), request.GroupId);
        }

        var correlationId = correlationIdProvider.Provide();
        var clientDetails = clientDetailsProvider.Provide();

        var groupAccessEntities = await accessRepository.GetAccessItemsForGroupAsync(request.GroupId, AccessType.GroupAccess);
        var groupUsers = groupAccessEntities.Select(x => x.User);

        foreach (var groupUser in groupUsers)
        {
            await organisationUserManager.UnassignUserFromGroup(correlationId, clientDetails, request.OrganisationCodeName, groupUser.Id, groupUser.Email, groupEntity);
        }

        await DeleteGroupAsync(groupEntity.Name, correlationId, clientDetails, request.OrganisationCodeName, groupEntity);
        var appGroupEntities = await appGroupRepository.GetAppGroupsByGroupIdAsync(request.GroupId);

        if (appGroupEntities.Any())
        {
            foreach (var appGroupEntity in appGroupEntities)
            {
                await appGroupManager.RemoveAppGroupAndAccessEntities(appGroupEntity, correlationId, clientDetails);
            }
        }

        return CommandResponse.Success();
    }

    private async Task DeleteGroupAsync(string groupName, Guid correlationId, ClientDetails clientDetails, string organisationCodeName, GroupEntity groupEntity)
    {
        var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.Group, AuditEventDescriptions.GroupDeleted(groupName), correlationId, clientDetails, organisationCodeName);

        await auditService.LogAsync(AuditEventTypes.GroupDeleted, tenantAuditExtensions, groupEntity,
            async () =>
            {
                await Repo.DeleteItemAsync(groupEntity.Id);
            });
    }
}