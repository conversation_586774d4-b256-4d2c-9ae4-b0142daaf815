﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Models.App;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Extensions;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Access;
using Axon.Core.Infrastructure.Extensions;
using CommunityToolkit.Diagnostics;
using MediatR;

namespace Axon.Core.Api.Queries.App.OrganisationIdQuery
{
    internal class GetAppListByOrganisationIdQueryHandler : IRequestHandler<GetAppListByOrganisationIdQueryRequest, CommandResponse<IEnumerable<AppOrganisationModel>>>
    {
        private readonly IOrganisationRepository organisationRepo;
        private readonly IAppRepository appRepo;
        private readonly IMapper mapper;
        private readonly IUserRequestContext userRequestContext;
        private readonly IAccessService accessService;

        public GetAppListByOrganisationIdQueryHandler(IOrganisationRepository organisationRepo, IAppRepository appRepo, IMapper mapper, IUserRequestContext userRequestContext,
            IAccessService accessService)
        {
            Guard.IsNotNull(organisationRepo);
            this.organisationRepo = organisationRepo;
            Guard.IsNotNull(appRepo);
            this.appRepo = appRepo;
            Guard.IsNotNull(mapper);
            this.mapper = mapper;
            Guard.IsNotNull(userRequestContext);
            this.userRequestContext = userRequestContext;
            Guard.IsNotNull(accessService);
            this.accessService = accessService;
        }

        public async Task<CommandResponse<IEnumerable<AppOrganisationModel>>> Handle(GetAppListByOrganisationIdQueryRequest request, CancellationToken cancellationToken)
        {
            var organisationEntity = await organisationRepo.GetItemOrThrowAsync(request.Id);

            var organisationApps = organisationEntity.Apps?.ToList() ?? [];
            if (!organisationApps.Any())
            {
                return CommandResponse<IEnumerable<AppOrganisationModel>>.Data(new List<AppOrganisationModel>());
            }

            var orgIds = organisationApps.Select(o => o.AppId);

            var orgAppEntities = appRepo.GetAllLinqQueryable()
                .Where(x => orgIds.Contains(x.Id))
                .FilteredResult(request.ListParams)
                .ToList();

            var resultList = new List<AppOrganisationModel>();

            var allPermissions = !userRequestContext.IsApplication() ? await accessService.GetUsersDirectEffectivePermissionsForAllAppsAndOrgs(userRequestContext.GetEmailAddress()) : [];
            foreach (var orgAppEntity in orgAppEntities)
            {
                var resultModel = mapper.Map<AppOrganisationModel>(orgAppEntity);

                resultModel.Status = organisationApps.Find(x => x.AppId == orgAppEntity.Id)?.Status.ToString();
                resultModel.ShowAuditLog = GetShowAuditLogValue(organisationEntity, orgAppEntity, allPermissions);
                resultModel.HasAccess = GetHasAccessValue(organisationEntity, orgAppEntity, allPermissions);
                resultList.Add(resultModel);
            }

            return CommandResponse<IEnumerable<AppOrganisationModel>>.Data(resultList);
        }

        private static bool GetShowAuditLogValue(OrganisationEntity organisationEntity, AppEntity app, IReadOnlyCollection<UserEffectivePermissionsForAppAndOrg> allPermissions)
        {
            var matchingPermission = allPermissions.SingleOrDefault(x => x.AppCodeName.Equals(app.AppCodeName, System.StringComparison.OrdinalIgnoreCase) &&
                x.OrgCodeName.Equals(organisationEntity.CodeName, System.StringComparison.OrdinalIgnoreCase));

            if (matchingPermission?.EffectivePermissions.HasPermission(nameof(CorePermissions.ViewAudit)) == true)
            {
                return app.ShowAudits;
            }

            return false;
        }

        private static bool GetHasAccessValue(OrganisationEntity organisationEntity, AppEntity app, IReadOnlyCollection<UserEffectivePermissionsForAppAndOrg> allPermissions)
        {
            var matchingPermission = allPermissions.SingleOrDefault(x => x.AppCodeName.Equals(app.AppCodeName, System.StringComparison.OrdinalIgnoreCase) &&
                x.OrgCodeName.Equals(organisationEntity.CodeName, System.StringComparison.OrdinalIgnoreCase));

            return matchingPermission != null && matchingPermission.EffectivePermissions.HasRole();
        }
    }
}