﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppGroup;
using MediatR;

namespace Axon.Core.Api.Queries.AppGroup.GetById
{
    public class GetAppGroupByIdQueryRequest : IRequest<CommandResponse<AppGroupModel>>
    {
        public string OrganisationCodeName { get; }
        public string AppCodeName { get; }
        public string Id { get; }

        public GetAppGroupByIdQueryRequest(string organisationCodeName, string appCodeName, string id)
        {
            OrganisationCodeName = organisationCodeName;
            AppCodeName = appCodeName;
            Id = id;
        }
    }
}
