﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.OrganisationAccess;
using MediatR;
using System.Collections.Generic;

namespace Axon.Core.Api.Queries.ScopeResource
{
    public record class GetScopeResourcesForRoleQuery(string RoleId, string AppCodeName, string OrgCodeName) : IRequest<CommandResponse<IEnumerable<ScopeDataModel>>>
    {
        public string RoleId { get; } = RoleId;
        public string AppCodeName { get; } = AppCodeName;
        public string OrgCodeName { get; } = OrgCodeName;
    }
}
