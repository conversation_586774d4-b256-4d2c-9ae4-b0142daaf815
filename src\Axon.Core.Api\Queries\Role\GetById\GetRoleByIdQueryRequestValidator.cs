﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.Role.GetById;

[UsedImplicitly]
public class GetRoleByIdQueryRequestValidator : AbstractValidator<GetRoleByIdQueryRequest>
{
    public GetRoleByIdQueryRequestValidator()
    {
        RuleFor(x => x.Id)
            .MustBeAValidGuid();

        RuleFor(x => x.OrgCodeName).MustBeAValidCodeName();
        RuleFor(x => x.AppCodeName).MustBeAValidCodeName();
    }
}