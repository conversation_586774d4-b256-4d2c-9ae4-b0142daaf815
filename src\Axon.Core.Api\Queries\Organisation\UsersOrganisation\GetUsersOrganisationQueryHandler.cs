﻿using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;
using MediatR;

namespace Axon.Core.Api.Queries.Organisation.UsersOrganisation
{
    [UsedImplicitly]
    internal class GetUsersOrganisationQueryHandler : IRequestHandler<GetUsersOrganisationQueryRequest, CommandResponse<OrganisationModel>>
    {
        private readonly IUserRequestContext userRequestContext;
        private readonly IUserRepository userRepository;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IMapper mapper;

        public GetUsersOrganisationQueryHandler(IUserRequestContext userRequestContext, IUserRepository userRepository, IOrganisationRepository organisationRepository, IMapper mapper)
        {
            this.userRequestContext = userRequestContext;
            this.userRepository = userRepository;
            this.organisationRepository = organisationRepository;
            this.mapper = mapper;
        }

        public async Task<CommandResponse<OrganisationModel>> Handle(GetUsersOrganisationQueryRequest request, CancellationToken cancellationToken)
        {
            var userOid = userRequestContext.GetClaimsData().UserOid;
            var user = await userRepository.GetByIdentityProviderObjectIdAsync(userOid);
            if (string.IsNullOrWhiteSpace(user?.OwnerOrganisationId))
                return CommandResponse<OrganisationModel>.NotFound(nameof(UserEntity.OwnerOrganisationId), "User is not linked to an organisation");

            var organisation = await organisationRepository.GetItemAsync(user.OwnerOrganisationId);
            if (organisation == null)
                return CommandResponse<OrganisationModel>.NotFound(nameof(UserEntity.OwnerOrganisationId), "User's organisation does not exist");

            var organisationModel = mapper.Map<OrganisationModel>(organisation);
            return CommandResponse<OrganisationModel>.Data(organisationModel);
        }
    }
}