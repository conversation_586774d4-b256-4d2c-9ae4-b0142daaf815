﻿using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Api.Validators;
using FluentValidation;

namespace Axon.Core.Api.Commands.AppGroup.Create;

public class CreateAppGroupBodyValidator : BaseCommandValidator<CreateAppGroupRequest>
{
    public CreateAppGroupBodyValidator()
    {
        RuleFor(o => o.OrganisationCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
        RuleFor(o => o.AppCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
        RuleFor(o => o.CreateAppGroupBody).NotNull();
        When(o => o.CreateAppGroupBody != null, () => RuleFor(o => o.CreateAppGroupBody.RoleId)
            .MustBeAValidGuid()
            .NotEmpty()
            .WithMessage("'RoleId' must not be empty"));
        When(o => o.CreateAppGroupBody != null, () => RuleFor(o => o.CreateAppGroupBody.RoleName)
            .MustBeAValidRoleName()
            .NotEmpty()
            .WithMessage("'RoleName' must not be empty"));
        When(o => o.CreateAppGroupBody != null, () => RuleFor(o => o.CreateAppGroupBody.GroupId)
            .MustBeAValidGuid()
            .NotEmpty()
            .WithMessage("'GroupId' must not be empty"));
        When(o => o.CreateAppGroupBody != null, () => RuleFor(o => o.CreateAppGroupBody.GroupName)
            .MustBeAValidGroupName()
            .NotEmpty()
            .WithMessage("'GroupName' must not be empty"));
    }
}