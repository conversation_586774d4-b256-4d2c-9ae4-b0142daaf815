/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// UserPreferencesModel
    /// </summary>
    [DataContract(Name = "UserPreferencesModel")]
    public partial class UserPreferencesModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UserPreferencesModel" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected UserPreferencesModel() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="UserPreferencesModel" /> class.
        /// </summary>
        /// <param name="userPreferences">userPreferences (required).</param>
        public UserPreferencesModel(Dictionary<string, string> userPreferences = default(Dictionary<string, string>))
        {
            // to ensure "userPreferences" is required (not null)
            if (userPreferences == null)
            {
                throw new ArgumentNullException("userPreferences is a required property for UserPreferencesModel and cannot be null");
            }
            this.UserPreferences = userPreferences;
        }

        /// <summary>
        /// Gets or Sets UserPreferences
        /// </summary>
        [DataMember(Name = "userPreferences", IsRequired = true, EmitDefaultValue = true)]
        public Dictionary<string, string> UserPreferences { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class UserPreferencesModel {\n");
            sb.Append("  UserPreferences: ").Append(UserPreferences).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
