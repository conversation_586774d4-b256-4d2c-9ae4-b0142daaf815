﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Axon.Core.Domain.Entities;
using Axon.Core.Shared.Api;

namespace Axon.Core.Domain.Interfaces.Persistence
{
    public interface IOrganisationRepository : IRepository<OrganisationEntity>
    {
        Task<OrganisationEntity> GetItemByDisplayNameAsync(string displayName);
        Task<IList<OrganisationEntity>> GetItemsByDisplayNameAsync(string displayName);
        Task<OrganisationEntity> GetItemByCodeNameAsync(string codeName);
        Task<IList<OrganisationEntity>> GetItemsByCodeNameAsync(string codeName);
        Task<IList<OrganisationEntity>> GetItemsByIdentityProviderIdAsync(string identityProviderId);
        Task<IList<OrganisationEntity>> GetItemsByIdentityProviderIdAsync(string identityProviderId, object listParams);
        Task<IList<OrganisationEntity>> GetAllByFilterLinqQueryableAsync(object listParams);
        Task<IList<OrganisationEntity>> GetAllItemsInCodenameSetByFilterLinqQueryableAsync(IReadOnlyCollection<string> codenames, object listParams);
        Task<IList<OrganisationEntity>> GetAllItemsInCodenameSetAsync(IReadOnlyCollection<string> codenames);
        Task<IList<OrganisationEntity>> GetAllChildOrganisationsByParentId(string parentOrganisationId);
        Task<IList<OrganisationEntity>> GetAccessibleItemsInCodenameSetAsync(IReadOnlyCollection<string> directlyAccessibleCodeNames, IReadOnlyCollection<string> parentAccessibility, ListParams listParams = null);
    }
}
