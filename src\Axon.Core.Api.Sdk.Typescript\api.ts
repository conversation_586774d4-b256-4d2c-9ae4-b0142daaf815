/* tslint:disable */
/* eslint-disable */
/**
 * Axon.Core.Api
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * 
 * @export
 * @interface AddOrganisationGroupCommandRequestBody
 */
export interface AddOrganisationGroupCommandRequestBody {
    /**
     * 
     * @type {string}
     * @memberof AddOrganisationGroupCommandRequestBody
     */
    'name': string;
}
/**
 * 
 * @export
 * @interface AddOrganisationGroupUserCommandRequestBody
 */
export interface AddOrganisationGroupUserCommandRequestBody {
    /**
     * 
     * @type {Array<string>}
     * @memberof AddOrganisationGroupUserCommandRequestBody
     */
    'userIds': Array<string>;
}
/**
 * 
 * @export
 * @interface AddOrganisationUserCommandRequestBody
 */
export interface AddOrganisationUserCommandRequestBody {
    /**
     * 
     * @type {string}
     * @memberof AddOrganisationUserCommandRequestBody
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof AddOrganisationUserCommandRequestBody
     */
    'email': string;
    /**
     * 
     * @type {string}
     * @memberof AddOrganisationUserCommandRequestBody
     */
    'status': string;
    /**
     * 
     * @type {Array<string>}
     * @memberof AddOrganisationUserCommandRequestBody
     */
    'groups'?: Array<string> | null;
    /**
     * 
     * @type {Array<GroupOrganisation>}
     * @memberof AddOrganisationUserCommandRequestBody
     */
    'childOrganisations'?: Array<GroupOrganisation> | null;
}
/**
 * 
 * @export
 * @interface AppBody
 */
export interface AppBody {
    /**
     * 
     * @type {string}
     * @memberof AppBody
     */
    'displayName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppBody
     */
    'description'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppBody
     */
    'icon'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppBody
     */
    'appCodeName'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof AppBody
     */
    'isDeleted'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppBody
     */
    'isEnabled'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppBody
     */
    'isDefault'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppBody
     */
    'isSystemApp'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof AppBody
     */
    'clientId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppBody
     */
    'runAs'?: string | null;
}
/**
 * 
 * @export
 * @interface AppConfigModel
 */
export interface AppConfigModel {
    /**
     * 
     * @type {string}
     * @memberof AppConfigModel
     */
    'appId': string;
    /**
     * 
     * @type {string}
     * @memberof AppConfigModel
     */
    'displayName': string;
    /**
     * 
     * @type {string}
     * @memberof AppConfigModel
     */
    'appCodeName': string;
    /**
     * 
     * @type {boolean}
     * @memberof AppConfigModel
     */
    'enabled': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppConfigModel
     */
    'deleted': boolean;
    /**
     * 
     * @type {string}
     * @memberof AppConfigModel
     */
    'appSetting': string;
    /**
     * 
     * @type {string}
     * @memberof AppConfigModel
     */
    'status': string;
    /**
     * 
     * @type {boolean}
     * @memberof AppConfigModel
     */
    'hasAccess': boolean;
}
/**
 * 
 * @export
 * @interface AppGroupModel
 */
export interface AppGroupModel {
    /**
     * 
     * @type {string}
     * @memberof AppGroupModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof AppGroupModel
     */
    'appCodeName': string;
    /**
     * 
     * @type {string}
     * @memberof AppGroupModel
     */
    'organisationCodeName': string;
    /**
     * 
     * @type {string}
     * @memberof AppGroupModel
     */
    'groupName': string;
    /**
     * 
     * @type {string}
     * @memberof AppGroupModel
     */
    'role': string;
    /**
     * 
     * @type {Array<ScopeResources>}
     * @memberof AppGroupModel
     */
    'scopes': Array<ScopeResources>;
    /**
     * 
     * @type {number}
     * @memberof AppGroupModel
     */
    'membersCount': number;
}
/**
 * 
 * @export
 * @interface AppGroupModelApiPagedListResult
 */
export interface AppGroupModelApiPagedListResult {
    /**
     * 
     * @type {Array<AppGroupModel>}
     * @memberof AppGroupModelApiPagedListResult
     */
    'data'?: Array<AppGroupModel> | null;
    /**
     * 
     * @type {AppGroupModelPagingInfo}
     * @memberof AppGroupModelApiPagedListResult
     */
    'paging'?: AppGroupModelPagingInfo;
}
/**
 * 
 * @export
 * @interface AppGroupModelApiPagedListResultCommandResponse
 */
export interface AppGroupModelApiPagedListResultCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof AppGroupModelApiPagedListResultCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof AppGroupModelApiPagedListResultCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof AppGroupModelApiPagedListResultCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof AppGroupModelApiPagedListResultCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppGroupModelApiPagedListResultCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {AppGroupModelApiPagedListResult}
     * @memberof AppGroupModelApiPagedListResultCommandResponse
     */
    'data'?: AppGroupModelApiPagedListResult;
}


/**
 * 
 * @export
 * @interface AppGroupModelCommandResponse
 */
export interface AppGroupModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof AppGroupModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof AppGroupModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof AppGroupModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof AppGroupModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppGroupModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {AppGroupModel}
     * @memberof AppGroupModelCommandResponse
     */
    'data'?: AppGroupModel;
}


/**
 * 
 * @export
 * @interface AppGroupModelPagingInfo
 */
export interface AppGroupModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof AppGroupModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof AppGroupModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof AppGroupModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface AppModel
 */
export interface AppModel {
    /**
     * 
     * @type {string}
     * @memberof AppModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof AppModel
     */
    'clientId': string;
    /**
     * 
     * @type {string}
     * @memberof AppModel
     */
    'displayName': string;
    /**
     * 
     * @type {string}
     * @memberof AppModel
     */
    'description': string;
    /**
     * 
     * @type {string}
     * @memberof AppModel
     */
    'icon': string;
    /**
     * 
     * @type {string}
     * @memberof AppModel
     */
    'url': string;
    /**
     * 
     * @type {string}
     * @memberof AppModel
     */
    'appCodeName': string;
    /**
     * 
     * @type {boolean}
     * @memberof AppModel
     */
    'isDeleted': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppModel
     */
    'isEnabled': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppModel
     */
    'isDefault': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppModel
     */
    'isSystemApp': boolean;
    /**
     * 
     * @type {string}
     * @memberof AppModel
     */
    'runAs': string;
    /**
     * 
     * @type {AppThemeConfigModel}
     * @memberof AppModel
     */
    'theme'?: AppThemeConfigModel;
}
/**
 * 
 * @export
 * @interface AppModelCommandResponse
 */
export interface AppModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof AppModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof AppModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof AppModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof AppModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {AppModel}
     * @memberof AppModelCommandResponse
     */
    'data'?: AppModel;
}


/**
 * 
 * @export
 * @interface AppModelIEnumerableCommandResponse
 */
export interface AppModelIEnumerableCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof AppModelIEnumerableCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof AppModelIEnumerableCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof AppModelIEnumerableCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof AppModelIEnumerableCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppModelIEnumerableCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {Array<AppModel>}
     * @memberof AppModelIEnumerableCommandResponse
     */
    'data'?: Array<AppModel> | null;
}


/**
 * 
 * @export
 * @interface AppOrganisationMasterDataBody
 */
export interface AppOrganisationMasterDataBody {
    /**
     * 
     * @type {Array<MasterDataSelectedValue>}
     * @memberof AppOrganisationMasterDataBody
     */
    'selected'?: Array<MasterDataSelectedValue> | null;
}
/**
 * 
 * @export
 * @interface AppOrganisationModel
 */
export interface AppOrganisationModel {
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModel
     */
    'displayName': string;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModel
     */
    'description': string;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModel
     */
    'icon': string;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModel
     */
    'codeName': string;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModel
     */
    'appCodeName': string;
    /**
     * 
     * @type {boolean}
     * @memberof AppOrganisationModel
     */
    'isDeleted': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppOrganisationModel
     */
    'isEnabled': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppOrganisationModel
     */
    'isSystemApp': boolean;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModel
     */
    'status': string;
    /**
     * 
     * @type {boolean}
     * @memberof AppOrganisationModel
     */
    'isDefault': boolean;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModel
     */
    'runAs': string;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModel
     */
    'url': string;
    /**
     * 
     * @type {boolean}
     * @memberof AppOrganisationModel
     */
    'showAuditLog': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppOrganisationModel
     */
    'hasAccess': boolean;
    /**
     * 
     * @type {AppThemeConfigModel}
     * @memberof AppOrganisationModel
     */
    'theme'?: AppThemeConfigModel;
}
/**
 * 
 * @export
 * @interface AppOrganisationModelIEnumerableCommandResponse
 */
export interface AppOrganisationModelIEnumerableCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModelIEnumerableCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof AppOrganisationModelIEnumerableCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof AppOrganisationModelIEnumerableCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModelIEnumerableCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationModelIEnumerableCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {Array<AppOrganisationModel>}
     * @memberof AppOrganisationModelIEnumerableCommandResponse
     */
    'data'?: Array<AppOrganisationModel> | null;
}


/**
 * 
 * @export
 * @interface AppOrganisationPermission
 */
export interface AppOrganisationPermission {
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationPermission
     */
    'permission': string;
    /**
     * 
     * @type {boolean}
     * @memberof AppOrganisationPermission
     */
    'hasScopes': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AppOrganisationPermission
     */
    'permitted': boolean;
    /**
     * 
     * @type {Array<AppOrganisationPermissionScope>}
     * @memberof AppOrganisationPermission
     */
    'scopes': Array<AppOrganisationPermissionScope>;
}
/**
 * 
 * @export
 * @interface AppOrganisationPermissionIEnumerableCommandResponse
 */
export interface AppOrganisationPermissionIEnumerableCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationPermissionIEnumerableCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof AppOrganisationPermissionIEnumerableCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof AppOrganisationPermissionIEnumerableCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationPermissionIEnumerableCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationPermissionIEnumerableCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {Array<AppOrganisationPermission>}
     * @memberof AppOrganisationPermissionIEnumerableCommandResponse
     */
    'data'?: Array<AppOrganisationPermission> | null;
}


/**
 * 
 * @export
 * @interface AppOrganisationPermissionScope
 */
export interface AppOrganisationPermissionScope {
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationPermissionScope
     */
    'scope': string;
    /**
     * 
     * @type {boolean}
     * @memberof AppOrganisationPermissionScope
     */
    'permitted': boolean;
    /**
     * 
     * @type {Array<AppOrganisationPermissionScopeResource>}
     * @memberof AppOrganisationPermissionScope
     */
    'resources': Array<AppOrganisationPermissionScopeResource>;
}
/**
 * 
 * @export
 * @interface AppOrganisationPermissionScopeResource
 */
export interface AppOrganisationPermissionScopeResource {
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationPermissionScopeResource
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof AppOrganisationPermissionScopeResource
     */
    'id': string;
}
/**
 * 
 * @export
 * @interface AppOrganisationSettingsBody
 */
export interface AppOrganisationSettingsBody {
    /**
     * 
     * @type {any}
     * @memberof AppOrganisationSettingsBody
     */
    'value'?: any;
}
/**
 * 
 * @export
 * @interface AppRoleDefinitionPermission
 */
export interface AppRoleDefinitionPermission {
    /**
     * 
     * @type {string}
     * @memberof AppRoleDefinitionPermission
     */
    'name'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof AppRoleDefinitionPermission
     */
    'hasScopes'?: boolean;
    /**
     * 
     * @type {Array<ValidScopeBody>}
     * @memberof AppRoleDefinitionPermission
     */
    'validScopes'?: Array<ValidScopeBody> | null;
}
/**
 * 
 * @export
 * @interface AppRoleDefinitionPermissionIEnumerableCommandResponse
 */
export interface AppRoleDefinitionPermissionIEnumerableCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof AppRoleDefinitionPermissionIEnumerableCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof AppRoleDefinitionPermissionIEnumerableCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof AppRoleDefinitionPermissionIEnumerableCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof AppRoleDefinitionPermissionIEnumerableCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppRoleDefinitionPermissionIEnumerableCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {Array<AppRoleDefinitionPermission>}
     * @memberof AppRoleDefinitionPermissionIEnumerableCommandResponse
     */
    'data'?: Array<AppRoleDefinitionPermission> | null;
}


/**
 * 
 * @export
 * @interface AppThemeConfigModel
 */
export interface AppThemeConfigModel {
    /**
     * 
     * @type {string}
     * @memberof AppThemeConfigModel
     */
    'avatarUrl'?: string | null;
}
/**
 * 
 * @export
 * @interface AppUserGroupModel
 */
export interface AppUserGroupModel {
    /**
     * 
     * @type {string}
     * @memberof AppUserGroupModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof AppUserGroupModel
     */
    'groupName': string;
}
/**
 * 
 * @export
 * @interface AppUserModel
 */
export interface AppUserModel {
    /**
     * 
     * @type {string}
     * @memberof AppUserModel
     */
    'appCodeName': string;
    /**
     * 
     * @type {string}
     * @memberof AppUserModel
     */
    'organisationCodeName': string;
    /**
     * 
     * @type {string}
     * @memberof AppUserModel
     */
    'userId': string;
    /**
     * 
     * @type {string}
     * @memberof AppUserModel
     */
    'userName': string;
    /**
     * 
     * @type {string}
     * @memberof AppUserModel
     */
    'email': string;
    /**
     * 
     * @type {Array<AppUserRoleModel>}
     * @memberof AppUserModel
     */
    'roles': Array<AppUserRoleModel>;
    /**
     * 
     * @type {Array<AppUserGroupModel>}
     * @memberof AppUserModel
     */
    'groups': Array<AppUserGroupModel>;
    /**
     * 
     * @type {boolean}
     * @memberof AppUserModel
     */
    'hasDirectRole': boolean;
}
/**
 * 
 * @export
 * @interface AppUserModelApiPagedListResult
 */
export interface AppUserModelApiPagedListResult {
    /**
     * 
     * @type {Array<AppUserModel>}
     * @memberof AppUserModelApiPagedListResult
     */
    'data'?: Array<AppUserModel> | null;
    /**
     * 
     * @type {AppUserModelPagingInfo}
     * @memberof AppUserModelApiPagedListResult
     */
    'paging'?: AppUserModelPagingInfo;
}
/**
 * 
 * @export
 * @interface AppUserModelApiPagedListResultCommandResponse
 */
export interface AppUserModelApiPagedListResultCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof AppUserModelApiPagedListResultCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof AppUserModelApiPagedListResultCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof AppUserModelApiPagedListResultCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof AppUserModelApiPagedListResultCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AppUserModelApiPagedListResultCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {AppUserModelApiPagedListResult}
     * @memberof AppUserModelApiPagedListResultCommandResponse
     */
    'data'?: AppUserModelApiPagedListResult;
}


/**
 * 
 * @export
 * @interface AppUserModelPagingInfo
 */
export interface AppUserModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof AppUserModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof AppUserModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof AppUserModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface AppUserRoleModel
 */
export interface AppUserRoleModel {
    /**
     * 
     * @type {string}
     * @memberof AppUserRoleModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof AppUserRoleModel
     */
    'roleName': string;
}
/**
 * 
 * @export
 * @interface BooleanCommandResponse
 */
export interface BooleanCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof BooleanCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof BooleanCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof BooleanCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof BooleanCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof BooleanCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof BooleanCommandResponse
     */
    'data'?: boolean;
}


/**
 * 
 * @export
 * @interface ChildOrganisationModel
 */
export interface ChildOrganisationModel {
    /**
     * 
     * @type {string}
     * @memberof ChildOrganisationModel
     */
    'organisationName': string;
    /**
     * 
     * @type {string}
     * @memberof ChildOrganisationModel
     */
    'organisationCodeName': string;
    /**
     * 
     * @type {Array<GroupModel>}
     * @memberof ChildOrganisationModel
     */
    'groups': Array<GroupModel>;
}
/**
 * 
 * @export
 * @interface CommandResponse
 */
export interface CommandResponse {
    /**
     * 
     * @type {string}
     * @memberof CommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof CommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof CommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof CommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {any}
     * @memberof CommandResponse
     */
    'data'?: any | null;
}


/**
 * 
 * @export
 * @interface CreateAppGroupBody
 */
export interface CreateAppGroupBody {
    /**
     * 
     * @type {string}
     * @memberof CreateAppGroupBody
     */
    'groupId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateAppGroupBody
     */
    'groupName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateAppGroupBody
     */
    'roleId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateAppGroupBody
     */
    'roleName'?: string | null;
    /**
     * 
     * @type {Array<ScopeResourcesBody>}
     * @memberof CreateAppGroupBody
     */
    'scopes'?: Array<ScopeResourcesBody> | null;
}
/**
 * 
 * @export
 * @interface CreateAppUserModel
 */
export interface CreateAppUserModel {
    /**
     * 
     * @type {string}
     * @memberof CreateAppUserModel
     */
    'roleId'?: string | null;
    /**
     * 
     * @type {Array<ScopeResourcesBody>}
     * @memberof CreateAppUserModel
     */
    'scopes'?: Array<ScopeResourcesBody> | null;
}
/**
 * 
 * @export
 * @interface Dashboard
 */
export interface Dashboard {
    /**
     * 
     * @type {string}
     * @memberof Dashboard
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Dashboard
     */
    'title'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Dashboard
     */
    'description'?: string | null;
}
/**
 * 
 * @export
 * @interface EditOrganisationGroupCommandRequestBody
 */
export interface EditOrganisationGroupCommandRequestBody {
    /**
     * 
     * @type {string}
     * @memberof EditOrganisationGroupCommandRequestBody
     */
    'name': string;
}
/**
 * 
 * @export
 * @interface GroupModel
 */
export interface GroupModel {
    /**
     * 
     * @type {string}
     * @memberof GroupModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof GroupModel
     */
    'name': string;
}
/**
 * 
 * @export
 * @interface GroupOrganisation
 */
export interface GroupOrganisation {
    /**
     * 
     * @type {string}
     * @memberof GroupOrganisation
     */
    'organisationCodeName': string;
    /**
     * 
     * @type {Array<string>}
     * @memberof GroupOrganisation
     */
    'groups'?: Array<string> | null;
}
/**
 * 
 * @export
 * @enum {string}
 */

export const HttpStatusCode = {
    NUMBER_100: 100,
    NUMBER_101: 101,
    NUMBER_102: 102,
    NUMBER_103: 103,
    NUMBER_200: 200,
    NUMBER_201: 201,
    NUMBER_202: 202,
    NUMBER_203: 203,
    NUMBER_204: 204,
    NUMBER_205: 205,
    NUMBER_206: 206,
    NUMBER_207: 207,
    NUMBER_208: 208,
    NUMBER_226: 226,
    NUMBER_300: 300,
    NUMBER_301: 301,
    NUMBER_302: 302,
    NUMBER_303: 303,
    NUMBER_304: 304,
    NUMBER_305: 305,
    NUMBER_306: 306,
    NUMBER_307: 307,
    NUMBER_308: 308,
    NUMBER_400: 400,
    NUMBER_401: 401,
    NUMBER_402: 402,
    NUMBER_403: 403,
    NUMBER_404: 404,
    NUMBER_405: 405,
    NUMBER_406: 406,
    NUMBER_407: 407,
    NUMBER_408: 408,
    NUMBER_409: 409,
    NUMBER_410: 410,
    NUMBER_411: 411,
    NUMBER_412: 412,
    NUMBER_413: 413,
    NUMBER_414: 414,
    NUMBER_415: 415,
    NUMBER_416: 416,
    NUMBER_417: 417,
    NUMBER_421: 421,
    NUMBER_422: 422,
    NUMBER_423: 423,
    NUMBER_424: 424,
    NUMBER_426: 426,
    NUMBER_428: 428,
    NUMBER_429: 429,
    NUMBER_431: 431,
    NUMBER_451: 451,
    NUMBER_500: 500,
    NUMBER_501: 501,
    NUMBER_502: 502,
    NUMBER_503: 503,
    NUMBER_504: 504,
    NUMBER_505: 505,
    NUMBER_506: 506,
    NUMBER_507: 507,
    NUMBER_508: 508,
    NUMBER_510: 510,
    NUMBER_511: 511
} as const;

export type HttpStatusCode = typeof HttpStatusCode[keyof typeof HttpStatusCode];


/**
 * 
 * @export
 * @interface LoginProviderDetails
 */
export interface LoginProviderDetails {
    /**
     * 
     * @type {string}
     * @memberof LoginProviderDetails
     */
    'clientId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof LoginProviderDetails
     */
    'authority'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof LoginProviderDetails
     */
    'useCustomRefresh'?: boolean;
}
/**
 * 
 * @export
 * @interface LoginProviderDetailsCommandResponse
 */
export interface LoginProviderDetailsCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof LoginProviderDetailsCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof LoginProviderDetailsCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof LoginProviderDetailsCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof LoginProviderDetailsCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof LoginProviderDetailsCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {LoginProviderDetails}
     * @memberof LoginProviderDetailsCommandResponse
     */
    'data'?: LoginProviderDetails;
}


/**
 * 
 * @export
 * @interface MasterDataModel
 */
export interface MasterDataModel {
    /**
     * 
     * @type {string}
     * @memberof MasterDataModel
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataModel
     */
    'dataType'?: string | null;
    /**
     * 
     * @type {{ [key: string]: any | null; }}
     * @memberof MasterDataModel
     */
    'data'?: { [key: string]: any | null; } | null;
}
/**
 * 
 * @export
 * @interface MasterDataModelCommandResponse
 */
export interface MasterDataModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof MasterDataModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof MasterDataModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof MasterDataModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {MasterDataModel}
     * @memberof MasterDataModelCommandResponse
     */
    'data'?: MasterDataModel;
}


/**
 * 
 * @export
 * @interface MasterDataModelIEnumerableCommandResponse
 */
export interface MasterDataModelIEnumerableCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof MasterDataModelIEnumerableCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof MasterDataModelIEnumerableCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof MasterDataModelIEnumerableCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataModelIEnumerableCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataModelIEnumerableCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {Array<MasterDataModel>}
     * @memberof MasterDataModelIEnumerableCommandResponse
     */
    'data'?: Array<MasterDataModel> | null;
}


/**
 * 
 * @export
 * @interface MasterDataSelectedValue
 */
export interface MasterDataSelectedValue {
    /**
     * 
     * @type {string}
     * @memberof MasterDataSelectedValue
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataSelectedValue
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface MasterDataValueModel
 */
export interface MasterDataValueModel {
    /**
     * 
     * @type {string}
     * @memberof MasterDataValueModel
     */
    'category'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataValueModel
     */
    'displayName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataValueModel
     */
    'description'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataValueModel
     */
    'masterDataType'?: string | null;
    /**
     * 
     * @type {Array<MasterDataSelectedValue>}
     * @memberof MasterDataValueModel
     */
    'selected'?: Array<MasterDataSelectedValue> | null;
}
/**
 * 
 * @export
 * @interface MasterDataValueModelIEnumerableCommandResponse
 */
export interface MasterDataValueModelIEnumerableCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof MasterDataValueModelIEnumerableCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof MasterDataValueModelIEnumerableCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof MasterDataValueModelIEnumerableCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataValueModelIEnumerableCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MasterDataValueModelIEnumerableCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {Array<MasterDataValueModel>}
     * @memberof MasterDataValueModelIEnumerableCommandResponse
     */
    'data'?: Array<MasterDataValueModel> | null;
}


/**
 * 
 * @export
 * @interface OrgColour
 */
export interface OrgColour {
    /**
     * 
     * @type {string}
     * @memberof OrgColour
     */
    'highlights'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrgColour
     */
    'buttons'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrgColour
     */
    'hover'?: string | null;
}
/**
 * 
 * @export
 * @interface OrgColourModel
 */
export interface OrgColourModel {
    /**
     * 
     * @type {string}
     * @memberof OrgColourModel
     */
    'highlights'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrgColourModel
     */
    'buttons'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrgColourModel
     */
    'hover'?: string | null;
}
/**
 * 
 * @export
 * @interface OrganisationBody
 */
export interface OrganisationBody {
    /**
     * 
     * @type {string}
     * @memberof OrganisationBody
     */
    'displayName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationBody
     */
    'codeName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationBody
     */
    'description'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationBody
     */
    'icon'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationBody
     */
    'accessLevel'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof OrganisationBody
     */
    'isDeleted'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof OrganisationBody
     */
    'isEnabled'?: boolean;
    /**
     * 
     * @type {Array<SetupAppBody>}
     * @memberof OrganisationBody
     */
    'apps'?: Array<SetupAppBody> | null;
    /**
     * 
     * @type {SetupThemeBody}
     * @memberof OrganisationBody
     */
    'theme'?: SetupThemeBody;
    /**
     * 
     * @type {string}
     * @memberof OrganisationBody
     */
    'parentOrganisationId'?: string | null;
}
/**
 * 
 * @export
 * @interface OrganisationGroupModel
 */
export interface OrganisationGroupModel {
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupModel
     */
    'name': string;
    /**
     * 
     * @type {Array<OrganisationGroupUserModel>}
     * @memberof OrganisationGroupModel
     */
    'users': Array<OrganisationGroupUserModel>;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupModel
     */
    'createdAt'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupModel
     */
    'lastUpdatedDate'?: string | null;
}
/**
 * 
 * @export
 * @interface OrganisationGroupModelApiPagedListResult
 */
export interface OrganisationGroupModelApiPagedListResult {
    /**
     * 
     * @type {Array<OrganisationGroupModel>}
     * @memberof OrganisationGroupModelApiPagedListResult
     */
    'data'?: Array<OrganisationGroupModel> | null;
    /**
     * 
     * @type {OrganisationGroupModelPagingInfo}
     * @memberof OrganisationGroupModelApiPagedListResult
     */
    'paging'?: OrganisationGroupModelPagingInfo;
}
/**
 * 
 * @export
 * @interface OrganisationGroupModelApiPagedListResultCommandResponse
 */
export interface OrganisationGroupModelApiPagedListResultCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupModelApiPagedListResultCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof OrganisationGroupModelApiPagedListResultCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof OrganisationGroupModelApiPagedListResultCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupModelApiPagedListResultCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupModelApiPagedListResultCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {OrganisationGroupModelApiPagedListResult}
     * @memberof OrganisationGroupModelApiPagedListResultCommandResponse
     */
    'data'?: OrganisationGroupModelApiPagedListResult;
}


/**
 * 
 * @export
 * @interface OrganisationGroupModelCommandResponse
 */
export interface OrganisationGroupModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof OrganisationGroupModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof OrganisationGroupModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {OrganisationGroupModel}
     * @memberof OrganisationGroupModelCommandResponse
     */
    'data'?: OrganisationGroupModel;
}


/**
 * 
 * @export
 * @interface OrganisationGroupModelPagingInfo
 */
export interface OrganisationGroupModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof OrganisationGroupModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof OrganisationGroupModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof OrganisationGroupModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface OrganisationGroupPagedModel
 */
export interface OrganisationGroupPagedModel {
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupPagedModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupPagedModel
     */
    'name': string;
    /**
     * 
     * @type {OrganisationGroupUserModelApiPagedListResult}
     * @memberof OrganisationGroupPagedModel
     */
    'users': OrganisationGroupUserModelApiPagedListResult;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupPagedModel
     */
    'createdAt': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupPagedModel
     */
    'lastUpdatedDate': string;
}
/**
 * 
 * @export
 * @interface OrganisationGroupPagedModelCommandResponse
 */
export interface OrganisationGroupPagedModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupPagedModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof OrganisationGroupPagedModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof OrganisationGroupPagedModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupPagedModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupPagedModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {OrganisationGroupPagedModel}
     * @memberof OrganisationGroupPagedModelCommandResponse
     */
    'data'?: OrganisationGroupPagedModel;
}


/**
 * 
 * @export
 * @interface OrganisationGroupUserModel
 */
export interface OrganisationGroupUserModel {
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupUserModel
     */
    'userId': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupUserModel
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationGroupUserModel
     */
    'email': string;
}
/**
 * 
 * @export
 * @interface OrganisationGroupUserModelApiPagedListResult
 */
export interface OrganisationGroupUserModelApiPagedListResult {
    /**
     * 
     * @type {Array<OrganisationGroupUserModel>}
     * @memberof OrganisationGroupUserModelApiPagedListResult
     */
    'data'?: Array<OrganisationGroupUserModel> | null;
    /**
     * 
     * @type {OrganisationGroupUserModelPagingInfo}
     * @memberof OrganisationGroupUserModelApiPagedListResult
     */
    'paging'?: OrganisationGroupUserModelPagingInfo;
}
/**
 * 
 * @export
 * @interface OrganisationGroupUserModelPagingInfo
 */
export interface OrganisationGroupUserModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof OrganisationGroupUserModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof OrganisationGroupUserModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof OrganisationGroupUserModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface OrganisationModel
 */
export interface OrganisationModel {
    /**
     * 
     * @type {string}
     * @memberof OrganisationModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationModel
     */
    'displayName': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationModel
     */
    'description': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationModel
     */
    'icon': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationModel
     */
    'codeName': string;
    /**
     * 
     * @type {boolean}
     * @memberof OrganisationModel
     */
    'isDeleted': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof OrganisationModel
     */
    'isEnabled': boolean;
    /**
     * 
     * @type {string}
     * @memberof OrganisationModel
     */
    'accessLevel': string;
    /**
     * 
     * @type {Array<string>}
     * @memberof OrganisationModel
     */
    'allowedRoles': Array<string>;
    /**
     * 
     * @type {boolean}
     * @memberof OrganisationModel
     */
    'showAudits': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof OrganisationModel
     */
    'canManage': boolean;
    /**
     * 
     * @type {Array<AppConfigModel>}
     * @memberof OrganisationModel
     */
    'apps': Array<AppConfigModel>;
    /**
     * 
     * @type {ThemeConfigModel}
     * @memberof OrganisationModel
     */
    'theme'?: ThemeConfigModel;
    /**
     * 
     * @type {string}
     * @memberof OrganisationModel
     */
    'parentOrganisationId'?: string | null;
}
/**
 * 
 * @export
 * @interface OrganisationModelCommandResponse
 */
export interface OrganisationModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof OrganisationModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof OrganisationModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof OrganisationModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {OrganisationModel}
     * @memberof OrganisationModelCommandResponse
     */
    'data'?: OrganisationModel;
}


/**
 * 
 * @export
 * @interface OrganisationModelIEnumerableCommandResponse
 */
export interface OrganisationModelIEnumerableCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof OrganisationModelIEnumerableCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof OrganisationModelIEnumerableCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof OrganisationModelIEnumerableCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationModelIEnumerableCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationModelIEnumerableCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {Array<OrganisationModel>}
     * @memberof OrganisationModelIEnumerableCommandResponse
     */
    'data'?: Array<OrganisationModel> | null;
}


/**
 * 
 * @export
 * @interface OrganisationUserModel
 */
export interface OrganisationUserModel {
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModel
     */
    'userId': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModel
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModel
     */
    'email': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModel
     */
    'lastAccessed'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModel
     */
    'status': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModel
     */
    'organisationName': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModel
     */
    'organisationCodeName': string;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModel
     */
    'createdAt'?: string | null;
    /**
     * 
     * @type {Array<GroupModel>}
     * @memberof OrganisationUserModel
     */
    'groups': Array<GroupModel>;
    /**
     * 
     * @type {Array<ChildOrganisationModel>}
     * @memberof OrganisationUserModel
     */
    'childOrganisations': Array<ChildOrganisationModel>;
}
/**
 * 
 * @export
 * @interface OrganisationUserModelApiPagedListResult
 */
export interface OrganisationUserModelApiPagedListResult {
    /**
     * 
     * @type {Array<OrganisationUserModel>}
     * @memberof OrganisationUserModelApiPagedListResult
     */
    'data'?: Array<OrganisationUserModel> | null;
    /**
     * 
     * @type {OrganisationUserModelPagingInfo}
     * @memberof OrganisationUserModelApiPagedListResult
     */
    'paging'?: OrganisationUserModelPagingInfo;
}
/**
 * 
 * @export
 * @interface OrganisationUserModelApiPagedListResultCommandResponse
 */
export interface OrganisationUserModelApiPagedListResultCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModelApiPagedListResultCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof OrganisationUserModelApiPagedListResultCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof OrganisationUserModelApiPagedListResultCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModelApiPagedListResultCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModelApiPagedListResultCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {OrganisationUserModelApiPagedListResult}
     * @memberof OrganisationUserModelApiPagedListResultCommandResponse
     */
    'data'?: OrganisationUserModelApiPagedListResult;
}


/**
 * 
 * @export
 * @interface OrganisationUserModelCommandResponse
 */
export interface OrganisationUserModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof OrganisationUserModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof OrganisationUserModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OrganisationUserModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {OrganisationUserModel}
     * @memberof OrganisationUserModelCommandResponse
     */
    'data'?: OrganisationUserModel;
}


/**
 * 
 * @export
 * @interface OrganisationUserModelPagingInfo
 */
export interface OrganisationUserModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof OrganisationUserModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof OrganisationUserModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof OrganisationUserModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface Permission
 */
export interface Permission {
    /**
     * 
     * @type {string}
     * @memberof Permission
     */
    'name'?: string | null;
    /**
     * 
     * @type {Array<ScopeResources>}
     * @memberof Permission
     */
    'scopes'?: Array<ScopeResources> | null;
    /**
     * 
     * @type {boolean}
     * @memberof Permission
     */
    'allow'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof Permission
     */
    'isInherited'?: boolean;
}
/**
 * 
 * @export
 * @interface PermissionBody
 */
export interface PermissionBody {
    /**
     * 
     * @type {string}
     * @memberof PermissionBody
     */
    'name'?: string | null;
    /**
     * 
     * @type {Array<ScopeResourcesBody>}
     * @memberof PermissionBody
     */
    'scopes'?: Array<ScopeResourcesBody> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PermissionBody
     */
    'allow'?: boolean;
}
/**
 * 
 * @export
 * @interface Resource
 */
export interface Resource {
    /**
     * 
     * @type {string}
     * @memberof Resource
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Resource
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface ResourceBody
 */
export interface ResourceBody {
    /**
     * 
     * @type {string}
     * @memberof ResourceBody
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ResourceBody
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface RoleBody
 */
export interface RoleBody {
    /**
     * 
     * @type {string}
     * @memberof RoleBody
     */
    'roleName'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof RoleBody
     */
    'isEnabled'?: boolean;
    /**
     * 
     * @type {Array<UserGroupScopeBody>}
     * @memberof RoleBody
     */
    'userGroupScopes'?: Array<UserGroupScopeBody> | null;
    /**
     * 
     * @type {Array<PermissionBody>}
     * @memberof RoleBody
     */
    'permissions'?: Array<PermissionBody> | null;
}
/**
 * 
 * @export
 * @interface RoleModel
 */
export interface RoleModel {
    /**
     * 
     * @type {string}
     * @memberof RoleModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof RoleModel
     */
    'appCodeName': string;
    /**
     * 
     * @type {string}
     * @memberof RoleModel
     */
    'organisationCodeName': string;
    /**
     * 
     * @type {string}
     * @memberof RoleModel
     */
    'roleType': string;
    /**
     * 
     * @type {string}
     * @memberof RoleModel
     */
    'roleName': string;
    /**
     * 
     * @type {boolean}
     * @memberof RoleModel
     */
    'isEnabled': boolean;
    /**
     * 
     * @type {Array<UserGroupScope>}
     * @memberof RoleModel
     */
    'userGroupScopes'?: Array<UserGroupScope> | null;
    /**
     * 
     * @type {Array<Permission>}
     * @memberof RoleModel
     */
    'permissions'?: Array<Permission> | null;
}
/**
 * 
 * @export
 * @interface RoleModelApiPagedListResult
 */
export interface RoleModelApiPagedListResult {
    /**
     * 
     * @type {Array<RoleModel>}
     * @memberof RoleModelApiPagedListResult
     */
    'data'?: Array<RoleModel> | null;
    /**
     * 
     * @type {RoleModelPagingInfo}
     * @memberof RoleModelApiPagedListResult
     */
    'paging'?: RoleModelPagingInfo;
}
/**
 * 
 * @export
 * @interface RoleModelApiPagedListResultCommandResponse
 */
export interface RoleModelApiPagedListResultCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof RoleModelApiPagedListResultCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof RoleModelApiPagedListResultCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof RoleModelApiPagedListResultCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof RoleModelApiPagedListResultCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RoleModelApiPagedListResultCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {RoleModelApiPagedListResult}
     * @memberof RoleModelApiPagedListResultCommandResponse
     */
    'data'?: RoleModelApiPagedListResult;
}


/**
 * 
 * @export
 * @interface RoleModelCommandResponse
 */
export interface RoleModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof RoleModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof RoleModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof RoleModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof RoleModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RoleModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {RoleModel}
     * @memberof RoleModelCommandResponse
     */
    'data'?: RoleModel;
}


/**
 * 
 * @export
 * @interface RoleModelIEnumerableCommandResponse
 */
export interface RoleModelIEnumerableCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof RoleModelIEnumerableCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof RoleModelIEnumerableCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof RoleModelIEnumerableCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof RoleModelIEnumerableCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RoleModelIEnumerableCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {Array<RoleModel>}
     * @memberof RoleModelIEnumerableCommandResponse
     */
    'data'?: Array<RoleModel> | null;
}


/**
 * 
 * @export
 * @interface RoleModelPagingInfo
 */
export interface RoleModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof RoleModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof RoleModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof RoleModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface ScopeDataModel
 */
export interface ScopeDataModel {
    /**
     * 
     * @type {string}
     * @memberof ScopeDataModel
     */
    'scope': string;
    /**
     * 
     * @type {Array<ScopeResourceDataModel>}
     * @memberof ScopeDataModel
     */
    'resources': Array<ScopeResourceDataModel>;
}
/**
 * 
 * @export
 * @interface ScopeDataModelIEnumerableCommandResponse
 */
export interface ScopeDataModelIEnumerableCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof ScopeDataModelIEnumerableCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof ScopeDataModelIEnumerableCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof ScopeDataModelIEnumerableCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof ScopeDataModelIEnumerableCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ScopeDataModelIEnumerableCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {Array<ScopeDataModel>}
     * @memberof ScopeDataModelIEnumerableCommandResponse
     */
    'data'?: Array<ScopeDataModel> | null;
}


/**
 * 
 * @export
 * @interface ScopeResourceDataModel
 */
export interface ScopeResourceDataModel {
    /**
     * 
     * @type {string}
     * @memberof ScopeResourceDataModel
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof ScopeResourceDataModel
     */
    'id': string;
}
/**
 * 
 * @export
 * @interface ScopeResources
 */
export interface ScopeResources {
    /**
     * 
     * @type {string}
     * @memberof ScopeResources
     */
    'scope'?: string | null;
    /**
     * 
     * @type {Array<Resource>}
     * @memberof ScopeResources
     */
    'resources'?: Array<Resource> | null;
}
/**
 * 
 * @export
 * @interface ScopeResourcesBody
 */
export interface ScopeResourcesBody {
    /**
     * 
     * @type {string}
     * @memberof ScopeResourcesBody
     */
    'scope'?: string | null;
    /**
     * 
     * @type {Array<ResourceBody>}
     * @memberof ScopeResourcesBody
     */
    'resources'?: Array<ResourceBody> | null;
}
/**
 * 
 * @export
 * @enum {string}
 */

export const ScopeType = {
    NUMBER_0: 0,
    NUMBER_1: 1
} as const;

export type ScopeType = typeof ScopeType[keyof typeof ScopeType];


/**
 * 
 * @export
 * @enum {string}
 */

export const SettingDataType = {
    NUMBER_0: 0,
    NUMBER_1: 1,
    NUMBER_2: 2,
    NUMBER_3: 3,
    NUMBER_4: 4
} as const;

export type SettingDataType = typeof SettingDataType[keyof typeof SettingDataType];


/**
 * 
 * @export
 * @interface SettingsValueModel
 */
export interface SettingsValueModel {
    /**
     * 
     * @type {string}
     * @memberof SettingsValueModel
     */
    'settingName': string;
    /**
     * 
     * @type {string}
     * @memberof SettingsValueModel
     */
    'category': string;
    /**
     * 
     * @type {string}
     * @memberof SettingsValueModel
     */
    'displayName': string;
    /**
     * 
     * @type {string}
     * @memberof SettingsValueModel
     */
    'description': string;
    /**
     * 
     * @type {SettingDataType}
     * @memberof SettingsValueModel
     */
    'dataType': SettingDataType;
    /**
     * 
     * @type {any}
     * @memberof SettingsValueModel
     */
    'value': any;
    /**
     * 
     * @type {Array<string>}
     * @memberof SettingsValueModel
     */
    'dataTypeOptions'?: Array<string> | null;
    /**
     * 
     * @type {boolean}
     * @memberof SettingsValueModel
     */
    'isKeyVault': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof SettingsValueModel
     */
    'isMultiple': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof SettingsValueModel
     */
    'isPublic': boolean;
}


/**
 * 
 * @export
 * @interface SettingsValueModelArrayCommandResponse
 */
export interface SettingsValueModelArrayCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof SettingsValueModelArrayCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof SettingsValueModelArrayCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof SettingsValueModelArrayCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof SettingsValueModelArrayCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SettingsValueModelArrayCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {Array<SettingsValueModel>}
     * @memberof SettingsValueModelArrayCommandResponse
     */
    'data'?: Array<SettingsValueModel> | null;
}


/**
 * 
 * @export
 * @interface SetupAppBody
 */
export interface SetupAppBody {
    /**
     * 
     * @type {string}
     * @memberof SetupAppBody
     */
    'appId': string;
    /**
     * 
     * @type {boolean}
     * @memberof SetupAppBody
     */
    'enabled': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof SetupAppBody
     */
    'deleted': boolean;
}
/**
 * 
 * @export
 * @interface SetupThemeBody
 */
export interface SetupThemeBody {
    /**
     * 
     * @type {OrgColour}
     * @memberof SetupThemeBody
     */
    'defaultColours'?: OrgColour;
    /**
     * 
     * @type {OrgColour}
     * @memberof SetupThemeBody
     */
    'darkColours'?: OrgColour;
}
/**
 * 
 * @export
 * @interface SpecificAppUserModel
 */
export interface SpecificAppUserModel {
    /**
     * 
     * @type {string}
     * @memberof SpecificAppUserModel
     */
    'appCodeName': string;
    /**
     * 
     * @type {string}
     * @memberof SpecificAppUserModel
     */
    'organisationCodeName': string;
    /**
     * 
     * @type {string}
     * @memberof SpecificAppUserModel
     */
    'userId': string;
    /**
     * 
     * @type {string}
     * @memberof SpecificAppUserModel
     */
    'userName': string;
    /**
     * 
     * @type {string}
     * @memberof SpecificAppUserModel
     */
    'email': string;
    /**
     * 
     * @type {AppUserRoleModel}
     * @memberof SpecificAppUserModel
     */
    'role': AppUserRoleModel;
    /**
     * 
     * @type {Array<AppUserGroupModel>}
     * @memberof SpecificAppUserModel
     */
    'groups': Array<AppUserGroupModel>;
    /**
     * 
     * @type {Array<ScopeResources>}
     * @memberof SpecificAppUserModel
     */
    'scopes': Array<ScopeResources>;
}
/**
 * 
 * @export
 * @interface SpecificAppUserModelCommandResponse
 */
export interface SpecificAppUserModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof SpecificAppUserModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof SpecificAppUserModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof SpecificAppUserModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof SpecificAppUserModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SpecificAppUserModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {SpecificAppUserModel}
     * @memberof SpecificAppUserModelCommandResponse
     */
    'data'?: SpecificAppUserModel;
}


/**
 * 
 * @export
 * @interface StringIEnumerableCommandResponse
 */
export interface StringIEnumerableCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof StringIEnumerableCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof StringIEnumerableCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof StringIEnumerableCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof StringIEnumerableCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof StringIEnumerableCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof StringIEnumerableCommandResponse
     */
    'data'?: Array<string> | null;
}


/**
 * 
 * @export
 * @interface ThemeConfigModel
 */
export interface ThemeConfigModel {
    /**
     * 
     * @type {string}
     * @memberof ThemeConfigModel
     */
    'avatarUrl'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ThemeConfigModel
     */
    'headerUrl'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ThemeConfigModel
     */
    'stylesheetUrl'?: string | null;
    /**
     * 
     * @type {OrgColourModel}
     * @memberof ThemeConfigModel
     */
    'defaultColours'?: OrgColourModel;
    /**
     * 
     * @type {OrgColourModel}
     * @memberof ThemeConfigModel
     */
    'darkColours'?: OrgColourModel;
}
/**
 * 
 * @export
 * @interface UnassignedGroupModel
 */
export interface UnassignedGroupModel {
    /**
     * 
     * @type {string}
     * @memberof UnassignedGroupModel
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof UnassignedGroupModel
     */
    'organisationCodeName': string;
    /**
     * 
     * @type {string}
     * @memberof UnassignedGroupModel
     */
    'groupName': string;
}
/**
 * 
 * @export
 * @interface UnassignedGroupModelApiListResult
 */
export interface UnassignedGroupModelApiListResult {
    /**
     * 
     * @type {Array<UnassignedGroupModel>}
     * @memberof UnassignedGroupModelApiListResult
     */
    'data'?: Array<UnassignedGroupModel> | null;
}
/**
 * 
 * @export
 * @interface UnassignedGroupModelApiListResultCommandResponse
 */
export interface UnassignedGroupModelApiListResultCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof UnassignedGroupModelApiListResultCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof UnassignedGroupModelApiListResultCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof UnassignedGroupModelApiListResultCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof UnassignedGroupModelApiListResultCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UnassignedGroupModelApiListResultCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {UnassignedGroupModelApiListResult}
     * @memberof UnassignedGroupModelApiListResultCommandResponse
     */
    'data'?: UnassignedGroupModelApiListResult;
}


/**
 * 
 * @export
 * @interface UpdateAppConfigBody
 */
export interface UpdateAppConfigBody {
    /**
     * 
     * @type {string}
     * @memberof UpdateAppConfigBody
     */
    'displayName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateAppConfigBody
     */
    'description'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof UpdateAppConfigBody
     */
    'isDefault'?: boolean;
}
/**
 * 
 * @export
 * @interface UpdateAppGroupBody
 */
export interface UpdateAppGroupBody {
    /**
     * 
     * @type {string}
     * @memberof UpdateAppGroupBody
     */
    'roleId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateAppGroupBody
     */
    'roleName'?: string | null;
    /**
     * 
     * @type {Array<ScopeResourcesBody>}
     * @memberof UpdateAppGroupBody
     */
    'scopes'?: Array<ScopeResourcesBody> | null;
}
/**
 * 
 * @export
 * @interface UpdateAppUserModel
 */
export interface UpdateAppUserModel {
    /**
     * 
     * @type {string}
     * @memberof UpdateAppUserModel
     */
    'roleId'?: string | null;
    /**
     * 
     * @type {Array<ScopeResourcesBody>}
     * @memberof UpdateAppUserModel
     */
    'scopes'?: Array<ScopeResourcesBody> | null;
}
/**
 * 
 * @export
 * @interface UpdateOrganisationBody
 */
export interface UpdateOrganisationBody {
    /**
     * 
     * @type {string}
     * @memberof UpdateOrganisationBody
     */
    'displayName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateOrganisationBody
     */
    'description'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateOrganisationBody
     */
    'icon'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateOrganisationBody
     */
    'accessLevel'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof UpdateOrganisationBody
     */
    'isDeleted'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof UpdateOrganisationBody
     */
    'isEnabled'?: boolean;
    /**
     * 
     * @type {SetupThemeBody}
     * @memberof UpdateOrganisationBody
     */
    'theme'?: SetupThemeBody;
    /**
     * 
     * @type {string}
     * @memberof UpdateOrganisationBody
     */
    'parentOrganisationId'?: string | null;
}
/**
 * 
 * @export
 * @interface UpdateUserInOrganisationCommandRequestBody
 */
export interface UpdateUserInOrganisationCommandRequestBody {
    /**
     * 
     * @type {string}
     * @memberof UpdateUserInOrganisationCommandRequestBody
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof UpdateUserInOrganisationCommandRequestBody
     */
    'email': string;
    /**
     * 
     * @type {string}
     * @memberof UpdateUserInOrganisationCommandRequestBody
     */
    'status': string;
    /**
     * 
     * @type {Array<string>}
     * @memberof UpdateUserInOrganisationCommandRequestBody
     */
    'groups'?: Array<string> | null;
    /**
     * 
     * @type {Array<GroupOrganisation>}
     * @memberof UpdateUserInOrganisationCommandRequestBody
     */
    'childOrganisations'?: Array<GroupOrganisation> | null;
}
/**
 * 
 * @export
 * @interface UserGroupScope
 */
export interface UserGroupScope {
    /**
     * 
     * @type {string}
     * @memberof UserGroupScope
     */
    'scope'?: string | null;
}
/**
 * 
 * @export
 * @interface UserGroupScopeBody
 */
export interface UserGroupScopeBody {
    /**
     * 
     * @type {string}
     * @memberof UserGroupScopeBody
     */
    'scope'?: string | null;
}
/**
 * 
 * @export
 * @interface UserIdentityProviderModel
 */
export interface UserIdentityProviderModel {
    /**
     * 
     * @type {string}
     * @memberof UserIdentityProviderModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserIdentityProviderModel
     */
    'type'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof UserIdentityProviderModel
     */
    'autoLogoutTime'?: number | null;
    /**
     * 
     * @type {number}
     * @memberof UserIdentityProviderModel
     */
    'autoLogoutWarningTime'?: number | null;
}
/**
 * 
 * @export
 * @interface UserIdentityProviderModelCommandResponse
 */
export interface UserIdentityProviderModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof UserIdentityProviderModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof UserIdentityProviderModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof UserIdentityProviderModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof UserIdentityProviderModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserIdentityProviderModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {UserIdentityProviderModel}
     * @memberof UserIdentityProviderModelCommandResponse
     */
    'data'?: UserIdentityProviderModel;
}


/**
 * 
 * @export
 * @interface UserModel
 */
export interface UserModel {
    /**
     * 
     * @type {string}
     * @memberof UserModel
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserModel
     */
    'email'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserModel
     */
    'userName'?: string | null;
}
/**
 * 
 * @export
 * @interface UserModelApiPagedListResult
 */
export interface UserModelApiPagedListResult {
    /**
     * 
     * @type {Array<UserModel>}
     * @memberof UserModelApiPagedListResult
     */
    'data'?: Array<UserModel> | null;
    /**
     * 
     * @type {UserModelPagingInfo}
     * @memberof UserModelApiPagedListResult
     */
    'paging'?: UserModelPagingInfo;
}
/**
 * 
 * @export
 * @interface UserModelApiPagedListResultCommandResponse
 */
export interface UserModelApiPagedListResultCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof UserModelApiPagedListResultCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof UserModelApiPagedListResultCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof UserModelApiPagedListResultCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof UserModelApiPagedListResultCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserModelApiPagedListResultCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {UserModelApiPagedListResult}
     * @memberof UserModelApiPagedListResultCommandResponse
     */
    'data'?: UserModelApiPagedListResult;
}


/**
 * 
 * @export
 * @interface UserModelCommandResponse
 */
export interface UserModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof UserModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof UserModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof UserModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof UserModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {UserModel}
     * @memberof UserModelCommandResponse
     */
    'data'?: UserModel;
}


/**
 * 
 * @export
 * @interface UserModelPagingInfo
 */
export interface UserModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof UserModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof UserModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof UserModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface UserPreferenceBody
 */
export interface UserPreferenceBody {
    /**
     * 
     * @type {{ [key: string]: string | null; }}
     * @memberof UserPreferenceBody
     */
    'userPreferences'?: { [key: string]: string | null; } | null;
}
/**
 * 
 * @export
 * @interface UserPreferencesModel
 */
export interface UserPreferencesModel {
    /**
     * 
     * @type {{ [key: string]: string | null; }}
     * @memberof UserPreferencesModel
     */
    'userPreferences': { [key: string]: string | null; };
}
/**
 * 
 * @export
 * @interface UserPreferencesModelCommandResponse
 */
export interface UserPreferencesModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof UserPreferencesModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof UserPreferencesModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof UserPreferencesModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof UserPreferencesModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserPreferencesModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {UserPreferencesModel}
     * @memberof UserPreferencesModelCommandResponse
     */
    'data'?: UserPreferencesModel;
}


/**
 * 
 * @export
 * @interface ValidScopeBody
 */
export interface ValidScopeBody {
    /**
     * 
     * @type {string}
     * @memberof ValidScopeBody
     */
    'scope'?: string | null;
    /**
     * 
     * @type {ScopeType}
     * @memberof ValidScopeBody
     */
    'scopeType'?: ScopeType;
}


/**
 * 
 * @export
 * @interface ValidateCurrentUserPermissionsAccessQuery
 */
export interface ValidateCurrentUserPermissionsAccessQuery {
    /**
     * 
     * @type {Array<string>}
     * @memberof ValidateCurrentUserPermissionsAccessQuery
     */
    'permissions'?: Array<string> | null;
    /**
     * 
     * @type {string}
     * @memberof ValidateCurrentUserPermissionsAccessQuery
     */
    'appCodeName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ValidateCurrentUserPermissionsAccessQuery
     */
    'orgCodeName'?: string | null;
}
/**
 * 
 * @export
 * @interface ValidateCurrentUserPermissionsAccessQueryResponse
 */
export interface ValidateCurrentUserPermissionsAccessQueryResponse {
    /**
     * 
     * @type {boolean}
     * @memberof ValidateCurrentUserPermissionsAccessQueryResponse
     */
    'anyAccess': boolean;
    /**
     * 
     * @type {boolean}
     * @memberof ValidateCurrentUserPermissionsAccessQueryResponse
     */
    'allAccess': boolean;
    /**
     * 
     * @type {{ [key: string]: boolean; }}
     * @memberof ValidateCurrentUserPermissionsAccessQueryResponse
     */
    'permissions': { [key: string]: boolean; };
}
/**
 * 
 * @export
 * @interface ValidateCurrentUserPermissionsAccessQueryResponseCommandResponse
 */
export interface ValidateCurrentUserPermissionsAccessQueryResponseCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof ValidateCurrentUserPermissionsAccessQueryResponseCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof ValidateCurrentUserPermissionsAccessQueryResponseCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof ValidateCurrentUserPermissionsAccessQueryResponseCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof ValidateCurrentUserPermissionsAccessQueryResponseCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ValidateCurrentUserPermissionsAccessQueryResponseCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {ValidateCurrentUserPermissionsAccessQueryResponse}
     * @memberof ValidateCurrentUserPermissionsAccessQueryResponseCommandResponse
     */
    'data'?: ValidateCurrentUserPermissionsAccessQueryResponse;
}


/**
 * 
 * @export
 * @interface ValidationResultModel
 */
export interface ValidationResultModel {
    /**
     * 
     * @type {boolean}
     * @memberof ValidationResultModel
     */
    'isValid'?: boolean;
}
/**
 * 
 * @export
 * @interface ValidationResultModelCommandResponse
 */
export interface ValidationResultModelCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof ValidationResultModelCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof ValidationResultModelCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof ValidationResultModelCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof ValidationResultModelCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ValidationResultModelCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {ValidationResultModel}
     * @memberof ValidationResultModelCommandResponse
     */
    'data'?: ValidationResultModel;
}


/**
 * 
 * @export
 * @interface WorkspaceDetails
 */
export interface WorkspaceDetails {
    /**
     * 
     * @type {string}
     * @memberof WorkspaceDetails
     */
    'appWorkspaceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof WorkspaceDetails
     */
    'tenantWorkspaceId'?: string | null;
    /**
     * 
     * @type {Array<Dashboard>}
     * @memberof WorkspaceDetails
     */
    'appDashboards'?: Array<Dashboard> | null;
    /**
     * 
     * @type {Array<Dashboard>}
     * @memberof WorkspaceDetails
     */
    'tenantDashboards'?: Array<Dashboard> | null;
}
/**
 * 
 * @export
 * @interface WorkspaceDetailsCommandResponse
 */
export interface WorkspaceDetailsCommandResponse {
    /**
     * 
     * @type {string}
     * @memberof WorkspaceDetailsCommandResponse
     */
    'title'?: string | null;
    /**
     * 
     * @type {HttpStatusCode}
     * @memberof WorkspaceDetailsCommandResponse
     */
    'status'?: HttpStatusCode;
    /**
     * 
     * @type {{ [key: string]: Array<string> | null; }}
     * @memberof WorkspaceDetailsCommandResponse
     */
    'errors'?: { [key: string]: Array<string> | null; } | null;
    /**
     * 
     * @type {string}
     * @memberof WorkspaceDetailsCommandResponse
     */
    'traceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof WorkspaceDetailsCommandResponse
     */
    'type'?: string | null;
    /**
     * 
     * @type {WorkspaceDetails}
     * @memberof WorkspaceDetailsCommandResponse
     */
    'data'?: WorkspaceDetails;
}



/**
 * AppApi - axios parameter creator
 * @export
 */
export const AppApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {AppBody} [appBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createApp: async (appBody?: AppBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/App`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(appBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteApp: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteApp', 'id', id)
            const localVarPath = `/v1/App/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminAppList: async (filter?: string, orderBy?: string, offset?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/App/admin`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getApp: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getApp', 'id', id)
            const localVarPath = `/v1/App/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppList: async (filter?: string, orderBy?: string, offset?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/App`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppsByOrganisation: async (id: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getAppsByOrganisation', 'id', id)
            const localVarPath = `/v1/App/Organisation/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {AppBody} [appBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateApp: async (id: string, appBody?: AppBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateApp', 'id', id)
            const localVarPath = `/v1/App/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(appBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAppAvatar: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateAppAvatar', 'id', id)
            const localVarPath = `/v1/App/{id}/avatar`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {UpdateAppConfigBody} [updateAppConfigBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAppConfig: async (id: string, updateAppConfigBody?: UpdateAppConfigBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateAppConfig', 'id', id)
            const localVarPath = `/v1/App/{id}/Config`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateAppConfigBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateApplication: async (name?: string, codeName?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/App/validate`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['Name'] = name;
            }

            if (codeName !== undefined) {
                localVarQueryParameter['CodeName'] = codeName;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateApplicationById: async (id: string, name?: string, codeName?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('validateApplicationById', 'id', id)
            const localVarPath = `/v1/App/{id}/validate`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['Name'] = name;
            }

            if (codeName !== undefined) {
                localVarQueryParameter['CodeName'] = codeName;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AppApi - functional programming interface
 * @export
 */
export const AppApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AppApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {AppBody} [appBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createApp(appBody?: AppBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createApp(appBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppApi.createApp']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteApp(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteApp(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppApi.deleteApp']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminAppList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AppModelIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminAppList(filter, orderBy, offset, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppApi.getAdminAppList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getApp(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AppModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getApp(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppApi.getApp']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAppList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AppModelIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAppList(filter, orderBy, offset, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppApi.getAppList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAppsByOrganisation(id: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AppOrganisationModelIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAppsByOrganisation(id, filter, orderBy, offset, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppApi.getAppsByOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {AppBody} [appBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateApp(id: string, appBody?: AppBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateApp(id, appBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppApi.updateApp']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateAppAvatar(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateAppAvatar(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppApi.updateAppAvatar']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {UpdateAppConfigBody} [updateAppConfigBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateAppConfig(id: string, updateAppConfigBody?: UpdateAppConfigBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateAppConfig(id, updateAppConfigBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppApi.updateAppConfig']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async validateApplication(name?: string, codeName?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ValidationResultModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.validateApplication(name, codeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppApi.validateApplication']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async validateApplicationById(id: string, name?: string, codeName?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ValidationResultModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.validateApplicationById(id, name, codeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppApi.validateApplicationById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AppApi - factory interface
 * @export
 */
export const AppApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AppApiFp(configuration)
    return {
        /**
         * 
         * @param {AppBody} [appBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createApp(appBody?: AppBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.createApp(appBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteApp(id: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteApp(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminAppList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: any): AxiosPromise<AppModelIEnumerableCommandResponse> {
            return localVarFp.getAdminAppList(filter, orderBy, offset, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getApp(id: string, options?: any): AxiosPromise<AppModelCommandResponse> {
            return localVarFp.getApp(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: any): AxiosPromise<AppModelIEnumerableCommandResponse> {
            return localVarFp.getAppList(filter, orderBy, offset, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppsByOrganisation(id: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: any): AxiosPromise<AppOrganisationModelIEnumerableCommandResponse> {
            return localVarFp.getAppsByOrganisation(id, filter, orderBy, offset, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {AppBody} [appBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateApp(id: string, appBody?: AppBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateApp(id, appBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAppAvatar(id: string, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateAppAvatar(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {UpdateAppConfigBody} [updateAppConfigBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAppConfig(id: string, updateAppConfigBody?: UpdateAppConfigBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateAppConfig(id, updateAppConfigBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateApplication(name?: string, codeName?: string, options?: any): AxiosPromise<ValidationResultModelCommandResponse> {
            return localVarFp.validateApplication(name, codeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateApplicationById(id: string, name?: string, codeName?: string, options?: any): AxiosPromise<ValidationResultModelCommandResponse> {
            return localVarFp.validateApplicationById(id, name, codeName, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AppApi - object-oriented interface
 * @export
 * @class AppApi
 * @extends {BaseAPI}
 */
export class AppApi extends BaseAPI {
    /**
     * 
     * @param {AppBody} [appBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppApi
     */
    public createApp(appBody?: AppBody, options?: RawAxiosRequestConfig) {
        return AppApiFp(this.configuration).createApp(appBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppApi
     */
    public deleteApp(id: string, options?: RawAxiosRequestConfig) {
        return AppApiFp(this.configuration).deleteApp(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppApi
     */
    public getAdminAppList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return AppApiFp(this.configuration).getAdminAppList(filter, orderBy, offset, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppApi
     */
    public getApp(id: string, options?: RawAxiosRequestConfig) {
        return AppApiFp(this.configuration).getApp(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppApi
     */
    public getAppList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return AppApiFp(this.configuration).getAppList(filter, orderBy, offset, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppApi
     */
    public getAppsByOrganisation(id: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return AppApiFp(this.configuration).getAppsByOrganisation(id, filter, orderBy, offset, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {AppBody} [appBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppApi
     */
    public updateApp(id: string, appBody?: AppBody, options?: RawAxiosRequestConfig) {
        return AppApiFp(this.configuration).updateApp(id, appBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppApi
     */
    public updateAppAvatar(id: string, options?: RawAxiosRequestConfig) {
        return AppApiFp(this.configuration).updateAppAvatar(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {UpdateAppConfigBody} [updateAppConfigBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppApi
     */
    public updateAppConfig(id: string, updateAppConfigBody?: UpdateAppConfigBody, options?: RawAxiosRequestConfig) {
        return AppApiFp(this.configuration).updateAppConfig(id, updateAppConfigBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [name] 
     * @param {string} [codeName] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppApi
     */
    public validateApplication(name?: string, codeName?: string, options?: RawAxiosRequestConfig) {
        return AppApiFp(this.configuration).validateApplication(name, codeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {string} [name] 
     * @param {string} [codeName] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppApi
     */
    public validateApplicationById(id: string, name?: string, codeName?: string, options?: RawAxiosRequestConfig) {
        return AppApiFp(this.configuration).validateApplicationById(id, name, codeName, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * AppGroupApi - axios parameter creator
 * @export
 */
export const AppGroupApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {CreateAppGroupBody} [createAppGroupBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAppGroup: async (orgCodeName: string, appCodeName: string, createAppGroupBody?: CreateAppGroupBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('createAppGroup', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('createAppGroup', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/group`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createAppGroupBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAppGroup: async (id: string, orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteAppGroup', 'id', id)
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('deleteAppGroup', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('deleteAppGroup', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/group/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppGroupById: async (orgCodeName: string, appCodeName: string, id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getAppGroupById', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getAppGroupById', 'appCodeName', appCodeName)
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getAppGroupById', 'id', id)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/group/{id}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)))
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFilteredAppGroups: async (orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getFilteredAppGroups', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getFilteredAppGroups', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/group`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUnassignedGroups: async (orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getUnassignedGroups', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getUnassignedGroups', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/group/unassigned`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {UpdateAppGroupBody} [updateAppGroupBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAppGroup: async (id: string, orgCodeName: string, appCodeName: string, updateAppGroupBody?: UpdateAppGroupBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateAppGroup', 'id', id)
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('updateAppGroup', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('updateAppGroup', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/group/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateAppGroupBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AppGroupApi - functional programming interface
 * @export
 */
export const AppGroupApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AppGroupApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {CreateAppGroupBody} [createAppGroupBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createAppGroup(orgCodeName: string, appCodeName: string, createAppGroupBody?: CreateAppGroupBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createAppGroup(orgCodeName, appCodeName, createAppGroupBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppGroupApi.createAppGroup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteAppGroup(id: string, orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteAppGroup(id, orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppGroupApi.deleteAppGroup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAppGroupById(orgCodeName: string, appCodeName: string, id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AppGroupModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAppGroupById(orgCodeName, appCodeName, id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppGroupApi.getAppGroupById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getFilteredAppGroups(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AppGroupModelApiPagedListResultCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getFilteredAppGroups(orgCodeName, appCodeName, filter, orderBy, offset, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppGroupApi.getFilteredAppGroups']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getUnassignedGroups(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UnassignedGroupModelApiListResultCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getUnassignedGroups(orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppGroupApi.getUnassignedGroups']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {UpdateAppGroupBody} [updateAppGroupBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateAppGroup(id: string, orgCodeName: string, appCodeName: string, updateAppGroupBody?: UpdateAppGroupBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateAppGroup(id, orgCodeName, appCodeName, updateAppGroupBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppGroupApi.updateAppGroup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AppGroupApi - factory interface
 * @export
 */
export const AppGroupApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AppGroupApiFp(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {CreateAppGroupBody} [createAppGroupBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAppGroup(orgCodeName: string, appCodeName: string, createAppGroupBody?: CreateAppGroupBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.createAppGroup(orgCodeName, appCodeName, createAppGroupBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAppGroup(id: string, orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteAppGroup(id, orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppGroupById(orgCodeName: string, appCodeName: string, id: string, options?: any): AxiosPromise<AppGroupModelCommandResponse> {
            return localVarFp.getAppGroupById(orgCodeName, appCodeName, id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFilteredAppGroups(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: any): AxiosPromise<AppGroupModelApiPagedListResultCommandResponse> {
            return localVarFp.getFilteredAppGroups(orgCodeName, appCodeName, filter, orderBy, offset, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUnassignedGroups(orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<UnassignedGroupModelApiListResultCommandResponse> {
            return localVarFp.getUnassignedGroups(orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {UpdateAppGroupBody} [updateAppGroupBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAppGroup(id: string, orgCodeName: string, appCodeName: string, updateAppGroupBody?: UpdateAppGroupBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateAppGroup(id, orgCodeName, appCodeName, updateAppGroupBody, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AppGroupApi - object-oriented interface
 * @export
 * @class AppGroupApi
 * @extends {BaseAPI}
 */
export class AppGroupApi extends BaseAPI {
    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {CreateAppGroupBody} [createAppGroupBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppGroupApi
     */
    public createAppGroup(orgCodeName: string, appCodeName: string, createAppGroupBody?: CreateAppGroupBody, options?: RawAxiosRequestConfig) {
        return AppGroupApiFp(this.configuration).createAppGroup(orgCodeName, appCodeName, createAppGroupBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppGroupApi
     */
    public deleteAppGroup(id: string, orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return AppGroupApiFp(this.configuration).deleteAppGroup(id, orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppGroupApi
     */
    public getAppGroupById(orgCodeName: string, appCodeName: string, id: string, options?: RawAxiosRequestConfig) {
        return AppGroupApiFp(this.configuration).getAppGroupById(orgCodeName, appCodeName, id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppGroupApi
     */
    public getFilteredAppGroups(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return AppGroupApiFp(this.configuration).getFilteredAppGroups(orgCodeName, appCodeName, filter, orderBy, offset, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppGroupApi
     */
    public getUnassignedGroups(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return AppGroupApiFp(this.configuration).getUnassignedGroups(orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {UpdateAppGroupBody} [updateAppGroupBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppGroupApi
     */
    public updateAppGroup(id: string, orgCodeName: string, appCodeName: string, updateAppGroupBody?: UpdateAppGroupBody, options?: RawAxiosRequestConfig) {
        return AppGroupApiFp(this.configuration).updateAppGroup(id, orgCodeName, appCodeName, updateAppGroupBody, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * AppOrganisationSettingsApi - axios parameter creator
 * @export
 */
export const AppOrganisationSettingsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationAppMasterData: async (orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getOrganisationAppMasterData', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getOrganisationAppMasterData', 'appCodeName', appCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/app/{appCodeName}/masterData`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationAppPublicSettings: async (orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getOrganisationAppPublicSettings', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getOrganisationAppPublicSettings', 'appCodeName', appCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/app/{appCodeName}/publicSettings`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationAppSettings: async (orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getOrganisationAppSettings', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getOrganisationAppSettings', 'appCodeName', appCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/app/{appCodeName}/settings`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} dataType 
         * @param {AppOrganisationMasterDataBody} [appOrganisationMasterDataBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrganisationAppMasterData: async (orgCodeName: string, appCodeName: string, dataType: string, appOrganisationMasterDataBody?: AppOrganisationMasterDataBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('updateOrganisationAppMasterData', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('updateOrganisationAppMasterData', 'appCodeName', appCodeName)
            // verify required parameter 'dataType' is not null or undefined
            assertParamExists('updateOrganisationAppMasterData', 'dataType', dataType)
            const localVarPath = `/v1/Organisation/{orgCodeName}/app/{appCodeName}/masterdata/{dataType}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)))
                .replace(`{${"dataType"}}`, encodeURIComponent(String(dataType)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(appOrganisationMasterDataBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} settingName 
         * @param {AppOrganisationSettingsBody} [appOrganisationSettingsBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrganisationAppSettings: async (orgCodeName: string, appCodeName: string, settingName: string, appOrganisationSettingsBody?: AppOrganisationSettingsBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('updateOrganisationAppSettings', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('updateOrganisationAppSettings', 'appCodeName', appCodeName)
            // verify required parameter 'settingName' is not null or undefined
            assertParamExists('updateOrganisationAppSettings', 'settingName', settingName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/app/{appCodeName}/settings/{settingName}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)))
                .replace(`{${"settingName"}}`, encodeURIComponent(String(settingName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(appOrganisationSettingsBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AppOrganisationSettingsApi - functional programming interface
 * @export
 */
export const AppOrganisationSettingsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AppOrganisationSettingsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrganisationAppMasterData(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<MasterDataValueModelIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrganisationAppMasterData(orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppOrganisationSettingsApi.getOrganisationAppMasterData']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrganisationAppPublicSettings(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SettingsValueModelArrayCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrganisationAppPublicSettings(orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppOrganisationSettingsApi.getOrganisationAppPublicSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrganisationAppSettings(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SettingsValueModelArrayCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrganisationAppSettings(orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppOrganisationSettingsApi.getOrganisationAppSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} dataType 
         * @param {AppOrganisationMasterDataBody} [appOrganisationMasterDataBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateOrganisationAppMasterData(orgCodeName: string, appCodeName: string, dataType: string, appOrganisationMasterDataBody?: AppOrganisationMasterDataBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateOrganisationAppMasterData(orgCodeName, appCodeName, dataType, appOrganisationMasterDataBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppOrganisationSettingsApi.updateOrganisationAppMasterData']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} settingName 
         * @param {AppOrganisationSettingsBody} [appOrganisationSettingsBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateOrganisationAppSettings(orgCodeName: string, appCodeName: string, settingName: string, appOrganisationSettingsBody?: AppOrganisationSettingsBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateOrganisationAppSettings(orgCodeName, appCodeName, settingName, appOrganisationSettingsBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppOrganisationSettingsApi.updateOrganisationAppSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AppOrganisationSettingsApi - factory interface
 * @export
 */
export const AppOrganisationSettingsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AppOrganisationSettingsApiFp(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationAppMasterData(orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<MasterDataValueModelIEnumerableCommandResponse> {
            return localVarFp.getOrganisationAppMasterData(orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationAppPublicSettings(orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<SettingsValueModelArrayCommandResponse> {
            return localVarFp.getOrganisationAppPublicSettings(orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationAppSettings(orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<SettingsValueModelArrayCommandResponse> {
            return localVarFp.getOrganisationAppSettings(orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} dataType 
         * @param {AppOrganisationMasterDataBody} [appOrganisationMasterDataBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrganisationAppMasterData(orgCodeName: string, appCodeName: string, dataType: string, appOrganisationMasterDataBody?: AppOrganisationMasterDataBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateOrganisationAppMasterData(orgCodeName, appCodeName, dataType, appOrganisationMasterDataBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} settingName 
         * @param {AppOrganisationSettingsBody} [appOrganisationSettingsBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrganisationAppSettings(orgCodeName: string, appCodeName: string, settingName: string, appOrganisationSettingsBody?: AppOrganisationSettingsBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateOrganisationAppSettings(orgCodeName, appCodeName, settingName, appOrganisationSettingsBody, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AppOrganisationSettingsApi - object-oriented interface
 * @export
 * @class AppOrganisationSettingsApi
 * @extends {BaseAPI}
 */
export class AppOrganisationSettingsApi extends BaseAPI {
    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppOrganisationSettingsApi
     */
    public getOrganisationAppMasterData(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return AppOrganisationSettingsApiFp(this.configuration).getOrganisationAppMasterData(orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppOrganisationSettingsApi
     */
    public getOrganisationAppPublicSettings(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return AppOrganisationSettingsApiFp(this.configuration).getOrganisationAppPublicSettings(orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppOrganisationSettingsApi
     */
    public getOrganisationAppSettings(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return AppOrganisationSettingsApiFp(this.configuration).getOrganisationAppSettings(orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {string} dataType 
     * @param {AppOrganisationMasterDataBody} [appOrganisationMasterDataBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppOrganisationSettingsApi
     */
    public updateOrganisationAppMasterData(orgCodeName: string, appCodeName: string, dataType: string, appOrganisationMasterDataBody?: AppOrganisationMasterDataBody, options?: RawAxiosRequestConfig) {
        return AppOrganisationSettingsApiFp(this.configuration).updateOrganisationAppMasterData(orgCodeName, appCodeName, dataType, appOrganisationMasterDataBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {string} settingName 
     * @param {AppOrganisationSettingsBody} [appOrganisationSettingsBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppOrganisationSettingsApi
     */
    public updateOrganisationAppSettings(orgCodeName: string, appCodeName: string, settingName: string, appOrganisationSettingsBody?: AppOrganisationSettingsBody, options?: RawAxiosRequestConfig) {
        return AppOrganisationSettingsApiFp(this.configuration).updateOrganisationAppSettings(orgCodeName, appCodeName, settingName, appOrganisationSettingsBody, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * AppUserApi - axios parameter creator
 * @export
 */
export const AppUserApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {string} emailAddress 
         * @param {CreateAppUserModel} [createAppUserModel] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAppUser: async (appCodeName: string, orgCodeName: string, emailAddress: string, createAppUserModel?: CreateAppUserModel, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('createAppUser', 'appCodeName', appCodeName)
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('createAppUser', 'orgCodeName', orgCodeName)
            // verify required parameter 'emailAddress' is not null or undefined
            assertParamExists('createAppUser', 'emailAddress', emailAddress)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}`
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)))
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"emailAddress"}}`, encodeURIComponent(String(emailAddress)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createAppUserModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {string} emailAddress 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAppUser: async (appCodeName: string, orgCodeName: string, emailAddress: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('deleteAppUser', 'appCodeName', appCodeName)
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('deleteAppUser', 'orgCodeName', orgCodeName)
            // verify required parameter 'emailAddress' is not null or undefined
            assertParamExists('deleteAppUser', 'emailAddress', emailAddress)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}`
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)))
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"emailAddress"}}`, encodeURIComponent(String(emailAddress)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} emailAddress 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppUser: async (orgCodeName: string, appCodeName: string, emailAddress: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getAppUser', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getAppUser', 'appCodeName', appCodeName)
            // verify required parameter 'emailAddress' is not null or undefined
            assertParamExists('getAppUser', 'emailAddress', emailAddress)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)))
                .replace(`{${"emailAddress"}}`, encodeURIComponent(String(emailAddress)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFilteredAppUsers: async (orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getFilteredAppUsers', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getFilteredAppUsers', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/user`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFilteredEligibleUsers: async (orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getFilteredEligibleUsers', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getFilteredEligibleUsers', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/user/eligible`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {string} emailAddress 
         * @param {UpdateAppUserModel} [updateAppUserModel] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAppUser: async (appCodeName: string, orgCodeName: string, emailAddress: string, updateAppUserModel?: UpdateAppUserModel, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('updateAppUser', 'appCodeName', appCodeName)
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('updateAppUser', 'orgCodeName', orgCodeName)
            // verify required parameter 'emailAddress' is not null or undefined
            assertParamExists('updateAppUser', 'emailAddress', emailAddress)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}`
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)))
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"emailAddress"}}`, encodeURIComponent(String(emailAddress)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateAppUserModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AppUserApi - functional programming interface
 * @export
 */
export const AppUserApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AppUserApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {string} emailAddress 
         * @param {CreateAppUserModel} [createAppUserModel] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createAppUser(appCodeName: string, orgCodeName: string, emailAddress: string, createAppUserModel?: CreateAppUserModel, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createAppUser(appCodeName, orgCodeName, emailAddress, createAppUserModel, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppUserApi.createAppUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {string} emailAddress 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteAppUser(appCodeName: string, orgCodeName: string, emailAddress: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteAppUser(appCodeName, orgCodeName, emailAddress, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppUserApi.deleteAppUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} emailAddress 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAppUser(orgCodeName: string, appCodeName: string, emailAddress: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SpecificAppUserModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAppUser(orgCodeName, appCodeName, emailAddress, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppUserApi.getAppUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getFilteredAppUsers(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AppUserModelApiPagedListResultCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getFilteredAppUsers(orgCodeName, appCodeName, filter, orderBy, offset, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppUserApi.getFilteredAppUsers']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getFilteredEligibleUsers(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserModelApiPagedListResultCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getFilteredEligibleUsers(orgCodeName, appCodeName, filter, orderBy, offset, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppUserApi.getFilteredEligibleUsers']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {string} emailAddress 
         * @param {UpdateAppUserModel} [updateAppUserModel] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateAppUser(appCodeName: string, orgCodeName: string, emailAddress: string, updateAppUserModel?: UpdateAppUserModel, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateAppUser(appCodeName, orgCodeName, emailAddress, updateAppUserModel, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AppUserApi.updateAppUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AppUserApi - factory interface
 * @export
 */
export const AppUserApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AppUserApiFp(configuration)
    return {
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {string} emailAddress 
         * @param {CreateAppUserModel} [createAppUserModel] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAppUser(appCodeName: string, orgCodeName: string, emailAddress: string, createAppUserModel?: CreateAppUserModel, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.createAppUser(appCodeName, orgCodeName, emailAddress, createAppUserModel, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {string} emailAddress 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAppUser(appCodeName: string, orgCodeName: string, emailAddress: string, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.deleteAppUser(appCodeName, orgCodeName, emailAddress, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} emailAddress 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppUser(orgCodeName: string, appCodeName: string, emailAddress: string, options?: any): AxiosPromise<SpecificAppUserModelCommandResponse> {
            return localVarFp.getAppUser(orgCodeName, appCodeName, emailAddress, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFilteredAppUsers(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: any): AxiosPromise<AppUserModelApiPagedListResultCommandResponse> {
            return localVarFp.getFilteredAppUsers(orgCodeName, appCodeName, filter, orderBy, offset, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFilteredEligibleUsers(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: any): AxiosPromise<UserModelApiPagedListResultCommandResponse> {
            return localVarFp.getFilteredEligibleUsers(orgCodeName, appCodeName, filter, orderBy, offset, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {string} emailAddress 
         * @param {UpdateAppUserModel} [updateAppUserModel] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAppUser(appCodeName: string, orgCodeName: string, emailAddress: string, updateAppUserModel?: UpdateAppUserModel, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateAppUser(appCodeName, orgCodeName, emailAddress, updateAppUserModel, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AppUserApi - object-oriented interface
 * @export
 * @class AppUserApi
 * @extends {BaseAPI}
 */
export class AppUserApi extends BaseAPI {
    /**
     * 
     * @param {string} appCodeName 
     * @param {string} orgCodeName 
     * @param {string} emailAddress 
     * @param {CreateAppUserModel} [createAppUserModel] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppUserApi
     */
    public createAppUser(appCodeName: string, orgCodeName: string, emailAddress: string, createAppUserModel?: CreateAppUserModel, options?: RawAxiosRequestConfig) {
        return AppUserApiFp(this.configuration).createAppUser(appCodeName, orgCodeName, emailAddress, createAppUserModel, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} appCodeName 
     * @param {string} orgCodeName 
     * @param {string} emailAddress 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppUserApi
     */
    public deleteAppUser(appCodeName: string, orgCodeName: string, emailAddress: string, options?: RawAxiosRequestConfig) {
        return AppUserApiFp(this.configuration).deleteAppUser(appCodeName, orgCodeName, emailAddress, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {string} emailAddress 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppUserApi
     */
    public getAppUser(orgCodeName: string, appCodeName: string, emailAddress: string, options?: RawAxiosRequestConfig) {
        return AppUserApiFp(this.configuration).getAppUser(orgCodeName, appCodeName, emailAddress, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppUserApi
     */
    public getFilteredAppUsers(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return AppUserApiFp(this.configuration).getFilteredAppUsers(orgCodeName, appCodeName, filter, orderBy, offset, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppUserApi
     */
    public getFilteredEligibleUsers(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return AppUserApiFp(this.configuration).getFilteredEligibleUsers(orgCodeName, appCodeName, filter, orderBy, offset, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} appCodeName 
     * @param {string} orgCodeName 
     * @param {string} emailAddress 
     * @param {UpdateAppUserModel} [updateAppUserModel] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AppUserApi
     */
    public updateAppUser(appCodeName: string, orgCodeName: string, emailAddress: string, updateAppUserModel?: UpdateAppUserModel, options?: RawAxiosRequestConfig) {
        return AppUserApiFp(this.configuration).updateAppUser(appCodeName, orgCodeName, emailAddress, updateAppUserModel, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * AuditApi - axios parameter creator
 * @export
 */
export const AuditApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAuditFilters: async (orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getAuditFilters', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getAuditFilters', 'appCodeName', appCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/app/{appCodeName}/audit/filters`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAuditFiltersUrl: async (orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getAuditFiltersUrl', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getAuditFiltersUrl', 'appCodeName', appCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/app/{appCodeName}/audit/url/filters`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAudits: async (orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getAudits', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getAudits', 'appCodeName', appCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/app/{appCodeName}/audit`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAuditsUrl: async (orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getAuditsUrl', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getAuditsUrl', 'appCodeName', appCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/app/{appCodeName}/audit/url`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AuditApi - functional programming interface
 * @export
 */
export const AuditApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AuditApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAuditFilters(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<StringIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAuditFilters(orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuditApi.getAuditFilters']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAuditFiltersUrl(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<StringIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAuditFiltersUrl(orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuditApi.getAuditFiltersUrl']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAudits(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<StringIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAudits(orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuditApi.getAudits']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAuditsUrl(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<StringIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAuditsUrl(orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuditApi.getAuditsUrl']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AuditApi - factory interface
 * @export
 */
export const AuditApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AuditApiFp(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAuditFilters(orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<StringIEnumerableCommandResponse> {
            return localVarFp.getAuditFilters(orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAuditFiltersUrl(orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<StringIEnumerableCommandResponse> {
            return localVarFp.getAuditFiltersUrl(orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAudits(orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<StringIEnumerableCommandResponse> {
            return localVarFp.getAudits(orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAuditsUrl(orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<StringIEnumerableCommandResponse> {
            return localVarFp.getAuditsUrl(orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AuditApi - object-oriented interface
 * @export
 * @class AuditApi
 * @extends {BaseAPI}
 */
export class AuditApi extends BaseAPI {
    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuditApi
     */
    public getAuditFilters(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return AuditApiFp(this.configuration).getAuditFilters(orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuditApi
     */
    public getAuditFiltersUrl(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return AuditApiFp(this.configuration).getAuditFiltersUrl(orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuditApi
     */
    public getAudits(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return AuditApiFp(this.configuration).getAudits(orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuditApi
     */
    public getAuditsUrl(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return AuditApiFp(this.configuration).getAuditsUrl(orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * AuthenticationApi - axios parameter creator
 * @export
 */
export const AuthenticationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getGoodDataUserToken: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/Authentication/GoodData/token`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [accessToken] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        signIn: async (accessToken?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/Authentication/SignIn`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (accessToken !== undefined) {
                localVarQueryParameter['accessToken'] = accessToken;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {boolean} [autoLogout] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        signOut: async (autoLogout?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/Authentication/SignOut`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (autoLogout !== undefined) {
                localVarQueryParameter['autoLogout'] = autoLogout;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AuthenticationApi - functional programming interface
 * @export
 */
export const AuthenticationApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AuthenticationApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getGoodDataUserToken(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getGoodDataUserToken(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuthenticationApi.getGoodDataUserToken']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [accessToken] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async signIn(accessToken?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.signIn(accessToken, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuthenticationApi.signIn']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {boolean} [autoLogout] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async signOut(autoLogout?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.signOut(autoLogout, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuthenticationApi.signOut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AuthenticationApi - factory interface
 * @export
 */
export const AuthenticationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AuthenticationApiFp(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getGoodDataUserToken(options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.getGoodDataUserToken(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [accessToken] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        signIn(accessToken?: string, options?: any): AxiosPromise<UserModelCommandResponse> {
            return localVarFp.signIn(accessToken, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {boolean} [autoLogout] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        signOut(autoLogout?: boolean, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.signOut(autoLogout, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AuthenticationApi - object-oriented interface
 * @export
 * @class AuthenticationApi
 * @extends {BaseAPI}
 */
export class AuthenticationApi extends BaseAPI {
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuthenticationApi
     */
    public getGoodDataUserToken(options?: RawAxiosRequestConfig) {
        return AuthenticationApiFp(this.configuration).getGoodDataUserToken(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [accessToken] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuthenticationApi
     */
    public signIn(accessToken?: string, options?: RawAxiosRequestConfig) {
        return AuthenticationApiFp(this.configuration).signIn(accessToken, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {boolean} [autoLogout] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuthenticationApi
     */
    public signOut(autoLogout?: boolean, options?: RawAxiosRequestConfig) {
        return AuthenticationApiFp(this.configuration).signOut(autoLogout, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * GoodDataApi - axios parameter creator
 * @export
 */
export const GoodDataApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getWorkspaceWithDashboards: async (appCodeName: string, orgCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getWorkspaceWithDashboards', 'appCodeName', appCodeName)
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getWorkspaceWithDashboards', 'orgCodeName', orgCodeName)
            const localVarPath = `/v1/GoodData/app/{appCodeName}/organisation/{orgCodeName}`
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)))
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * GoodDataApi - functional programming interface
 * @export
 */
export const GoodDataApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = GoodDataApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getWorkspaceWithDashboards(appCodeName: string, orgCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<WorkspaceDetailsCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getWorkspaceWithDashboards(appCodeName, orgCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['GoodDataApi.getWorkspaceWithDashboards']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * GoodDataApi - factory interface
 * @export
 */
export const GoodDataApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = GoodDataApiFp(configuration)
    return {
        /**
         * 
         * @param {string} appCodeName 
         * @param {string} orgCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getWorkspaceWithDashboards(appCodeName: string, orgCodeName: string, options?: any): AxiosPromise<WorkspaceDetailsCommandResponse> {
            return localVarFp.getWorkspaceWithDashboards(appCodeName, orgCodeName, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * GoodDataApi - object-oriented interface
 * @export
 * @class GoodDataApi
 * @extends {BaseAPI}
 */
export class GoodDataApi extends BaseAPI {
    /**
     * 
     * @param {string} appCodeName 
     * @param {string} orgCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof GoodDataApi
     */
    public getWorkspaceWithDashboards(appCodeName: string, orgCodeName: string, options?: RawAxiosRequestConfig) {
        return GoodDataApiFp(this.configuration).getWorkspaceWithDashboards(appCodeName, orgCodeName, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * MasterDataApi - axios parameter creator
 * @export
 */
export const MasterDataApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} dataType 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllMasterDataForType: async (dataType: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'dataType' is not null or undefined
            assertParamExists('getAllMasterDataForType', 'dataType', dataType)
            const localVarPath = `/v1/MasterData/{dataType}`
                .replace(`{${"dataType"}}`, encodeURIComponent(String(dataType)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} dataType 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getMasterDataById: async (dataType: string, id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'dataType' is not null or undefined
            assertParamExists('getMasterDataById', 'dataType', dataType)
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getMasterDataById', 'id', id)
            const localVarPath = `/v1/MasterData/{dataType}/{id}`
                .replace(`{${"dataType"}}`, encodeURIComponent(String(dataType)))
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * MasterDataApi - functional programming interface
 * @export
 */
export const MasterDataApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = MasterDataApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} dataType 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllMasterDataForType(dataType: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<MasterDataModelIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllMasterDataForType(dataType, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MasterDataApi.getAllMasterDataForType']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} dataType 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getMasterDataById(dataType: string, id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<MasterDataModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getMasterDataById(dataType, id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['MasterDataApi.getMasterDataById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * MasterDataApi - factory interface
 * @export
 */
export const MasterDataApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = MasterDataApiFp(configuration)
    return {
        /**
         * 
         * @param {string} dataType 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllMasterDataForType(dataType: string, options?: any): AxiosPromise<MasterDataModelIEnumerableCommandResponse> {
            return localVarFp.getAllMasterDataForType(dataType, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} dataType 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getMasterDataById(dataType: string, id: string, options?: any): AxiosPromise<MasterDataModelCommandResponse> {
            return localVarFp.getMasterDataById(dataType, id, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * MasterDataApi - object-oriented interface
 * @export
 * @class MasterDataApi
 * @extends {BaseAPI}
 */
export class MasterDataApi extends BaseAPI {
    /**
     * 
     * @param {string} dataType 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterDataApi
     */
    public getAllMasterDataForType(dataType: string, options?: RawAxiosRequestConfig) {
        return MasterDataApiFp(this.configuration).getAllMasterDataForType(dataType, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} dataType 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MasterDataApi
     */
    public getMasterDataById(dataType: string, id: string, options?: RawAxiosRequestConfig) {
        return MasterDataApiFp(this.configuration).getMasterDataById(dataType, id, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * OrganisationApi - axios parameter creator
 * @export
 */
export const OrganisationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} id 
         * @param {Array<string>} [requestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addAppListToOrganisation: async (id: string, requestBody?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('addAppListToOrganisation', 'id', id)
            const localVarPath = `/v1/Organisation/{id}/App`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {string} appId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addAppToOrganisation: async (id: string, appId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('addAppToOrganisation', 'id', id)
            // verify required parameter 'appId' is not null or undefined
            assertParamExists('addAppToOrganisation', 'appId', appId)
            const localVarPath = `/v1/Organisation/{id}/App/{appId}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"appId"}}`, encodeURIComponent(String(appId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {AddOrganisationGroupCommandRequestBody} [addOrganisationGroupCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addOrganisationGroup: async (orgCodeName: string, addOrganisationGroupCommandRequestBody?: AddOrganisationGroupCommandRequestBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('addOrganisationGroup', 'orgCodeName', orgCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/groups`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(addOrganisationGroupCommandRequestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {AddOrganisationUserCommandRequestBody} [addOrganisationUserCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addUsersToOrganisation: async (orgCodeName: string, addOrganisationUserCommandRequestBody?: AddOrganisationUserCommandRequestBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('addUsersToOrganisation', 'orgCodeName', orgCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/users`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(addOrganisationUserCommandRequestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {AddOrganisationGroupUserCommandRequestBody} [addOrganisationGroupUserCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addUsersToOrganisationGroup: async (orgCodeName: string, groupId: string, addOrganisationGroupUserCommandRequestBody?: AddOrganisationGroupUserCommandRequestBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('addUsersToOrganisationGroup', 'orgCodeName', orgCodeName)
            // verify required parameter 'groupId' is not null or undefined
            assertParamExists('addUsersToOrganisationGroup', 'groupId', groupId)
            const localVarPath = `/v1/Organisation/{orgCodeName}/groups/{groupId}/users`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"groupId"}}`, encodeURIComponent(String(groupId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(addOrganisationGroupUserCommandRequestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {OrganisationBody} [organisationBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createOrganisation: async (organisationBody?: OrganisationBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/Organisation`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(organisationBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {string} appId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAppConfig: async (id: string, appId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteAppConfig', 'id', id)
            // verify required parameter 'appId' is not null or undefined
            assertParamExists('deleteAppConfig', 'appId', appId)
            const localVarPath = `/v1/Organisation/{id}/Config/{appId}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"appId"}}`, encodeURIComponent(String(appId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteGroupFromOrganisation: async (orgCodeName: string, groupId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('deleteGroupFromOrganisation', 'orgCodeName', orgCodeName)
            // verify required parameter 'groupId' is not null or undefined
            assertParamExists('deleteGroupFromOrganisation', 'groupId', groupId)
            const localVarPath = `/v1/Organisation/{orgCodeName}/groups/{groupId}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"groupId"}}`, encodeURIComponent(String(groupId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteOrganisation: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteOrganisation', 'id', id)
            const localVarPath = `/v1/Organisation/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteOrganisationUser: async (orgCodeName: string, userId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('deleteOrganisationUser', 'orgCodeName', orgCodeName)
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('deleteOrganisationUser', 'userId', userId)
            const localVarPath = `/v1/Organisation/{orgCodeName}/users/{userId}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {EditOrganisationGroupCommandRequestBody} [editOrganisationGroupCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        editOrganisationGroup: async (orgCodeName: string, groupId: string, editOrganisationGroupCommandRequestBody?: EditOrganisationGroupCommandRequestBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('editOrganisationGroup', 'orgCodeName', orgCodeName)
            // verify required parameter 'groupId' is not null or undefined
            assertParamExists('editOrganisationGroup', 'groupId', groupId)
            const localVarPath = `/v1/Organisation/{orgCodeName}/groups/{groupId}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"groupId"}}`, encodeURIComponent(String(groupId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(editOrganisationGroupCommandRequestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminOrganisationList: async (filter?: string, orderBy?: string, offset?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/Organisation/admin`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisation: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getOrganisation', 'id', id)
            const localVarPath = `/v1/Organisation/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationByCodeName: async (orgCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getOrganisationByCodeName', 'orgCodeName', orgCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationGroup: async (orgCodeName: string, groupId: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getOrganisationGroup', 'orgCodeName', orgCodeName)
            // verify required parameter 'groupId' is not null or undefined
            assertParamExists('getOrganisationGroup', 'groupId', groupId)
            const localVarPath = `/v1/Organisation/{orgCodeName}/groups/{groupId}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"groupId"}}`, encodeURIComponent(String(groupId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {string} [embed] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationGroupEligibleUsers: async (orgCodeName: string, groupId: string, filter?: string, orderBy?: string, offset?: number, limit?: number, embed?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getOrganisationGroupEligibleUsers', 'orgCodeName', orgCodeName)
            // verify required parameter 'groupId' is not null or undefined
            assertParamExists('getOrganisationGroupEligibleUsers', 'groupId', groupId)
            const localVarPath = `/v1/Organisation/{orgCodeName}/groups/{groupId}/eligibleUsers`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"groupId"}}`, encodeURIComponent(String(groupId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }

            if (embed !== undefined) {
                localVarQueryParameter['embed'] = embed;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationGroups: async (orgCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getOrganisationGroups', 'orgCodeName', orgCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/groups`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationList: async (filter?: string, orderBy?: string, offset?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/Organisation`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} userId 
         * @param {boolean} [takeUserFromChildOrganisations] 
         * @param {string} [embed] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationUser: async (orgCodeName: string, userId: string, takeUserFromChildOrganisations?: boolean, embed?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getOrganisationUser', 'orgCodeName', orgCodeName)
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('getOrganisationUser', 'userId', userId)
            const localVarPath = `/v1/Organisation/{orgCodeName}/users/{userId}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (takeUserFromChildOrganisations !== undefined) {
                localVarQueryParameter['takeUserFromChildOrganisations'] = takeUserFromChildOrganisations;
            }

            if (embed !== undefined) {
                localVarQueryParameter['embed'] = embed;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {string} [embed] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationUsers: async (orgCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, embed?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getOrganisationUsers', 'orgCodeName', orgCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/users`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }

            if (embed !== undefined) {
                localVarQueryParameter['embed'] = embed;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {string} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        removeUserFromOrganisationGroup: async (orgCodeName: string, groupId: string, userId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('removeUserFromOrganisationGroup', 'orgCodeName', orgCodeName)
            // verify required parameter 'groupId' is not null or undefined
            assertParamExists('removeUserFromOrganisationGroup', 'groupId', groupId)
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('removeUserFromOrganisationGroup', 'userId', userId)
            const localVarPath = `/v1/Organisation/{orgCodeName}/groups/{groupId}/users/{userId}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"groupId"}}`, encodeURIComponent(String(groupId)))
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {UpdateOrganisationBody} [updateOrganisationBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrganisation: async (id: string, updateOrganisationBody?: UpdateOrganisationBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateOrganisation', 'id', id)
            const localVarPath = `/v1/Organisation/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateOrganisationBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrganisationAvatar: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateOrganisationAvatar', 'id', id)
            const localVarPath = `/v1/Organisation/{id}/avatar`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrganisationHeader: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateOrganisationHeader', 'id', id)
            const localVarPath = `/v1/Organisation/{id}/header`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} userId 
         * @param {UpdateUserInOrganisationCommandRequestBody} [updateUserInOrganisationCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateUserInOrganisation: async (orgCodeName: string, userId: string, updateUserInOrganisationCommandRequestBody?: UpdateUserInOrganisationCommandRequestBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('updateUserInOrganisation', 'orgCodeName', orgCodeName)
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('updateUserInOrganisation', 'userId', userId)
            const localVarPath = `/v1/Organisation/{orgCodeName}/users/{userId}`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateUserInOrganisationCommandRequestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateOrganisation: async (name?: string, codeName?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/Organisation/validate`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['Name'] = name;
            }

            if (codeName !== undefined) {
                localVarQueryParameter['CodeName'] = codeName;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateOrganisationById: async (id: string, name?: string, codeName?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('validateOrganisationById', 'id', id)
            const localVarPath = `/v1/Organisation/{id}/validate`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (name !== undefined) {
                localVarQueryParameter['Name'] = name;
            }

            if (codeName !== undefined) {
                localVarQueryParameter['CodeName'] = codeName;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * OrganisationApi - functional programming interface
 * @export
 */
export const OrganisationApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = OrganisationApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} id 
         * @param {Array<string>} [requestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async addAppListToOrganisation(id: string, requestBody?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.addAppListToOrganisation(id, requestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.addAppListToOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {string} appId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async addAppToOrganisation(id: string, appId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.addAppToOrganisation(id, appId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.addAppToOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {AddOrganisationGroupCommandRequestBody} [addOrganisationGroupCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async addOrganisationGroup(orgCodeName: string, addOrganisationGroupCommandRequestBody?: AddOrganisationGroupCommandRequestBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationGroupModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.addOrganisationGroup(orgCodeName, addOrganisationGroupCommandRequestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.addOrganisationGroup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {AddOrganisationUserCommandRequestBody} [addOrganisationUserCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async addUsersToOrganisation(orgCodeName: string, addOrganisationUserCommandRequestBody?: AddOrganisationUserCommandRequestBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.addUsersToOrganisation(orgCodeName, addOrganisationUserCommandRequestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.addUsersToOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {AddOrganisationGroupUserCommandRequestBody} [addOrganisationGroupUserCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async addUsersToOrganisationGroup(orgCodeName: string, groupId: string, addOrganisationGroupUserCommandRequestBody?: AddOrganisationGroupUserCommandRequestBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.addUsersToOrganisationGroup(orgCodeName, groupId, addOrganisationGroupUserCommandRequestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.addUsersToOrganisationGroup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {OrganisationBody} [organisationBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createOrganisation(organisationBody?: OrganisationBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createOrganisation(organisationBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.createOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {string} appId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteAppConfig(id: string, appId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteAppConfig(id, appId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.deleteAppConfig']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteGroupFromOrganisation(orgCodeName: string, groupId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteGroupFromOrganisation(orgCodeName, groupId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.deleteGroupFromOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteOrganisation(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteOrganisation(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.deleteOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteOrganisationUser(orgCodeName: string, userId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteOrganisationUser(orgCodeName, userId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.deleteOrganisationUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {EditOrganisationGroupCommandRequestBody} [editOrganisationGroupCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async editOrganisationGroup(orgCodeName: string, groupId: string, editOrganisationGroupCommandRequestBody?: EditOrganisationGroupCommandRequestBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationGroupModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.editOrganisationGroup(orgCodeName, groupId, editOrganisationGroupCommandRequestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.editOrganisationGroup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminOrganisationList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationModelIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminOrganisationList(filter, orderBy, offset, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.getAdminOrganisationList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrganisation(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrganisation(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.getOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrganisationByCodeName(orgCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrganisationByCodeName(orgCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.getOrganisationByCodeName']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrganisationGroup(orgCodeName: string, groupId: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationGroupPagedModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrganisationGroup(orgCodeName, groupId, filter, orderBy, offset, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.getOrganisationGroup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {string} [embed] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrganisationGroupEligibleUsers(orgCodeName: string, groupId: string, filter?: string, orderBy?: string, offset?: number, limit?: number, embed?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationUserModelApiPagedListResultCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrganisationGroupEligibleUsers(orgCodeName, groupId, filter, orderBy, offset, limit, embed, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.getOrganisationGroupEligibleUsers']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrganisationGroups(orgCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationGroupModelApiPagedListResultCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrganisationGroups(orgCodeName, filter, orderBy, offset, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.getOrganisationGroups']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrganisationList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationModelIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrganisationList(filter, orderBy, offset, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.getOrganisationList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} userId 
         * @param {boolean} [takeUserFromChildOrganisations] 
         * @param {string} [embed] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrganisationUser(orgCodeName: string, userId: string, takeUserFromChildOrganisations?: boolean, embed?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationUserModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrganisationUser(orgCodeName, userId, takeUserFromChildOrganisations, embed, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.getOrganisationUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {string} [embed] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrganisationUsers(orgCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, embed?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationUserModelApiPagedListResultCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrganisationUsers(orgCodeName, filter, orderBy, offset, limit, embed, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.getOrganisationUsers']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {string} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async removeUserFromOrganisationGroup(orgCodeName: string, groupId: string, userId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.removeUserFromOrganisationGroup(orgCodeName, groupId, userId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.removeUserFromOrganisationGroup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {UpdateOrganisationBody} [updateOrganisationBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateOrganisation(id: string, updateOrganisationBody?: UpdateOrganisationBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateOrganisation(id, updateOrganisationBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.updateOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateOrganisationAvatar(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateOrganisationAvatar(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.updateOrganisationAvatar']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateOrganisationHeader(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateOrganisationHeader(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.updateOrganisationHeader']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} userId 
         * @param {UpdateUserInOrganisationCommandRequestBody} [updateUserInOrganisationCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateUserInOrganisation(orgCodeName: string, userId: string, updateUserInOrganisationCommandRequestBody?: UpdateUserInOrganisationCommandRequestBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateUserInOrganisation(orgCodeName, userId, updateUserInOrganisationCommandRequestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.updateUserInOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async validateOrganisation(name?: string, codeName?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ValidationResultModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.validateOrganisation(name, codeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.validateOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async validateOrganisationById(id: string, name?: string, codeName?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ValidationResultModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.validateOrganisationById(id, name, codeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['OrganisationApi.validateOrganisationById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * OrganisationApi - factory interface
 * @export
 */
export const OrganisationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = OrganisationApiFp(configuration)
    return {
        /**
         * 
         * @param {string} id 
         * @param {Array<string>} [requestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addAppListToOrganisation(id: string, requestBody?: Array<string>, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.addAppListToOrganisation(id, requestBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {string} appId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addAppToOrganisation(id: string, appId: string, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.addAppToOrganisation(id, appId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {AddOrganisationGroupCommandRequestBody} [addOrganisationGroupCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addOrganisationGroup(orgCodeName: string, addOrganisationGroupCommandRequestBody?: AddOrganisationGroupCommandRequestBody, options?: any): AxiosPromise<OrganisationGroupModelCommandResponse> {
            return localVarFp.addOrganisationGroup(orgCodeName, addOrganisationGroupCommandRequestBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {AddOrganisationUserCommandRequestBody} [addOrganisationUserCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addUsersToOrganisation(orgCodeName: string, addOrganisationUserCommandRequestBody?: AddOrganisationUserCommandRequestBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.addUsersToOrganisation(orgCodeName, addOrganisationUserCommandRequestBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {AddOrganisationGroupUserCommandRequestBody} [addOrganisationGroupUserCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addUsersToOrganisationGroup(orgCodeName: string, groupId: string, addOrganisationGroupUserCommandRequestBody?: AddOrganisationGroupUserCommandRequestBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.addUsersToOrganisationGroup(orgCodeName, groupId, addOrganisationGroupUserCommandRequestBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {OrganisationBody} [organisationBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createOrganisation(organisationBody?: OrganisationBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.createOrganisation(organisationBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {string} appId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAppConfig(id: string, appId: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteAppConfig(id, appId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteGroupFromOrganisation(orgCodeName: string, groupId: string, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.deleteGroupFromOrganisation(orgCodeName, groupId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteOrganisation(id: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteOrganisation(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteOrganisationUser(orgCodeName: string, userId: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteOrganisationUser(orgCodeName, userId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {EditOrganisationGroupCommandRequestBody} [editOrganisationGroupCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        editOrganisationGroup(orgCodeName: string, groupId: string, editOrganisationGroupCommandRequestBody?: EditOrganisationGroupCommandRequestBody, options?: any): AxiosPromise<OrganisationGroupModelCommandResponse> {
            return localVarFp.editOrganisationGroup(orgCodeName, groupId, editOrganisationGroupCommandRequestBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminOrganisationList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: any): AxiosPromise<OrganisationModelIEnumerableCommandResponse> {
            return localVarFp.getAdminOrganisationList(filter, orderBy, offset, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisation(id: string, options?: any): AxiosPromise<OrganisationModelCommandResponse> {
            return localVarFp.getOrganisation(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationByCodeName(orgCodeName: string, options?: any): AxiosPromise<OrganisationModelCommandResponse> {
            return localVarFp.getOrganisationByCodeName(orgCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationGroup(orgCodeName: string, groupId: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: any): AxiosPromise<OrganisationGroupPagedModelCommandResponse> {
            return localVarFp.getOrganisationGroup(orgCodeName, groupId, filter, orderBy, offset, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {string} [embed] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationGroupEligibleUsers(orgCodeName: string, groupId: string, filter?: string, orderBy?: string, offset?: number, limit?: number, embed?: string, options?: any): AxiosPromise<OrganisationUserModelApiPagedListResultCommandResponse> {
            return localVarFp.getOrganisationGroupEligibleUsers(orgCodeName, groupId, filter, orderBy, offset, limit, embed, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationGroups(orgCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: any): AxiosPromise<OrganisationGroupModelApiPagedListResultCommandResponse> {
            return localVarFp.getOrganisationGroups(orgCodeName, filter, orderBy, offset, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: any): AxiosPromise<OrganisationModelIEnumerableCommandResponse> {
            return localVarFp.getOrganisationList(filter, orderBy, offset, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} userId 
         * @param {boolean} [takeUserFromChildOrganisations] 
         * @param {string} [embed] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationUser(orgCodeName: string, userId: string, takeUserFromChildOrganisations?: boolean, embed?: string, options?: any): AxiosPromise<OrganisationUserModelCommandResponse> {
            return localVarFp.getOrganisationUser(orgCodeName, userId, takeUserFromChildOrganisations, embed, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {string} [embed] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrganisationUsers(orgCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, embed?: string, options?: any): AxiosPromise<OrganisationUserModelApiPagedListResultCommandResponse> {
            return localVarFp.getOrganisationUsers(orgCodeName, filter, orderBy, offset, limit, embed, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} groupId 
         * @param {string} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        removeUserFromOrganisationGroup(orgCodeName: string, groupId: string, userId: string, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.removeUserFromOrganisationGroup(orgCodeName, groupId, userId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {UpdateOrganisationBody} [updateOrganisationBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrganisation(id: string, updateOrganisationBody?: UpdateOrganisationBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateOrganisation(id, updateOrganisationBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrganisationAvatar(id: string, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateOrganisationAvatar(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateOrganisationHeader(id: string, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateOrganisationHeader(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} userId 
         * @param {UpdateUserInOrganisationCommandRequestBody} [updateUserInOrganisationCommandRequestBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateUserInOrganisation(orgCodeName: string, userId: string, updateUserInOrganisationCommandRequestBody?: UpdateUserInOrganisationCommandRequestBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateUserInOrganisation(orgCodeName, userId, updateUserInOrganisationCommandRequestBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateOrganisation(name?: string, codeName?: string, options?: any): AxiosPromise<ValidationResultModelCommandResponse> {
            return localVarFp.validateOrganisation(name, codeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {string} [name] 
         * @param {string} [codeName] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateOrganisationById(id: string, name?: string, codeName?: string, options?: any): AxiosPromise<ValidationResultModelCommandResponse> {
            return localVarFp.validateOrganisationById(id, name, codeName, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * OrganisationApi - object-oriented interface
 * @export
 * @class OrganisationApi
 * @extends {BaseAPI}
 */
export class OrganisationApi extends BaseAPI {
    /**
     * 
     * @param {string} id 
     * @param {Array<string>} [requestBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public addAppListToOrganisation(id: string, requestBody?: Array<string>, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).addAppListToOrganisation(id, requestBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {string} appId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public addAppToOrganisation(id: string, appId: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).addAppToOrganisation(id, appId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {AddOrganisationGroupCommandRequestBody} [addOrganisationGroupCommandRequestBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public addOrganisationGroup(orgCodeName: string, addOrganisationGroupCommandRequestBody?: AddOrganisationGroupCommandRequestBody, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).addOrganisationGroup(orgCodeName, addOrganisationGroupCommandRequestBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {AddOrganisationUserCommandRequestBody} [addOrganisationUserCommandRequestBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public addUsersToOrganisation(orgCodeName: string, addOrganisationUserCommandRequestBody?: AddOrganisationUserCommandRequestBody, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).addUsersToOrganisation(orgCodeName, addOrganisationUserCommandRequestBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} groupId 
     * @param {AddOrganisationGroupUserCommandRequestBody} [addOrganisationGroupUserCommandRequestBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public addUsersToOrganisationGroup(orgCodeName: string, groupId: string, addOrganisationGroupUserCommandRequestBody?: AddOrganisationGroupUserCommandRequestBody, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).addUsersToOrganisationGroup(orgCodeName, groupId, addOrganisationGroupUserCommandRequestBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {OrganisationBody} [organisationBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public createOrganisation(organisationBody?: OrganisationBody, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).createOrganisation(organisationBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {string} appId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public deleteAppConfig(id: string, appId: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).deleteAppConfig(id, appId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} groupId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public deleteGroupFromOrganisation(orgCodeName: string, groupId: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).deleteGroupFromOrganisation(orgCodeName, groupId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public deleteOrganisation(id: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).deleteOrganisation(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} userId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public deleteOrganisationUser(orgCodeName: string, userId: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).deleteOrganisationUser(orgCodeName, userId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} groupId 
     * @param {EditOrganisationGroupCommandRequestBody} [editOrganisationGroupCommandRequestBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public editOrganisationGroup(orgCodeName: string, groupId: string, editOrganisationGroupCommandRequestBody?: EditOrganisationGroupCommandRequestBody, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).editOrganisationGroup(orgCodeName, groupId, editOrganisationGroupCommandRequestBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public getAdminOrganisationList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).getAdminOrganisationList(filter, orderBy, offset, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public getOrganisation(id: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).getOrganisation(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public getOrganisationByCodeName(orgCodeName: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).getOrganisationByCodeName(orgCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} groupId 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public getOrganisationGroup(orgCodeName: string, groupId: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).getOrganisationGroup(orgCodeName, groupId, filter, orderBy, offset, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} groupId 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {string} [embed] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public getOrganisationGroupEligibleUsers(orgCodeName: string, groupId: string, filter?: string, orderBy?: string, offset?: number, limit?: number, embed?: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).getOrganisationGroupEligibleUsers(orgCodeName, groupId, filter, orderBy, offset, limit, embed, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public getOrganisationGroups(orgCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).getOrganisationGroups(orgCodeName, filter, orderBy, offset, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public getOrganisationList(filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).getOrganisationList(filter, orderBy, offset, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} userId 
     * @param {boolean} [takeUserFromChildOrganisations] 
     * @param {string} [embed] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public getOrganisationUser(orgCodeName: string, userId: string, takeUserFromChildOrganisations?: boolean, embed?: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).getOrganisationUser(orgCodeName, userId, takeUserFromChildOrganisations, embed, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {string} [embed] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public getOrganisationUsers(orgCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, embed?: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).getOrganisationUsers(orgCodeName, filter, orderBy, offset, limit, embed, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} groupId 
     * @param {string} userId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public removeUserFromOrganisationGroup(orgCodeName: string, groupId: string, userId: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).removeUserFromOrganisationGroup(orgCodeName, groupId, userId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {UpdateOrganisationBody} [updateOrganisationBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public updateOrganisation(id: string, updateOrganisationBody?: UpdateOrganisationBody, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).updateOrganisation(id, updateOrganisationBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public updateOrganisationAvatar(id: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).updateOrganisationAvatar(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public updateOrganisationHeader(id: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).updateOrganisationHeader(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} userId 
     * @param {UpdateUserInOrganisationCommandRequestBody} [updateUserInOrganisationCommandRequestBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public updateUserInOrganisation(orgCodeName: string, userId: string, updateUserInOrganisationCommandRequestBody?: UpdateUserInOrganisationCommandRequestBody, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).updateUserInOrganisation(orgCodeName, userId, updateUserInOrganisationCommandRequestBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [name] 
     * @param {string} [codeName] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public validateOrganisation(name?: string, codeName?: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).validateOrganisation(name, codeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {string} [name] 
     * @param {string} [codeName] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OrganisationApi
     */
    public validateOrganisationById(id: string, name?: string, codeName?: string, options?: RawAxiosRequestConfig) {
        return OrganisationApiFp(this.configuration).validateOrganisationById(id, name, codeName, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * PermissionApi - axios parameter creator
 * @export
 */
export const PermissionApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScopesAndResourcesForOrganisation: async (orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getScopesAndResourcesForOrganisation', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getScopesAndResourcesForOrganisation', 'appCodeName', appCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/app/{appCodeName}/scopes`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PermissionApi - functional programming interface
 * @export
 */
export const PermissionApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = PermissionApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getScopesAndResourcesForOrganisation(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ScopeDataModelIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getScopesAndResourcesForOrganisation(orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionApi.getScopesAndResourcesForOrganisation']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * PermissionApi - factory interface
 * @export
 */
export const PermissionApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = PermissionApiFp(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScopesAndResourcesForOrganisation(orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<ScopeDataModelIEnumerableCommandResponse> {
            return localVarFp.getScopesAndResourcesForOrganisation(orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * PermissionApi - object-oriented interface
 * @export
 * @class PermissionApi
 * @extends {BaseAPI}
 */
export class PermissionApi extends BaseAPI {
    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionApi
     */
    public getScopesAndResourcesForOrganisation(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return PermissionApiFp(this.configuration).getScopesAndResourcesForOrganisation(orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * RoleApi - axios parameter creator
 * @export
 */
export const RoleApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {RoleBody} [roleBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createRole: async (orgCodeName: string, appCodeName: string, roleBody?: RoleBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('createRole', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('createRole', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/role`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(roleBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteRole: async (id: string, orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteRole', 'id', id)
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('deleteRole', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('deleteRole', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/role/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllRoles: async (orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getAllRoles', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getAllRoles', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/role/all`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getById: async (id: string, orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getById', 'id', id)
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getById', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getById', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/role/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFiltered: async (orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getFiltered', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getFiltered', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/role`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filter !== undefined) {
                localVarQueryParameter['filter'] = filter;
            }

            if (orderBy !== undefined) {
                localVarQueryParameter['orderBy'] = orderBy;
            }

            if (offset !== undefined) {
                localVarQueryParameter['offset'] = offset;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {RoleBody} [roleBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateRole: async (id: string, orgCodeName: string, appCodeName: string, roleBody?: RoleBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateRole', 'id', id)
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('updateRole', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('updateRole', 'appCodeName', appCodeName)
            const localVarPath = `/v1/organisation/{orgCodeName}/app/{appCodeName}/role/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(roleBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * RoleApi - functional programming interface
 * @export
 */
export const RoleApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = RoleApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {RoleBody} [roleBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createRole(orgCodeName: string, appCodeName: string, roleBody?: RoleBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createRole(orgCodeName, appCodeName, roleBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleApi.createRole']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteRole(id: string, orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteRole(id, orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleApi.deleteRole']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllRoles(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<RoleModelIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllRoles(orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleApi.getAllRoles']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getById(id: string, orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<RoleModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getById(id, orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleApi.getById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getFiltered(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<RoleModelApiPagedListResultCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getFiltered(orgCodeName, appCodeName, filter, orderBy, offset, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleApi.getFiltered']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {RoleBody} [roleBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateRole(id: string, orgCodeName: string, appCodeName: string, roleBody?: RoleBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateRole(id, orgCodeName, appCodeName, roleBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleApi.updateRole']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * RoleApi - factory interface
 * @export
 */
export const RoleApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = RoleApiFp(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {RoleBody} [roleBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createRole(orgCodeName: string, appCodeName: string, roleBody?: RoleBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.createRole(orgCodeName, appCodeName, roleBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteRole(id: string, orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteRole(id, orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllRoles(orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<RoleModelIEnumerableCommandResponse> {
            return localVarFp.getAllRoles(orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getById(id: string, orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<RoleModelCommandResponse> {
            return localVarFp.getById(id, orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} [filter] 
         * @param {string} [orderBy] 
         * @param {number} [offset] 
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFiltered(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: any): AxiosPromise<RoleModelApiPagedListResultCommandResponse> {
            return localVarFp.getFiltered(orgCodeName, appCodeName, filter, orderBy, offset, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {RoleBody} [roleBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateRole(id: string, orgCodeName: string, appCodeName: string, roleBody?: RoleBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.updateRole(id, orgCodeName, appCodeName, roleBody, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * RoleApi - object-oriented interface
 * @export
 * @class RoleApi
 * @extends {BaseAPI}
 */
export class RoleApi extends BaseAPI {
    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {RoleBody} [roleBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleApi
     */
    public createRole(orgCodeName: string, appCodeName: string, roleBody?: RoleBody, options?: RawAxiosRequestConfig) {
        return RoleApiFp(this.configuration).createRole(orgCodeName, appCodeName, roleBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleApi
     */
    public deleteRole(id: string, orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return RoleApiFp(this.configuration).deleteRole(id, orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleApi
     */
    public getAllRoles(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return RoleApiFp(this.configuration).getAllRoles(orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleApi
     */
    public getById(id: string, orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return RoleApiFp(this.configuration).getById(id, orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {string} [filter] 
     * @param {string} [orderBy] 
     * @param {number} [offset] 
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleApi
     */
    public getFiltered(orgCodeName: string, appCodeName: string, filter?: string, orderBy?: string, offset?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return RoleApiFp(this.configuration).getFiltered(orgCodeName, appCodeName, filter, orderBy, offset, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {RoleBody} [roleBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleApi
     */
    public updateRole(id: string, orgCodeName: string, appCodeName: string, roleBody?: RoleBody, options?: RawAxiosRequestConfig) {
        return RoleApiFp(this.configuration).updateRole(id, orgCodeName, appCodeName, roleBody, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * RoleDefinitionApi - axios parameter creator
 * @export
 */
export const RoleDefinitionApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getByAppCode: async (appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getByAppCode', 'appCodeName', appCodeName)
            const localVarPath = `/v1/App/{appCodeName}/role-definition`
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getByOrganisationAndAppCode: async (orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getByOrganisationAndAppCode', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getByOrganisationAndAppCode', 'appCodeName', appCodeName)
            const localVarPath = `/v1/Organisation/{orgCodeName}/App/{appCodeName}/role-definition`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * RoleDefinitionApi - functional programming interface
 * @export
 */
export const RoleDefinitionApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = RoleDefinitionApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getByAppCode(appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AppRoleDefinitionPermissionIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getByAppCode(appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleDefinitionApi.getByAppCode']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getByOrganisationAndAppCode(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AppRoleDefinitionPermissionIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getByOrganisationAndAppCode(orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RoleDefinitionApi.getByOrganisationAndAppCode']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * RoleDefinitionApi - factory interface
 * @export
 */
export const RoleDefinitionApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = RoleDefinitionApiFp(configuration)
    return {
        /**
         * 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getByAppCode(appCodeName: string, options?: any): AxiosPromise<AppRoleDefinitionPermissionIEnumerableCommandResponse> {
            return localVarFp.getByAppCode(appCodeName, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getByOrganisationAndAppCode(orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<AppRoleDefinitionPermissionIEnumerableCommandResponse> {
            return localVarFp.getByOrganisationAndAppCode(orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * RoleDefinitionApi - object-oriented interface
 * @export
 * @class RoleDefinitionApi
 * @extends {BaseAPI}
 */
export class RoleDefinitionApi extends BaseAPI {
    /**
     * 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleDefinitionApi
     */
    public getByAppCode(appCodeName: string, options?: RawAxiosRequestConfig) {
        return RoleDefinitionApiFp(this.configuration).getByAppCode(appCodeName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RoleDefinitionApi
     */
    public getByOrganisationAndAppCode(orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return RoleDefinitionApiFp(this.configuration).getByOrganisationAndAppCode(orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * ScopeResourceApi - axios parameter creator
 * @export
 */
export const ScopeResourceApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} roleId 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScopeResourcesForRole: async (roleId: string, orgCodeName: string, appCodeName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'roleId' is not null or undefined
            assertParamExists('getScopeResourcesForRole', 'roleId', roleId)
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getScopeResourcesForRole', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getScopeResourcesForRole', 'appCodeName', appCodeName)
            const localVarPath = `/v1/ScopeResource/organisation/{orgCodeName}/app/{appCodeName}/role/{roleId}`
                .replace(`{${"roleId"}}`, encodeURIComponent(String(roleId)))
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ScopeResourceApi - functional programming interface
 * @export
 */
export const ScopeResourceApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ScopeResourceApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} roleId 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getScopeResourcesForRole(roleId: string, orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ScopeDataModelIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getScopeResourcesForRole(roleId, orgCodeName, appCodeName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ScopeResourceApi.getScopeResourcesForRole']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ScopeResourceApi - factory interface
 * @export
 */
export const ScopeResourceApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ScopeResourceApiFp(configuration)
    return {
        /**
         * 
         * @param {string} roleId 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getScopeResourcesForRole(roleId: string, orgCodeName: string, appCodeName: string, options?: any): AxiosPromise<ScopeDataModelIEnumerableCommandResponse> {
            return localVarFp.getScopeResourcesForRole(roleId, orgCodeName, appCodeName, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ScopeResourceApi - object-oriented interface
 * @export
 * @class ScopeResourceApi
 * @extends {BaseAPI}
 */
export class ScopeResourceApi extends BaseAPI {
    /**
     * 
     * @param {string} roleId 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ScopeResourceApi
     */
    public getScopeResourcesForRole(roleId: string, orgCodeName: string, appCodeName: string, options?: RawAxiosRequestConfig) {
        return ScopeResourceApiFp(this.configuration).getScopeResourcesForRole(roleId, orgCodeName, appCodeName, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * TenantApi - axios parameter creator
 * @export
 */
export const TenantApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAll: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/Tenant/tenants`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * TenantApi - functional programming interface
 * @export
 */
export const TenantApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = TenantApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAll(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<StringIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAll(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['TenantApi.getAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * TenantApi - factory interface
 * @export
 */
export const TenantApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = TenantApiFp(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAll(options?: any): AxiosPromise<StringIEnumerableCommandResponse> {
            return localVarFp.getAll(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * TenantApi - object-oriented interface
 * @export
 * @class TenantApi
 * @extends {BaseAPI}
 */
export class TenantApi extends BaseAPI {
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TenantApi
     */
    public getAll(options?: RawAxiosRequestConfig) {
        return TenantApiFp(this.configuration).getAll(options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * UserApi - axios parameter creator
 * @export
 */
export const UserApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} [email] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        v1UserGet: async (email?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/User`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (email !== undefined) {
                localVarQueryParameter['email'] = email;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * UserApi - functional programming interface
 * @export
 */
export const UserApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = UserApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} [email] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async v1UserGet(email?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.v1UserGet(email, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserApi.v1UserGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * UserApi - factory interface
 * @export
 */
export const UserApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = UserApiFp(configuration)
    return {
        /**
         * 
         * @param {string} [email] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        v1UserGet(email?: string, options?: any): AxiosPromise<UserModelCommandResponse> {
            return localVarFp.v1UserGet(email, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * UserApi - object-oriented interface
 * @export
 * @class UserApi
 * @extends {BaseAPI}
 */
export class UserApi extends BaseAPI {
    /**
     * 
     * @param {string} [email] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserApi
     */
    public v1UserGet(email?: string, options?: RawAxiosRequestConfig) {
        return UserApiFp(this.configuration).v1UserGet(email, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * UserAccessApi - axios parameter creator
 * @export
 */
export const UserAccessApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} permission 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCurrentUserPermissionAccess: async (permission: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'permission' is not null or undefined
            assertParamExists('getCurrentUserPermissionAccess', 'permission', permission)
            const localVarPath = `/v1/Users/<USER>/{permission}`
                .replace(`{${"permission"}}`, encodeURIComponent(String(permission)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {ValidateCurrentUserPermissionsAccessQuery} [validateCurrentUserPermissionsAccessQuery] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateCurrentUserPermissionsAccess: async (validateCurrentUserPermissionsAccessQuery?: ValidateCurrentUserPermissionsAccessQuery, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/Users/<USER>/Validate`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(validateCurrentUserPermissionsAccessQuery, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * UserAccessApi - functional programming interface
 * @export
 */
export const UserAccessApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = UserAccessApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} permission 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCurrentUserPermissionAccess(permission: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BooleanCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCurrentUserPermissionAccess(permission, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserAccessApi.getCurrentUserPermissionAccess']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {ValidateCurrentUserPermissionsAccessQuery} [validateCurrentUserPermissionsAccessQuery] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async validateCurrentUserPermissionsAccess(validateCurrentUserPermissionsAccessQuery?: ValidateCurrentUserPermissionsAccessQuery, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ValidateCurrentUserPermissionsAccessQueryResponseCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.validateCurrentUserPermissionsAccess(validateCurrentUserPermissionsAccessQuery, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserAccessApi.validateCurrentUserPermissionsAccess']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * UserAccessApi - factory interface
 * @export
 */
export const UserAccessApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = UserAccessApiFp(configuration)
    return {
        /**
         * 
         * @param {string} permission 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCurrentUserPermissionAccess(permission: string, options?: any): AxiosPromise<BooleanCommandResponse> {
            return localVarFp.getCurrentUserPermissionAccess(permission, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {ValidateCurrentUserPermissionsAccessQuery} [validateCurrentUserPermissionsAccessQuery] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateCurrentUserPermissionsAccess(validateCurrentUserPermissionsAccessQuery?: ValidateCurrentUserPermissionsAccessQuery, options?: any): AxiosPromise<ValidateCurrentUserPermissionsAccessQueryResponseCommandResponse> {
            return localVarFp.validateCurrentUserPermissionsAccess(validateCurrentUserPermissionsAccessQuery, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * UserAccessApi - object-oriented interface
 * @export
 * @class UserAccessApi
 * @extends {BaseAPI}
 */
export class UserAccessApi extends BaseAPI {
    /**
     * 
     * @param {string} permission 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserAccessApi
     */
    public getCurrentUserPermissionAccess(permission: string, options?: RawAxiosRequestConfig) {
        return UserAccessApiFp(this.configuration).getCurrentUserPermissionAccess(permission, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {ValidateCurrentUserPermissionsAccessQuery} [validateCurrentUserPermissionsAccessQuery] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserAccessApi
     */
    public validateCurrentUserPermissionsAccess(validateCurrentUserPermissionsAccessQuery?: ValidateCurrentUserPermissionsAccessQuery, options?: RawAxiosRequestConfig) {
        return UserAccessApiFp(this.configuration).validateCurrentUserPermissionsAccess(validateCurrentUserPermissionsAccessQuery, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * UserIdentityProviderApi - axios parameter creator
 * @export
 */
export const UserIdentityProviderApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} [email] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLoginProviderDetails: async (email?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/User/IdentityProvider/login/provider`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (email !== undefined) {
                localVarQueryParameter['email'] = email;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserIdentityProvider: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/User/IdentityProvider`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * UserIdentityProviderApi - functional programming interface
 * @export
 */
export const UserIdentityProviderApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = UserIdentityProviderApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} [email] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLoginProviderDetails(email?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<LoginProviderDetailsCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getLoginProviderDetails(email, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserIdentityProviderApi.getLoginProviderDetails']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getUserIdentityProvider(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserIdentityProviderModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getUserIdentityProvider(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserIdentityProviderApi.getUserIdentityProvider']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * UserIdentityProviderApi - factory interface
 * @export
 */
export const UserIdentityProviderApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = UserIdentityProviderApiFp(configuration)
    return {
        /**
         * 
         * @param {string} [email] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLoginProviderDetails(email?: string, options?: any): AxiosPromise<LoginProviderDetailsCommandResponse> {
            return localVarFp.getLoginProviderDetails(email, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserIdentityProvider(options?: any): AxiosPromise<UserIdentityProviderModelCommandResponse> {
            return localVarFp.getUserIdentityProvider(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * UserIdentityProviderApi - object-oriented interface
 * @export
 * @class UserIdentityProviderApi
 * @extends {BaseAPI}
 */
export class UserIdentityProviderApi extends BaseAPI {
    /**
     * 
     * @param {string} [email] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserIdentityProviderApi
     */
    public getLoginProviderDetails(email?: string, options?: RawAxiosRequestConfig) {
        return UserIdentityProviderApiFp(this.configuration).getLoginProviderDetails(email, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserIdentityProviderApi
     */
    public getUserIdentityProvider(options?: RawAxiosRequestConfig) {
        return UserIdentityProviderApiFp(this.configuration).getUserIdentityProvider(options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * UserOrganisationApi - axios parameter creator
 * @export
 */
export const UserOrganisationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        v1UserOrganisationGet: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/User/Organisation`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * UserOrganisationApi - functional programming interface
 * @export
 */
export const UserOrganisationApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = UserOrganisationApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async v1UserOrganisationGet(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<OrganisationModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.v1UserOrganisationGet(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserOrganisationApi.v1UserOrganisationGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * UserOrganisationApi - factory interface
 * @export
 */
export const UserOrganisationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = UserOrganisationApiFp(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        v1UserOrganisationGet(options?: any): AxiosPromise<OrganisationModelCommandResponse> {
            return localVarFp.v1UserOrganisationGet(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * UserOrganisationApi - object-oriented interface
 * @export
 * @class UserOrganisationApi
 * @extends {BaseAPI}
 */
export class UserOrganisationApi extends BaseAPI {
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserOrganisationApi
     */
    public v1UserOrganisationGet(options?: RawAxiosRequestConfig) {
        return UserOrganisationApiFp(this.configuration).v1UserOrganisationGet(options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * UserPermissionApi - axios parameter creator
 * @export
 */
export const UserPermissionApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} userEmail 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppOrgUserPermissions: async (orgCodeName: string, appCodeName: string, userEmail: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'orgCodeName' is not null or undefined
            assertParamExists('getAppOrgUserPermissions', 'orgCodeName', orgCodeName)
            // verify required parameter 'appCodeName' is not null or undefined
            assertParamExists('getAppOrgUserPermissions', 'appCodeName', appCodeName)
            // verify required parameter 'userEmail' is not null or undefined
            assertParamExists('getAppOrgUserPermissions', 'userEmail', userEmail)
            const localVarPath = `/v1/Organisation/{orgCodeName}/app/{appCodeName}/user-permission/{userEmail}/permission`
                .replace(`{${"orgCodeName"}}`, encodeURIComponent(String(orgCodeName)))
                .replace(`{${"appCodeName"}}`, encodeURIComponent(String(appCodeName)))
                .replace(`{${"userEmail"}}`, encodeURIComponent(String(userEmail)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * UserPermissionApi - functional programming interface
 * @export
 */
export const UserPermissionApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = UserPermissionApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} userEmail 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAppOrgUserPermissions(orgCodeName: string, appCodeName: string, userEmail: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AppOrganisationPermissionIEnumerableCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAppOrgUserPermissions(orgCodeName, appCodeName, userEmail, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserPermissionApi.getAppOrgUserPermissions']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * UserPermissionApi - factory interface
 * @export
 */
export const UserPermissionApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = UserPermissionApiFp(configuration)
    return {
        /**
         * 
         * @param {string} orgCodeName 
         * @param {string} appCodeName 
         * @param {string} userEmail 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppOrgUserPermissions(orgCodeName: string, appCodeName: string, userEmail: string, options?: any): AxiosPromise<AppOrganisationPermissionIEnumerableCommandResponse> {
            return localVarFp.getAppOrgUserPermissions(orgCodeName, appCodeName, userEmail, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * UserPermissionApi - object-oriented interface
 * @export
 * @class UserPermissionApi
 * @extends {BaseAPI}
 */
export class UserPermissionApi extends BaseAPI {
    /**
     * 
     * @param {string} orgCodeName 
     * @param {string} appCodeName 
     * @param {string} userEmail 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserPermissionApi
     */
    public getAppOrgUserPermissions(orgCodeName: string, appCodeName: string, userEmail: string, options?: RawAxiosRequestConfig) {
        return UserPermissionApiFp(this.configuration).getAppOrgUserPermissions(orgCodeName, appCodeName, userEmail, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * UserPreferenceApi - axios parameter creator
 * @export
 */
export const UserPreferenceApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {UserPreferenceBody} [userPreferenceBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createManyPreference: async (userPreferenceBody?: UserPreferenceBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/User/Preferences`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(userPreferenceBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {UserPreferenceBody} [userPreferenceBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createOrUpdateManyPreference: async (userPreferenceBody?: UserPreferenceBody, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/User/Preferences`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(userPreferenceBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} key 
         * @param {string} value 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createOrUpdatePreference: async (key: string, value: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('createOrUpdatePreference', 'key', key)
            // verify required parameter 'value' is not null or undefined
            assertParamExists('createOrUpdatePreference', 'value', value)
            const localVarPath = `/v1/User/Preferences/{key}/{value}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)))
                .replace(`{${"value"}}`, encodeURIComponent(String(value)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} key 
         * @param {string} value 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createPreference: async (key: string, value: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('createPreference', 'key', key)
            // verify required parameter 'value' is not null or undefined
            assertParamExists('createPreference', 'value', value)
            const localVarPath = `/v1/User/Preferences/{key}/{value}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)))
                .replace(`{${"value"}}`, encodeURIComponent(String(value)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deletePreference: async (key: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('deletePreference', 'key', key)
            const localVarPath = `/v1/User/Preferences/{key}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserPreferenceByKey: async (key: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('getUserPreferenceByKey', 'key', key)
            const localVarPath = `/v1/User/Preferences/{key}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserPreferences: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/User/Preferences`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * UserPreferenceApi - functional programming interface
 * @export
 */
export const UserPreferenceApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = UserPreferenceApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {UserPreferenceBody} [userPreferenceBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createManyPreference(userPreferenceBody?: UserPreferenceBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createManyPreference(userPreferenceBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserPreferenceApi.createManyPreference']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {UserPreferenceBody} [userPreferenceBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createOrUpdateManyPreference(userPreferenceBody?: UserPreferenceBody, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createOrUpdateManyPreference(userPreferenceBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserPreferenceApi.createOrUpdateManyPreference']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} key 
         * @param {string} value 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createOrUpdatePreference(key: string, value: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createOrUpdatePreference(key, value, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserPreferenceApi.createOrUpdatePreference']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} key 
         * @param {string} value 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createPreference(key: string, value: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createPreference(key, value, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserPreferenceApi.createPreference']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deletePreference(key: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deletePreference(key, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserPreferenceApi.deletePreference']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getUserPreferenceByKey(key: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserPreferencesModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getUserPreferenceByKey(key, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserPreferenceApi.getUserPreferenceByKey']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getUserPreferences(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserPreferencesModelCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getUserPreferences(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UserPreferenceApi.getUserPreferences']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * UserPreferenceApi - factory interface
 * @export
 */
export const UserPreferenceApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = UserPreferenceApiFp(configuration)
    return {
        /**
         * 
         * @param {UserPreferenceBody} [userPreferenceBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createManyPreference(userPreferenceBody?: UserPreferenceBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.createManyPreference(userPreferenceBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {UserPreferenceBody} [userPreferenceBody] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createOrUpdateManyPreference(userPreferenceBody?: UserPreferenceBody, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.createOrUpdateManyPreference(userPreferenceBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} key 
         * @param {string} value 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createOrUpdatePreference(key: string, value: string, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.createOrUpdatePreference(key, value, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} key 
         * @param {string} value 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createPreference(key: string, value: string, options?: any): AxiosPromise<CommandResponse> {
            return localVarFp.createPreference(key, value, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deletePreference(key: string, options?: any): AxiosPromise<void> {
            return localVarFp.deletePreference(key, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserPreferenceByKey(key: string, options?: any): AxiosPromise<UserPreferencesModelCommandResponse> {
            return localVarFp.getUserPreferenceByKey(key, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserPreferences(options?: any): AxiosPromise<UserPreferencesModelCommandResponse> {
            return localVarFp.getUserPreferences(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * UserPreferenceApi - object-oriented interface
 * @export
 * @class UserPreferenceApi
 * @extends {BaseAPI}
 */
export class UserPreferenceApi extends BaseAPI {
    /**
     * 
     * @param {UserPreferenceBody} [userPreferenceBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserPreferenceApi
     */
    public createManyPreference(userPreferenceBody?: UserPreferenceBody, options?: RawAxiosRequestConfig) {
        return UserPreferenceApiFp(this.configuration).createManyPreference(userPreferenceBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {UserPreferenceBody} [userPreferenceBody] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserPreferenceApi
     */
    public createOrUpdateManyPreference(userPreferenceBody?: UserPreferenceBody, options?: RawAxiosRequestConfig) {
        return UserPreferenceApiFp(this.configuration).createOrUpdateManyPreference(userPreferenceBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} key 
     * @param {string} value 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserPreferenceApi
     */
    public createOrUpdatePreference(key: string, value: string, options?: RawAxiosRequestConfig) {
        return UserPreferenceApiFp(this.configuration).createOrUpdatePreference(key, value, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} key 
     * @param {string} value 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserPreferenceApi
     */
    public createPreference(key: string, value: string, options?: RawAxiosRequestConfig) {
        return UserPreferenceApiFp(this.configuration).createPreference(key, value, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} key 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserPreferenceApi
     */
    public deletePreference(key: string, options?: RawAxiosRequestConfig) {
        return UserPreferenceApiFp(this.configuration).deletePreference(key, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} key 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserPreferenceApi
     */
    public getUserPreferenceByKey(key: string, options?: RawAxiosRequestConfig) {
        return UserPreferenceApiFp(this.configuration).getUserPreferenceByKey(key, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UserPreferenceApi
     */
    public getUserPreferences(options?: RawAxiosRequestConfig) {
        return UserPreferenceApiFp(this.configuration).getUserPreferences(options).then((request) => request(this.axios, this.basePath));
    }
}



