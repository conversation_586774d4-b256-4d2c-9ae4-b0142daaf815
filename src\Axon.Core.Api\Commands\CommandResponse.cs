﻿using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using Axon.Core.Api.Models.App;

namespace Axon.Core.Api.Commands
{
    /// <summary>
    /// Based on the response from the AbstractValidator
    /// </summary>
    [SuppressMessage("<PERSON><PERSON>harper", "InconsistentNaming")]
    [SuppressMessage("<PERSON>Sharper", "MemberCanBePrivate.Global")]
    [SuppressMessage("ReSharper", "UnusedAutoPropertyAccessor.Global")]
    public class CommandResponse : ICommandResponse
    {
        public static CommandResponse Data(object data) => new(data);
        public static CommandResponse Created(string entityType, string entityId) => new(entityType, entityId);
        public static CommandResponse NoContent() =>
            new() { status = HttpStatusCode.NoContent, title = "No content." };
        public static CommandResponse Success() => new();
        public static CommandResponse Failed(IDictionary<string, string[]> errors,
            HttpStatusCode status = HttpStatusCode.BadRequest, string type = null,
            string title = "One or more validation errors occurred.") =>
            new(status, title, type, "", errors);
        public static CommandResponse Failed(string propertyName, string message,
                HttpStatusCode status = HttpStatusCode.BadRequest,
                string type = null,
                string title = "One or more validation errors occurred.") =>
            new(status, title, type, "", new Dictionary<string, string[]>() { { propertyName, new[] { message } } });

        public static CommandResponse<IEnumerable<AppOrganisationModel>> Forbidden(string type, string id,
            HttpStatusCode status = HttpStatusCode.Forbidden) =>
            new()
            {
                status = status,
                errors = new Dictionary<string, string[]>() { { "Id", new[] { "Resource not authorized" } } }
            };

        public static CommandResponse NotFound(string type, string id) =>
            new(HttpStatusCode.NotFound, "Resource not found.", type, id, new Dictionary<string, string[]>() { { "Id", new[] { "Resource not found" } } });

        public static CommandResponse Conflict(string entity, string id, string name) =>
            new(HttpStatusCode.Conflict, "Resource already exist.", entity, id, new Dictionary<string, string[]>() { { name, new[] { "Resource already exist" } } });
        
        public static CommandResponse BadRequest(string entity, string message) =>
            new(HttpStatusCode.BadRequest, "Invalid request.", entity, string.Empty, new Dictionary<string, string[]>() { { entity, new[] { message } } });

        public string title { get; set; }
        public HttpStatusCode status { get; set; }
        public IDictionary<string, string[]> errors { get; set; }

        public string traceId { get; set; }
        public string type { get; set; }
        public object data { get; set; }

        public CommandResponse()
        {
            status = HttpStatusCode.OK;
        }

        private CommandResponse(object data)
        {
            this.status = HttpStatusCode.OK;
            this.data = data;
        }
        private CommandResponse(string rootType, string rootId, HttpStatusCode status = HttpStatusCode.Created)
        {
            this.status = status;
            this.type = rootType;
            this.traceId = rootId;
        }
        private CommandResponse(HttpStatusCode status, string title, string type, string traceId,
            IDictionary<string, string[]> errors = null)
        {
            this.status = status;
            this.title = title;
            this.type = type;
            this.traceId = traceId;
            this.errors = errors;
        }
    }


    public class CommandResponse<TModel> : CommandResponse
    {
        public new TModel data { get; set; }

        public static CommandResponse<TModel> Data(TModel data) => new() { data = data };
        public static CommandResponse<TModel> Data(TModel data, HttpStatusCode status, IDictionary<string, string[]> errors = null)
            => new() { data = data, status = status, errors = errors };
        public static CommandResponse<IEnumerable<TModel>> Data(IEnumerable<TModel> data) => new() { data = data };

        public static CommandResponse<TModel> Failed(IDictionary<string, string[]> errors,
            HttpStatusCode status = HttpStatusCode.BadRequest) =>
            new()
            {
                status = status,
                errors = errors
            };
        public static new CommandResponse<TModel> NotFound(string type, string id) =>
            new()
            {
                type = type,
                traceId = id,
                status = HttpStatusCode.NotFound,
                title = "Resource not found.",
                errors = new Dictionary<string, string[]>() { { "Id", new[] { "Resource not found" } } }
            };
        public static new CommandResponse<TModel> BadRequest(string entity, string message) =>
            new()
            {
                type = entity,
                traceId = string.Empty,
                status = HttpStatusCode.BadRequest,
                title = "Invalid request.",
                errors = new Dictionary<string, string[]>() { { entity, new[] { message } } }
            };

    }
    public interface ICommandResponse
    {
        HttpStatusCode status { get; set; }
    }
}
