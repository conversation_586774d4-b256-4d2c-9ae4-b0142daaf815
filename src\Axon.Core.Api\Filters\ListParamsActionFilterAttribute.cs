﻿using Axon.Core.Api.Controllers;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Infrastructure.OpenApi.Filters;
using Axon.Core.Shared.Api;
using JetBrains.Annotations;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;

namespace Axon.Core.Api.Filters
{
    /// <summary>
    /// A custom MVC Action Filter for use on "List" type endpoints, when applied to xxxListAsync() control actions.<para/>
    /// 
    /// It will automatically parse the query string for the "List" parameters and populate there values in <see cref="ApiControllerBase.ListParams"/>.<para/>
    ///
    /// Note: We have to also supply the information on <see cref="ListParams"/> to Swagger for the Swagger Doc/UI and SDK generators.<para/>
    ///
    /// See <see cref="ListParamsSwaggerOperationFilter"/> for more details
    /// </summary>
    [UsedImplicitly]
    public class ListParamsActionFilterAttribute : ActionFilterAttribute
    {
        private readonly ILogger<ListParamsActionFilterAttribute> logger;

        public ListParamsActionFilterAttribute()
        {
            logger = NullLogger<ListParamsActionFilterAttribute>.Instance;
        }
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            base.OnActionExecuting(context);
            if (context.Controller is ApiControllerBase contextController)
            {
                var queryCollection = contextController.Request.Query;

                GetLimitParam(queryCollection, contextController);
                GetOffsetParam(queryCollection, contextController);
                GetOrderByParam(queryCollection, contextController);
                SearchQuery(queryCollection, contextController);

                logger.LogDebug("ListParams: {param}", contextController.ListParams);
            }
        }

        private static void SearchQuery(IQueryCollection queryCollection, ApiControllerBase contextController)
        {
            var val = queryCollection.ByAlias("filter");

            contextController.ListParams.Filter = val;
        }

        private static void GetOrderByParam(IQueryCollection queryCollection, ApiControllerBase contextController)
        {
            var val = queryCollection.ByAlias("orderby", "order-by");

            contextController.ListParams.OrderBy = OrderByClauses.Parse(val);
        }

        private static void GetLimitParam(IQueryCollection queryCollection, ApiControllerBase contextController)
        {
            var val = queryCollection.ByAlias("limit", "size", "page-size");

            if (int.TryParse(val, out var limit))
            {
                contextController.ListParams.Limit = limit;
            }
        }

        private static void GetOffsetParam(IQueryCollection queryCollection, ApiControllerBase contextController)
        {
            var val = queryCollection.ByAlias("offset");

            if (int.TryParse(val, out var offset))
            {
                contextController.ListParams.Offset = offset;
            }

        }
    }
}