﻿using Axon.Core.Api.EventHandlers.GoodData;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Phlex.Core.MessageBus.Extensions;

namespace Axon.Core.Api.Extensions;

public static class BusRegistrationConfiguratorExtensions
{
    public static void AddGoodDataEventHandlers(this IBusRegistrationConfigurator configurator, IConfiguration configuration)
    {
        configurator.AddEventHandler<AppUserUpdatedEventHandler>(configuration);
        configurator.AddEventHandler<AppRoleUpdatedEventHandler>(configuration);
        configurator.AddEventHandler<OrgUpdatedEventHandler>(configuration);
        configurator.AddEventHandler<OrgUserUpdatedEventHandler>(configuration);
        configurator.AddEventHandler<AppGroupUpdatedEventHandler>(configuration);
    }
}