﻿using System;
using System.Threading.Tasks;
using Axon.Core.Domain.Interfaces.Access;
using CommunityToolkit.Diagnostics;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;

namespace Axon.Core.Api.Attributes;

/// <inheritdoc cref="HasOrganisationPermissionBaseAttribute"/>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public class HasOrganisationPermissionsAttribute : HasOrganisationPermissionBaseAttribute
{
    private readonly string orgPathKey;
    private readonly bool extractFromQueryString;

    public HasOrganisationPermissionsAttribute(string permissionsKey,
        string appCodeNameUrlIdentifier = "appCodeName",
        string orgCodeNameUrlIdentifier = "orgCodeName",
        bool extractFromQueryString = false,
        bool useTargetApplicationPermissions = false) : base(permissionsKey, appCodeNameUrlIdentifier, useTargetApplicationPermissions)
    {
        Guard.IsNotNullOrEmpty(orgCodeNameUrlIdentifier); 
        this.orgPathKey = orgCodeNameUrlIdentifier;
        this.extractFromQueryString = extractFromQueryString;
    }

    protected override string GetOrgIdentifier(AuthorizationFilterContext context)
    {
        if (extractFromQueryString)
        {
            return context.HttpContext.Request.Query[orgPathKey];
        }
        return context.RouteData.Values[orgPathKey] as string;
    }

    protected override async Task<bool> PerformPermissionCheck(IAccessGate accessGate, ILogger logger, string appCodeName, string orgIdentifier, string permission)
    {
        if (!await accessGate.IsAuthorisedForOrganisation(appCodeName, orgIdentifier, permission))
        {
            logger.LogDebug("Auth failed: For user, permission (`{Permission}`) against org: `{OrgIdentifier}` and app: `{AppCodeName}`",  permission, orgIdentifier, appCodeName);
            return false;
        }

        return true;
    }
}