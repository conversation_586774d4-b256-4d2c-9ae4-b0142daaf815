﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Infrastructure.Extensions;
using Axon.Core.Shared.Api;
using System;

namespace Axon.Core.Api.Queries.AppAccess.OrganisationGroupsQuery;

internal class GetOrganisationGroupsQueryHandler : IRequestHandler<GetOrganisationGroupsQueryRequest, CommandResponse<ApiPagedListResult<OrganisationGroupModel>>>
{
    private readonly IOrganisationRepository organisationRepository;
    private readonly IGroupRepository groupRepository;
    private readonly IAccessRepository accessRepository;

    public GetOrganisationGroupsQueryHandler(IOrganisationRepository organisationRepository, IGroupRepository groupRepository, IAccessRepository accessRepository)
    {
        this.organisationRepository = organisationRepository;
        this.groupRepository = groupRepository;
        this.accessRepository = accessRepository;
    }
    public async Task<CommandResponse<ApiPagedListResult<OrganisationGroupModel>>> Handle(GetOrganisationGroupsQueryRequest request, CancellationToken cancellationToken)
    {
        var organisation = await organisationRepository.GetItemByCodeNameAsync(request.OrgCodeName);
        if (organisation == null)
            return CommandResponse<ApiPagedListResult<OrganisationGroupModel>>.NotFound(nameof(OrganisationModel), request.OrgCodeName);

        var groupEntities = await groupRepository.GetForOrganisationAsync(organisation.CodeName);

        var filteredGroupEntities = groupEntities.AsQueryable().FilteredResult(request.ListParams.Filter);

        if (!request.ListParams.OrderBy.Any())
            request.ListParams.OrderBy = OrderByClauses.Parse(nameof(GroupEntity.Name));
        var pagedGroupEntities = filteredGroupEntities
            .OrderedResult(request.ListParams.OrderBy)
            .PagedResult(request.ListParams.Offset, request.ListParams.Limit);

        var organisationGroups = new List<OrganisationGroupModel>();
        foreach (var groupEntity in pagedGroupEntities)
        {
            var accessEntities = await accessRepository.GetAccessItemsForGroupAsync(groupEntity.Id, AccessType.GroupAccess);

            var users = accessEntities
                .Where(accessEntity => accessEntity.RoleId == null)
                .Select(accessEntity => new OrganisationGroupUserModel
                {
                    UserId = accessEntity.User.Id,
                    Email = accessEntity.User.Email,
                    Name = accessEntity.User.Name,
                })
                .ToArray();

            organisationGroups.Add(new OrganisationGroupModel
            {
                Id = groupEntity.Id,
                Name = groupEntity.Name,
                Users = users,
                CreatedAt = groupEntity.CreatedAt == DateTime.MinValue ? null : groupEntity.CreatedAt,
                LastUpdatedDate = groupEntity.LastUpdatedDate == DateTime.MinValue ? null : groupEntity.LastUpdatedDate
            });
        }

        return CommandResponse<ApiPagedListResult<OrganisationGroupModel>>.Data(
            new ApiPagedListResult<OrganisationGroupModel>(
                organisationGroups,
                request.ListParams.Offset,
                request.ListParams.Limit,
                filteredGroupEntities.Count()));
    }
}