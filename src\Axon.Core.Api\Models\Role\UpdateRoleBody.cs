﻿using Microsoft.AspNetCore.Mvc;

namespace Axon.Core.Api.Models.Role
{
    public class UpdateRoleBody
    {
        [FromRout<PERSON>, BindProperty(Name = "orgCodeName")]
        public string OrganisationCodeName { get; set; }
        [FromRoute, BindProperty(Name = "appCodeName")]
        public string AppCodeName { get; set; }
        [FromBody]
        public RoleBody RoleBody { get; set; }
    }
}
