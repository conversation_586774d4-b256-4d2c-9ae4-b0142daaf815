﻿using Axon.Core.Domain.Interfaces.Persistence;
using System.Linq;
using System.Threading.Tasks;

namespace Axon.Core.Api.Services.OrganisationGroup
{
    public class OrganisationGroupSynchroniser : IOrganisationGroupSynchroniser
    {
        private readonly IGroupRepository groupRepository;
        private readonly IAppGroupRepository appGroupRepository;
        public OrganisationGroupSynchroniser(
            IGroupRepository groupRepository,
            IAppGroupRepository appGroupRepository)
        {
            this.groupRepository = groupRepository;
            this.appGroupRepository = appGroupRepository;
        }

        public async Task SynchroniseGroupRenameWithAppGroups(string groupId)
        {
            var orgGroup = await groupRepository.GetItemAsync(groupId);

            var appGroups = await appGroupRepository.GetAppGroupsByGroupIdAsync(groupId);

            if(appGroups != null)
            {
                foreach (var existingAppGroup in appGroups)
                {
                    existingAppGroup.GroupName = orgGroup.Name;
                    await appGroupRepository.UpdateItemAsync(existingAppGroup.Id, existingAppGroup);
                }
            }
        }
    }
}
