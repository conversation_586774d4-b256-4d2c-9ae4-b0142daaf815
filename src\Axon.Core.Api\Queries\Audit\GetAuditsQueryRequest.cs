﻿using System.Net.Http.Json;
using Axon.Core.Api.Commands;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace Axon.Core.Api.Queries.Audit
{
    public class GetAuditsQueryRequest : IRequest<CommandResponse>
    {
        public string OrgCodeName { get; }
        public string AppCodeName { get; }
        public QueryString ODataOptions { get; }
        public string Host { get; }

        public GetAuditsQueryRequest(string orgCodeName, string appCodeName,string host, QueryString oDataOptions)
        {
            OrgCodeName = orgCodeName;
            AppCodeName = appCodeName;
            ODataOptions = oDataOptions;
            Host = host;
        }
    }
}
