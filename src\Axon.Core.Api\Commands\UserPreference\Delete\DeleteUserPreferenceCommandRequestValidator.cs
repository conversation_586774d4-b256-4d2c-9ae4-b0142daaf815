﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.UserPreference.Delete;

[UsedImplicitly]
public class DeleteUserPreferenceCommandRequestValidator : AbstractValidator<DeleteUserPreferenceCommandRequest>
{
    public DeleteUserPreferenceCommandRequestValidator()
    {
        RuleFor(x => x.Key)
            .MustBeAValidPreferencesKey();
    }
}