/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// ValidateCurrentUserPermissionsAccessQuery
    /// </summary>
    [DataContract(Name = "ValidateCurrentUserPermissionsAccessQuery")]
    public partial class ValidateCurrentUserPermissionsAccessQuery : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ValidateCurrentUserPermissionsAccessQuery" /> class.
        /// </summary>
        /// <param name="permissions">permissions.</param>
        /// <param name="appCodeName">appCodeName.</param>
        /// <param name="orgCodeName">orgCodeName.</param>
        public ValidateCurrentUserPermissionsAccessQuery(List<string> permissions = default(List<string>), string appCodeName = default(string), string orgCodeName = default(string))
        {
            this.Permissions = permissions;
            this.AppCodeName = appCodeName;
            this.OrgCodeName = orgCodeName;
        }

        /// <summary>
        /// Gets or Sets Permissions
        /// </summary>
        [DataMember(Name = "permissions", EmitDefaultValue = true)]
        public List<string> Permissions { get; set; }

        /// <summary>
        /// Gets or Sets AppCodeName
        /// </summary>
        [DataMember(Name = "appCodeName", EmitDefaultValue = true)]
        public string AppCodeName { get; set; }

        /// <summary>
        /// Gets or Sets OrgCodeName
        /// </summary>
        [DataMember(Name = "orgCodeName", EmitDefaultValue = true)]
        public string OrgCodeName { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class ValidateCurrentUserPermissionsAccessQuery {\n");
            sb.Append("  Permissions: ").Append(Permissions).Append("\n");
            sb.Append("  AppCodeName: ").Append(AppCodeName).Append("\n");
            sb.Append("  OrgCodeName: ").Append(OrgCodeName).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
