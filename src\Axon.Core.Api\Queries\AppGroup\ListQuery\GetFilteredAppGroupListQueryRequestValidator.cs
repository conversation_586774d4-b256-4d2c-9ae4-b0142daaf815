﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.AppGroup.ListQuery;

[UsedImplicitly]
public class GetFilteredAppGroupListQueryRequestValidator : AbstractValidator<GetFilteredAppGroupListQueryRequest>
{
    public GetFilteredAppGroupListQueryRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
    }
}