﻿using System;
using Axon.Core.Domain.Interfaces.Auth;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using MediatR;
using Microsoft.Extensions.Logging;
using Phlex.Core.GoodData.Interfaces;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Api.Commands.Authentication.GoodData;

internal class GetGoodDataUserTokenCommandHandler : IRequestHandler<GetGoodDataUserTokenCommand, CommandResponse>
{
    private readonly IIdentityProviderRepository identityProviderRepository;
    private readonly IGoodData goodDataService;
    private readonly IUserRequestContext userRequestContext;
    private readonly ILogger<GetGoodDataUserTokenCommandHandler> logger;

    public GetGoodDataUserTokenCommandHandler(IIdentityProviderRepository identityProviderRepository, IGoodData goodDataService, IUserRequestContext userRequestContext,
        ILogger<GetGoodDataUserTokenCommandHandler> logger)
    {
        Guard.IsNotNull(identityProviderRepository);
        this.identityProviderRepository = identityProviderRepository;

        Guard.IsNotNull(goodDataService);
        this.goodDataService = goodDataService;

        Guard.IsNotNull(userRequestContext);
        this.userRequestContext = userRequestContext;

        Guard.IsNotNull(logger);
        this.logger = logger;
    }

    public async Task<CommandResponse> Handle(GetGoodDataUserTokenCommand request, CancellationToken cancellationToken)
    {
        var emailAddress = !userRequestContext.IsApplication() ? userRequestContext.GetEmailAddress() : throw new InvalidOperationException("User email address cannot be found");

        var identityProviderId = userRequestContext.GetUserIdentityProviderId();
        var identityProvider = await identityProviderRepository.GetItemAsync(identityProviderId);
        if (identityProvider == null)
        {
            logger.LogWarning("Users identity provider: {IdentityProviderId} could not be found", identityProviderId);
            return CommandResponse.NotFound("IdentityProvider", identityProviderId);
        }

        var tokenDuration = identityProvider.AutoLogoutTime ?? 20;
        var tokenResult = await goodDataService.GetUserTokenAsync(emailAddress, tokenDuration);

        if (!tokenResult.UserExists)
        {
            return CommandResponse.NotFound(nameof(emailAddress), emailAddress);
        }

        return CommandResponse.Data(tokenResult.Token);
    }
}
