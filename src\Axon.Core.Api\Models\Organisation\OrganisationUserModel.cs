﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.Organisation;

public class OrganisationUserModel
{
    [Required]
    public string Id { get; set; }
    [Required]
    public string UserId { get; set; }
    [Required]
    public string Name { get; set; }
    [Required]
    public string Email { get; set; }
    public DateTime? LastAccessed { get; set; }
    [Required]
    public string Status { get; set; }
    [Required]
    public string OrganisationName { get; set; }
    [Required]
    public string OrganisationCodeName { get; set; }
    public DateTime? CreatedAt { get; set; }
    [Required]
    public GroupModel[] Groups { get; set; }
    [Required]
    public ChildOrganisationModel[] ChildOrganisations { get; set; }

    public class GroupModel
    {
        [Required]
        public string Id { get; set; }
        [Required]
        public string Name { get; set; }
    }

    public class ChildOrganisationModel
    {
        [Required]
        public string OrganisationName { get; set; }
        [Required]
        public string OrganisationCodeName { get; set; }
        [Required]
        public GroupModel[] Groups { get; set; }
    }
}