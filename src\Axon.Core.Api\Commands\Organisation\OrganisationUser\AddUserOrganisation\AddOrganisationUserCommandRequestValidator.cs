﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser.AddUserOrganisation;

[UsedImplicitly]
public class AddOrganisationUserCommandRequestValidator : AbstractValidator<AddOrganisationUserCommandRequest>
{
    public AddOrganisationUserCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
    }
}