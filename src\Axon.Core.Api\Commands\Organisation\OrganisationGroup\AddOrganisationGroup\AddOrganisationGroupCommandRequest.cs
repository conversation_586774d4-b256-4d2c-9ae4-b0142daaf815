﻿using MediatR;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.AddOrganisationGroup;

public class AddOrganisationGroupCommandRequest : IRequest<CommandResponse>
{
    public string OrganisationCodeName { get; }
    public AddOrganisationGroupCommandRequestBody AddOrganisationGroupCommandRequestBody { get; }

    public AddOrganisationGroupCommandRequest(string organisationCodeName, AddOrganisationGroupCommandRequestBody addOrganisationGroupCommandRequestBody)
    {
        OrganisationCodeName = organisationCodeName;
        AddOrganisationGroupCommandRequestBody = addOrganisationGroupCommandRequestBody;
    }
}
