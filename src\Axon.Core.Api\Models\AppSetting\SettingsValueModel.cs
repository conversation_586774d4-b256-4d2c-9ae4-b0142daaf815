﻿using System.ComponentModel.DataAnnotations;
using Axon.Core.Domain.Enums;
using JetBrains.Annotations;

namespace Axon.Core.Api.Models.AppSetting
{
    public class SettingsValueModel
    {
        public SettingsValueModel(string settingName, string category, string displayName, string description, SettingDataType dataType, object value, [CanBeNull] string[] dataTypeOptions, bool isKeyVault, bool isMultiple, bool isPublic)
        {
            SettingName = settingName;
            Category = category;
            DisplayName = displayName;
            Description = description;
            DataType = dataType;
            Value = value;
            DataTypeOptions = dataTypeOptions;
            IsKeyVault = isKeyVault;
            IsMultiple = isMultiple;
            IsPublic = isPublic;
        }
        [Required]
        public string SettingName { get; }
        [Required]
        public string Category { get; }
        [Required]
        public string DisplayName { get; }
        [Required]
        public string Description { get; }
        [Required]
        public SettingDataType DataType { get; }
        [Required]
        public object Value { get; }
        [CanBeNull] public string[] DataTypeOptions { get; }
        [Required]
        public bool IsKeyVault { get; }
        [Required]
        public bool IsMultiple { get; }
        [Required]
        public bool IsPublic { get; }
    }
}
