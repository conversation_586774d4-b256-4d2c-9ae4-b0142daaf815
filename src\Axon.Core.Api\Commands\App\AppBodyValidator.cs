using Axon.Core.Api.Validators;
using Axon.Core.Domain.Enums;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.App;

[UsedImplicitly]
public class AppBodyValidator : BaseCommandValidator<AppBody>
{
    public AppBodyValidator() 
    {
        RuleFor(a => a.DisplayName)
            .MustBeAValidDisplayName();

        RuleFor(a => a.Description)
            .NotEmpty().WithMessage("{PropertyName} cannot be empty")
            .MustBeAValidDescription();

        RuleFor(x => x.Icon)
            .MustBeAValidUri();

        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();

        RuleFor(x => x.ClientId)
            .MustBeAValidGuidOrNa();

        RuleFor(x => x.RunAs)
            .MustBeAValidEnum(typeof(AppRunAs));
    }
}