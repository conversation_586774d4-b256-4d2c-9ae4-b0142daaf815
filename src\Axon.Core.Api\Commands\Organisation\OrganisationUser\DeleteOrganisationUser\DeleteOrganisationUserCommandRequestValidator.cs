﻿using Axon.Core.Api.Validators;
using FluentValidation;

namespace Axon.Core.Api.Commands.Organisation.OrganisationUser.DeleteOrganisationUser;

public class DeleteOrganisationUserCommandRequestValidator : AbstractValidator<DeleteOrganisationUserCommandRequest>
{
    public DeleteOrganisationUserCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();

        RuleFor(x => x.UserId)
            .MustBeAValidGuid();
    }
}
