﻿using System;
using System.Threading.Tasks;
using Axon.Core.Contracts;
using Axon.Core.Domain.Interfaces.Persistence;
using MassTransit;

namespace Axon.Core.Api.EventHandlers;

public class ScopeResourceNameChangedEvenHandler : IConsumer<ScopeResourceNameChangedEvent>
{
    public async Task Consume(ConsumeContext<ScopeResourceNameChangedEvent> context)
    {
        //Future piece of work will decide what needs to happen to permissions using scopes etc when they are modified
        throw new NotImplementedException();
    }
}