/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Axon.Core.Api.Sdk.NetCore.Client;
using Axon.Core.Api.Sdk.NetCore.Model;

namespace Axon.Core.Api.Sdk.NetCore.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppAccessApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <returns>StringIEnumerableCommandResponse</returns>
        StringIEnumerableCommandResponse GetAppRoles(string appCodeName);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <returns>ApiResponse of StringIEnumerableCommandResponse</returns>
        ApiResponse<StringIEnumerableCommandResponse> GetAppRolesWithHttpInfo(string appCodeName);
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppAccessApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of StringIEnumerableCommandResponse</returns>
        System.Threading.Tasks.Task<StringIEnumerableCommandResponse> GetAppRolesAsync(string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (StringIEnumerableCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<StringIEnumerableCommandResponse>> GetAppRolesWithHttpInfoAsync(string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppAccessApi : IAppAccessApiSync, IAppAccessApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class AppAccessApi : IDisposable, IAppAccessApi
    {
        private Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="AppAccessApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public AppAccessApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppAccessApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public AppAccessApi(string basePath)
        {
            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppAccessApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public AppAccessApi(Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppAccessApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppAccessApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppAccessApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppAccessApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppAccessApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppAccessApi(HttpClient client, Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppAccessApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public AppAccessApi(Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient client, Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient asyncClient, Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <returns>StringIEnumerableCommandResponse</returns>
        public StringIEnumerableCommandResponse GetAppRoles(string appCodeName)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<StringIEnumerableCommandResponse> localVarResponse = GetAppRolesWithHttpInfo(appCodeName);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <returns>ApiResponse of StringIEnumerableCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<StringIEnumerableCommandResponse> GetAppRolesWithHttpInfo(string appCodeName)
        {
            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppAccessApi->GetAppRoles");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<StringIEnumerableCommandResponse>("/v1/App/{appCodeName}/roles", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppRoles", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of StringIEnumerableCommandResponse</returns>
        public async System.Threading.Tasks.Task<StringIEnumerableCommandResponse> GetAppRolesAsync(string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<StringIEnumerableCommandResponse> localVarResponse = await GetAppRolesWithHttpInfoAsync(appCodeName, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (StringIEnumerableCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<StringIEnumerableCommandResponse>> GetAppRolesWithHttpInfoAsync(string appCodeName, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppAccessApi->GetAppRoles");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<StringIEnumerableCommandResponse>("/v1/App/{appCodeName}/roles", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppRoles", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
