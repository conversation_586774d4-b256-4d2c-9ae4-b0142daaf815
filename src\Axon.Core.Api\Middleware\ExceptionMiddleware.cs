﻿using Axon.Core.Domain.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Phlex.Core.Api.Abstractions;
using Phlex.Core.Api.Abstractions.Models;
using System;
using System.IO;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;

namespace Axon.Core.Api.Middleware
{
    /// <summary>
    /// Global error handler to prevent internal stack trace from being returned to caller.
    /// </summary>
    public class ExceptionMiddleware
    {
        private readonly IHostEnvironment env;
        private readonly RequestDelegate next;

        /// <summary>
        /// Global error handler to prevent internal stack trace from being returned to caller.
        /// </summary>
        public ExceptionMiddleware(RequestDelegate next, IHostEnvironment env)
        {
            this.next = next;
            this.env = env;
        }

        /// <summary>
        /// Invokes the next middleware and returns an <see cref="ErrorResult" /> if an exception occurs.
        /// </summary>
        public async Task InvokeAsync(HttpContext httpContext, ILogger<ExceptionMiddleware> logger)
        {
            try
            {
                await next(httpContext);
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(httpContext, ex, env, logger);
            }
        }

        private static Task HandleExceptionAsync(HttpContext context, Exception exception, IHostEnvironment env,
            ILogger logger)
        {
            context.Response.StatusCode = GetStatusCode(exception);
            context.Response.ContentType = "application/json";
            
            var errorResult = GetErrorMessageWithErrorId(exception, env, logger);

            return context.Response.WriteAsync(JsonSerializer.Serialize(errorResult));
        }

        private static int GetStatusCode(Exception exception)
        {
            return exception switch
            {
                EntityNotFoundException => (int) HttpStatusCode.NotFound,
                BadRequestException => (int) HttpStatusCode.BadRequest,
                UnauthorizedAccessException => (int)HttpStatusCode.Unauthorized,
                TooManyRequestsException => (int)HttpStatusCode.TooManyRequests,
                _ => (int) HttpStatusCode.InternalServerError
            };
        }

        private static ErrorResult GetErrorMessageWithErrorId(Exception exception, IHostEnvironment env, ILogger logger)
        {
            var errorId = Guid.NewGuid().ToString().Substring(0, 8).ToUpper();
            logger.LogError(exception, $"Unhandled error occurred. ErrorID: {errorId}");

            return exception switch
            {
                EntityNotFoundException => new ErrorResult(ErrorTypes.RESOURCE_NOT_FOUND, $"ErrorID: {errorId}"),
                UnauthorizedAccessException => new ErrorResult(ErrorTypes.NOT_AUTHORIZED, exception.Message),
                BadRequestException => new ErrorResult(ErrorTypes.INVALID_REQUEST, exception.Message),
                InvalidDataException => new ErrorResult(ErrorTypes.INVALID_REQUEST, exception.Message),
                _ => env.IsDevelopment()
                    ? new ErrorResult(ErrorTypes.INTERNAL_ERROR, $"Internal Server Error. ErrorID: {errorId}. {exception.Message}", exception.ToString())
                    : new ErrorResult(ErrorTypes.INTERNAL_ERROR, $"Internal Server Error. ErrorID: {errorId}.")
            };
        }
    }
}