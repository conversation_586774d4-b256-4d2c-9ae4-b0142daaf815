﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.AppUser.ListQuery;

[UsedImplicitly]
public class GetFilteredAppUsersQueryRequestValidator : AbstractValidator<GetFilteredAppUsersQueryRequest>
{
    public GetFilteredAppUsersQueryRequestValidator()
    {
        RuleFor(o => o.OrganisationCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
        RuleFor(o => o.AppCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
    }
}