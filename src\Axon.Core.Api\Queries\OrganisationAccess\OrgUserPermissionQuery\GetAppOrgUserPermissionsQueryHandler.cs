﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.OrganisationAccess;
using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Models.Access;
using MediatR;

namespace Axon.Core.Api.Queries.OrganisationAccess.OrgUserPermissionQuery;

internal class GetAppOrgUserPermissionsQueryHandler : IRequestHandler<GetAppOrgUserPermissionsQueryRequest, CommandResponse<IEnumerable<AppOrganisationPermission>>>
{
    private readonly IAccessService accessService;

    public GetAppOrgUserPermissionsQueryHandler(IAccessService accessService)
    {
        this.accessService = accessService;
    }

    public async Task<CommandResponse<IEnumerable<AppOrganisationPermission>>> Handle(GetAppOrgUserPermissionsQueryRequest request, CancellationToken cancellationToken)
    {
        var effectivePermissions = await accessService.GetEffectivePermissions(request.UserEmail, request.AppCodeName, request.OrganisationCodeName);

        var converted = GetPermissionSets(effectivePermissions);

        return CommandResponse<IEnumerable<AppOrganisationPermission>>.Data(converted);
    }

    private IEnumerable<AppOrganisationPermission> GetPermissionSets(UserEffectivePermissionsForAppAndOrg effectivePermisisons)
    {
        foreach(var permission in effectivePermisisons.EffectivePermissions.Permissions)
        {
            if (!permission.HasScopes)
            {
                yield return AppOrganisationPermission.CreateGeneralPermission(permission.Name,
                                                                               permission.Allow);
            }
            else
            {
                if (permission.ScopeSets == null || !permission.ScopeSets.Any())
                {
                    yield return AppOrganisationPermission.CreateScopedPermission(permission.Name, permission.Allow, null);
                }
                else
                {
                    foreach (var scopeSet in permission.ScopeSets)
                    {
                        yield return AppOrganisationPermission.CreateScopedPermission(permission.Name,
                                                                                      permission.Allow,
                                                                                      scopeSet.Scopes.Select(sc => new AppOrganisationPermissionScope(sc.Scope,
                                                                                                                             true,
                                                                                                                             sc.Resources?.Select(res => new AppOrganisationPermissionScopeResource(res.Name,
                                                                                                                                                                                                   res.Id)).ToList())).ToList());
                    }
                }
            }
        }
    }
}