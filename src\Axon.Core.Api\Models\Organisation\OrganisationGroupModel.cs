﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.Organisation;

public class OrganisationGroupModel
{
    [Required]
    public string Id { get; set; }
    [Required]
    public string Name { get; set; }
    [Required]
    public OrganisationGroupUserModel[] Users { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? LastUpdatedDate { get; set; }
}

public class OrganisationGroupUserModel
{
    [Required]
    public string UserId { get; set; }
    [Required]
    public string Name { get; set; }
    [Required]
    public string Email { get; set; }
}