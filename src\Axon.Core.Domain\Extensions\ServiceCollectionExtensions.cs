using Axon.Core.Domain.Interfaces.Access;
using Axon.Core.Domain.Services.Access;
using Microsoft.Extensions.DependencyInjection;
namespace Axon.Core.Domain.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddAccessServices(this IServiceCollection services)
        {
            services.AddSingleton<IAccessService, AccessService>();
            services.AddSingleton<IEffectivePermissionsCalculator, EffectivePermissionCalculator>();
            services.AddSingleton<IPermissionMerger, PermissionMerger>();

            services.AddScoped<IAccessLevelProvider, AccessLevelProvider>();
            services.AddScoped<IAccessGate, AccessGate>();
            services.AddScoped<IAppPrincipalAccessGate, AppPrincipalAccessGate>();
            services.AddScoped<IContextUserAccessGate, ContextUserAccessGate>();
            services.AddScoped<ITargetUserAccessGate, UserAccessGate>();
            services.AddScoped<IRoleSelector, RoleSelector>();

            return services;
        }
    }
}
