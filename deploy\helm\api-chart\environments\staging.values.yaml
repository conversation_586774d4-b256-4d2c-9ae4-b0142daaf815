api:
  tag: latest

grpc:
  tag: latest

ingress:
  tls:
    - tlsSecretName: tls-app-staging-smartphlex-com
      hosts:
        - app-staging.smartphlex.com
  hosts:
    - host: app-staging.smartphlex.com
      paths:
        - path: /api/core/(.*)
          pathType: ImplementationSpecific

scaledObject:
  # -- Minimum number of replicas that the scaled object will create
  minReplicas: 3
  # -- Maximum number of replicas that the scaled object will create
  maxReplicas: 10
  # -- This is the polling interval to check each trigger on for the Scaled object type. By default its 30 seconds.
  pollingInterval: 30
  # -- The period to wait after the last trigger reported active before scaling the deployment back to 0. By default it’s 5 minutes (300 seconds).
  cooldownPeriod: 300
  # -- The CPU percentage value to scale up on
  utilisation: 70

newrelic_api_app_name: axon-core-stg-api
newrelic_grpc_app_name: axon-core-stg-grpc
keyVaultName: axn-stg-kv-eun
clientId: 329eb7fa-bee5-4e15-b62f-5e785fe9eb2d
azureIssuer: https://login.microsoftonline.com/common/v2.0
azureUseCustomRefresh: false


gigyaClientId: uRP9BL8uRN3LbswaSIReRrsd
gigyaIssuer: https://stg.aaas.cencora.com/oidc/op/v1.0/4_pgDlm4GH1hYhGHvA0F0tVw/authorize
gigyaUseCustomRefresh: true

corsOriginUrl0: https://app-staging.smartphlex.com
corsOriginUrl1: https://localhost:4000

BlobStorageConnectionString: https://phcgvsharedstaticeun.blob.core.windows.net/

NamespaceName: "axn-stg-servicebus-eun"
cosmosdbName: Axon-Core-ApiDb
cosmosdbUrl: 'https://axn-stg-cosmos-eun.documents.azure.com:443/'

managedIdentityClientId: 7f94bece-8469-4621-897f-749a9350fb1b
azureWorkload:
  clientId: 7f94bece-8469-4621-897f-749a9350fb1b
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AzureBlobStorageContainersAppAvatarsFolderPrefix: stg/axon-avatar
AzureBlobStorageContainersOrganisationAvatarsFolderPrefix: stg/organisations
AzureBlobStorageContainersThemesFolderPrefix: stg/axon-theme

DataProtectionBlobStorageUri: 'https://axnstgstorageeun.blob.core.windows.net/'
DataProtectionKeyVaultKey: 'https://axn-stg-kv-eun.vault.azure.net/keys/AxonDataProtection'

GoodDataBaseUri: 'https://phlexglobal-test.cloud.gooddata.com/'
GoodDataEnvironment: 'stg'