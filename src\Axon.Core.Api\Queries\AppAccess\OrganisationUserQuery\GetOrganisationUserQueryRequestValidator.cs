﻿using System;
using System.Collections.Generic;
using System.Linq;
using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.AppAccess.OrganisationUserQuery;

[UsedImplicitly]
public class GetOrganisationUserQueryRequestValidator : AbstractValidator<GetOrganisationUserQueryRequest>
{
    public GetOrganisationUserQueryRequestValidator()
    {
        RuleFor(x => x.OrgCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.UserId)
            .MustBeAValidGuid();
        RuleFor(o => o.Embed)
            .Must(embed =>
            {
                if (string.IsNullOrWhiteSpace(embed))
                    return true;

                var embedOptions = embed.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
                return embedOptions.TrueForAll(embedOption =>
                    embedOption.Equals("groups", StringComparison.OrdinalIgnoreCase) || embedOption.Equals("childOrganisations", StringComparison.OrdinalIgnoreCase));
            })
            .WithMessage("Valid embed options are: 'groups' and 'childOrganisations'.");
    }
}