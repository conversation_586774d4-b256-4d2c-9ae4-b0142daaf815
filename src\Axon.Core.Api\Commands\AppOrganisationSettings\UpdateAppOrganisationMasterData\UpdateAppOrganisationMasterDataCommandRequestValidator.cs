﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.AppMasterData.UpdateAppMasterData;

[UsedImplicitly]
public class UpdateAppOrganisationMasterDataCommandRequestValidator : AbstractValidator<UpdateAppOrganisationMasterDataCommandRequest>
{
    public UpdateAppOrganisationMasterDataCommandRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.MasterDataType)
            .MustBeAValidSettingName();
        RuleFor(x => x.Selected)
            .NotNull().WithMessage("Selected cannot be null");
    }
}