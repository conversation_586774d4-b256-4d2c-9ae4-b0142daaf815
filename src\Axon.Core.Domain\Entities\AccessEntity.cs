﻿using Axon.Core.Domain.Entities.Base;
using Axon.Core.Domain.Enums;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
namespace Axon.Core.Domain.Entities
{
    public sealed class AccessEntity : BaseEntity
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public AccessType AccessType { get; set; }
        public string OrganisationId { get; set; }
        public string OrganisationCodeName { get; set; }
        public string AppId { get; set; }
        public string AppCodeName { get; set; }
        public string GroupId { get; set; }
        public string RoleId { get; set; }
        public AccessUser User { get; set; }
        public RoleEntity.ScopeResources[] Scopes { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public sealed class AccessUser
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
    }
}
