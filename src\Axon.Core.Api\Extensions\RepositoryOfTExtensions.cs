using System.Threading.Tasks;
using Axon.Core.Domain.Entities.Base;
using Axon.Core.Domain.Exceptions;
using Axon.Core.Domain.Interfaces.Persistence;

namespace Axon.Core.Api.Extensions
{
    public static class RepositoryOfTExtensions
    {
        public static async Task<TEntity> GetItemOrThrowAsync<TEntity>(this IRepository<TEntity> repo, string id) 
            where TEntity : BaseEntity
        {
            var entity = await repo.GetItemAsync(id);

            if (entity == null) throw new EntityNotFoundException(typeof(TEntity).Name, id);

            return entity;
        }

        public static Task<bool> TryGetItemAsync<TEntity>(this IRepository<TEntity> repo, string id, out TEntity entity)
            where TEntity : BaseEntity
        {
            entity = repo.GetItemAsync(id).Result;

            return Task.FromResult(entity != null);
        }
    }
}