﻿using Axon.Core.Domain.Models.Access;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Axon.Core.Domain.Extensions
{
    public static class UserEffectivePermissionsExtensions
    {
        /// <summary>
        /// Determines if the user has the provided permission as allowed
        /// </summary>
        /// <param name="permissions">The users effective permissions</param>
        /// <param name="permissionName">The permission name to check</param>
        /// <returns>True if the user has the permission provided, False if they do not</returns>
        public static bool HasPermission(this UserEffectivePermissions permissions, string permissionName)
        {
            var permission = permissions.Permissions.SingleOrDefault(x => x.Name.Equals(permissionName, StringComparison.OrdinalIgnoreCase));
            return permission != null && permission.Allow;
        }

        /// <summary>
        /// Determines if the user has at least one of the provided permissions as allowed
        /// </summary>
        /// <param name="permissions">The users effective permissions</param>
        /// <param name="targetPermissions">The permission name to check</param>
        /// <returns>True if the user has one of the provided permissions set as allowed, False if they do not</returns>
        public static bool HasPermissionFromSet(this UserEffectivePermissions permissions, IEnumerable<string> targetPermissions)
        {
            return permissions.Permissions.Any(p => targetPermissions.Any(tgt => tgt.Equals(p.Name, StringComparison.OrdinalIgnoreCase) && p.Allow));
        }

        /// <summary>
        /// Determines if the user has any role assigned to the application
        /// </summary>
        /// <param name="permissions">The users effective permissions</param>
        /// <returns>True if the user has any role, False if they do not</returns>
        public static bool HasRole(this UserEffectivePermissions permissions)
        {
            return permissions.Roles.Any();
        }
    }
}
