﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Queries.Organisation;
using Axon.Core.Api.Services.Authorisation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Axon.Core.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("v{version:apiVersion}/Tenant")]
[ProducesResponseType(401, Type = typeof(CommandResponse))]
[ProducesResponseType(500, Type = typeof(CommandResponse))]
[Authorize]

public class TenantController : ApiControllerBase
{
    public TenantController(IMediator mediator) : base(mediator)
    {
    }

    [HttpGet("tenants", Name = "GetAll")]
    [HasPermissions(nameof(CorePermissions.ViewTenant))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<string>>))]
    public async Task<IActionResult> GetAllAsync()
    {
        return await Send(new GetAllTenantsQueryRequest());
    }
}