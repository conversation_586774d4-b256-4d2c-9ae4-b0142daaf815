[{"id": "f8d87fd3-d65f-445f-97f8-0d8aefd537eb", "AppCodeName": "permission-test-app-2", "OrganisationCodeName": "permission-test-org", "GroupId": "9068ec30-9f9b-4e5a-8ebb-d1a0d38df5b5", "GroupName": "Group test-1", "RoleId": "37e7a11d-e631-4432-b58b-bcf973084f88", "Role": "System.Administrator", "CreatedAt": "2024-06-19T11:50:17+0000", "LastUpdatedDate": "2024-06-19T11:50:17+0000"}, {"id": "5c169866-84b5-4446-9b3b-6d89dfd0d81e", "AppCodeName": "permission-test-app-2", "OrganisationCodeName": "permission-test-org", "GroupId": "da53fb57-1457-42b6-a998-2eb8269bc9a3", "GroupName": "Group test-2", "RoleId": "1754c798-5882-4fcd-9423-19aee26fff27", "Role": "Custom role", "CreatedAt": "2024-06-19T11:50:17+0000", "LastUpdatedDate": "2024-06-19T11:50:17+0000"}, {"id": "00371bad-983f-43ab-b8ee-fdc6afa2711c", "AppCodeName": "data-explorer", "OrganisationCodeName": "phlexglobal", "GroupId": "95d99b16-e86a-48d1-883b-fa0271392c2d", "GroupName": "Group 1", "RoleId": "4bf678ba-43ce-44dd-9fa9-d614094b430b", "Role": "System.Administrator", "CreatedAt": "2024-06-19T12:50:17+01:00", "LastUpdatedDate": "2024-06-19T12:50:17+01:00"}, {"id": "561abaa8-cacf-4f1f-b886-7b5291860f97", "AppCodeName": "permission-test-app", "OrganisationCodeName": "permission-test-org", "GroupId": "397cefe4-1be8-4f17-82e3-759be75c377d", "GroupName": "Update tests", "RoleId": "05a6f52c-bde9-49a6-a9a7-d34d25e3def9", "Role": "System.Administrator", "CreatedAt": "2024-06-19T11:50:17+0000", "LastUpdatedDate": "2024-06-19T11:50:17+0000"}, {"id": "604e0b51-0df7-46ab-8218-e46be5c4f87f", "AppCodeName": "permission-test-app", "OrganisationCodeName": "permission-test-org", "GroupId": "773e4ee5-1601-4d0e-a76b-d0991cf2e9dc", "GroupName": "Delete tests", "RoleId": "7f2a5147-13fb-4685-9177-2d37b0a6afa2", "Role": "CustomRole1", "CreatedAt": "2024-06-19T11:50:17+0000", "LastUpdatedDate": "2024-06-19T11:50:17+0000"}, {"id": "88d58c9f-c6f7-4c73-b26a-f594a47081a8", "AppCodeName": "permission-test-app-2", "OrganisationCodeName": "permission-test-org", "GroupId": "86db2d52-9a48-44cb-ac69-93c9b20188bf", "GroupName": "Delete role tests", "RoleId": "9b215d4f-f565-4aa1-9f0a-f82f124a73c9", "Role": "Custom role for delete", "CreatedAt": "2024-06-19T11:50:17+0000", "LastUpdatedDate": "2024-06-19T11:50:17+0000"}, {"id": "c2b7a052-631c-4abd-84f2-f17bee8b7259", "AppCodeName": "permission-test-app-2", "OrganisationCodeName": "remove-permission-test-org", "GroupId": "86db2d52-9a48-44cb-ac69-93c9b20188bf", "GroupName": "Delete role tests", "RoleId": "9b215d4f-f565-4aa1-9f0a-f82f124a73c9", "Role": "Custom role for delete", "CreatedAt": "2024-06-19T11:50:17+0000", "LastUpdatedDate": "2024-06-19T11:50:17+0000"}, {"id": "5e5b7c07-598b-404d-8223-97040d989551", "AppCodeName": "query-group-test-app", "OrganisationCodeName": "permission-test-org", "GroupId": "9068ec30-9f9b-4e5a-8ebb-d1a0d38df5b5", "GroupName": "Group test-1", "RoleId": "37e7a11d-e631-4432-b58b-bcf973084f88", "Role": "System.Administrator", "CreatedAt": "2024-06-19T11:50:17+0000", "LastUpdatedDate": "2024-06-19T11:50:17+0000"}, {"id": "f68ae454-bd0a-4899-9a64-c78217a5f453", "AppCodeName": "query-group-test-app", "OrganisationCodeName": "permission-test-org", "GroupId": "da53fb57-1457-42b6-a998-2eb8269bc9a3", "GroupName": "Group test-2", "RoleId": "1754c798-5882-4fcd-9423-19aee26fff27", "Role": "Custom role", "CreatedAt": "2024-06-19T11:50:17+0000", "LastUpdatedDate": "2024-06-19T11:50:17+0000"}]