﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Services.UserOrganisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Enums;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using CommunityToolkit.Diagnostics;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.AddOrganisationGroupUser;

internal class AddOrganisationGroupUserCommandHandler : BaseCommandHandler<AccessEntity, AddOrganisationGroupUserCommandRequest, CommandResponse>
{
    private readonly IAccessRepository accessRepository;
    private readonly IOrganisationRepository organisationRepository;
    private readonly IGroupRepository groupRepository;
    private readonly IUserRepository userRepository;
    private readonly IOrganisationUserManager organisationUserManager;
    private readonly IClientDetailsProvider clientDetailsProvider;
    private readonly ICorrelationIdProvider correlationIdProvider;

    public AddOrganisationGroupUserCommandHandler(
        IAccessRepository accessRepository,
        IMapper mapper,
        IMessageBus messageBus,
        IOrganisationRepository organisationRepository,
        IGroupRepository groupRepository,
        IUserRepository userRepository,
        IOrganisationUserManager organisationUserManager,
        IClientDetailsProvider clientDetailsProvider,
        ICorrelationIdProvider correlationIdProvider)
        : base(accessRepository, mapper, messageBus)
    {
        Guard.IsNotNull(accessRepository);
        this.accessRepository = accessRepository;
        Guard.IsNotNull(organisationRepository);
        this.organisationRepository = organisationRepository;
        Guard.IsNotNull(groupRepository);
        this.groupRepository = groupRepository;
        Guard.IsNotNull(userRepository);
        this.userRepository = userRepository;
        Guard.IsNotNull(organisationUserManager);
        this.organisationUserManager = organisationUserManager;
        Guard.IsNotNull(clientDetailsProvider);
        this.clientDetailsProvider = clientDetailsProvider;
        Guard.IsNotNull(correlationIdProvider);
        this.correlationIdProvider = correlationIdProvider;
    }

    public override async Task<CommandResponse> Handle(AddOrganisationGroupUserCommandRequest request, CancellationToken cancellationToken)
    {
        HashSet<string> checkForDuplicatesHashSet = new HashSet<string>();
        if (request.AddOrganisationGroupUserBody.UserIds.Any(r => !checkForDuplicatesHashSet.Add(r.ToLowerInvariant())))
        {
            return CommandResponse.BadRequest(nameof(request.AddOrganisationGroupUserBody.UserIds), "Duplicate userIds are not allowed");
        }

        var organisation = await organisationRepository.GetItemByCodeNameAsync(request.OrganisationCodeName);
        if (organisation == null)
            return CommandResponse.NotFound(nameof(OrganisationEntity), request.OrganisationCodeName);

        var group = await groupRepository.GetItemAsync(request.GroupId);
        if (group == null)
            return CommandResponse.NotFound(nameof(GroupEntity), request.GroupId);

        var usersToAdd = new List<UserEntity>();
        var orgAccessItems = await accessRepository.GetAccessItemsForOrganisationAsync(organisation.Id, AccessType.OrganisationAccess);
        var groupAccessItems = await accessRepository.GetAccessItemsForGroupAsync(request.GroupId, AccessType.GroupAccess);

        if (!request.AddOrganisationGroupUserBody.UserIds.Any())
            return CommandResponse.Success();

        foreach (var userId in request.AddOrganisationGroupUserBody.UserIds)
        {
            var existingUser = await userRepository.GetItemAsync(userId);
            if (existingUser == null)
                return CommandResponse.NotFound(nameof(UserEntity), userId);

            var existingOrgAccess = orgAccessItems.FirstOrDefault(x => string.Equals(x.User.Id, userId, StringComparison.InvariantCultureIgnoreCase));
            if (existingOrgAccess == null)
                return CommandResponse.Forbidden(nameof(UserEntity), userId);

            var existingGroupAccess = groupAccessItems.FirstOrDefault(x => string.Equals(x.User.Id, userId, StringComparison.OrdinalIgnoreCase));
            if (existingGroupAccess != null)
                return CommandResponse.Conflict(nameof(AccessEntity), userId, existingGroupAccess.GroupId);

            usersToAdd.Add(existingUser);
        }

        var clientDetails = clientDetailsProvider.Provide();
        var correlationId = correlationIdProvider.Provide();

        foreach (var user in usersToAdd)
        {
            await organisationUserManager.AssignUserToGroup(
                correlationId,
                clientDetails,
                organisation.Id,
                organisation.CodeName,
                user,
                group);
        }

        group.LastUpdatedDate = DateTime.UtcNow;
        await groupRepository.UpdateItemAsync(group.Id, group);

        return CommandResponse.Success();
    }
}
