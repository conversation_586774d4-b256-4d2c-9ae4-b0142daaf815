/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// ScopedResource
    /// </summary>
    [DataContract(Name = "ScopedResource")]
    public partial class ScopedResource : IEquatable<ScopedResource>, IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ScopedResource" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected ScopedResource() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="ScopedResource" /> class.
        /// </summary>
        /// <param name="scope">scope (required).</param>
        /// <param name="resources">resources (required).</param>
        public ScopedResource(string scope = default(string), List<Resource> resources = default(List<Resource>))
        {
            // to ensure "scope" is required (not null)
            if (scope == null)
            {
                throw new ArgumentNullException("scope is a required property for ScopedResource and cannot be null");
            }
            this.Scope = scope;
            // to ensure "resources" is required (not null)
            if (resources == null)
            {
                throw new ArgumentNullException("resources is a required property for ScopedResource and cannot be null");
            }
            this.Resources = resources;
        }

        /// <summary>
        /// Gets or Sets Scope
        /// </summary>
        [DataMember(Name = "scope", IsRequired = true, EmitDefaultValue = true)]
        public string Scope { get; set; }

        /// <summary>
        /// Gets or Sets Resources
        /// </summary>
        [DataMember(Name = "resources", IsRequired = true, EmitDefaultValue = true)]
        public List<Resource> Resources { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class ScopedResource {\n");
            sb.Append("  Scope: ").Append(Scope).Append("\n");
            sb.Append("  Resources: ").Append(Resources).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="input">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object input)
        {
            return this.Equals(input as ScopedResource);
        }

        /// <summary>
        /// Returns true if ScopedResource instances are equal
        /// </summary>
        /// <param name="input">Instance of ScopedResource to be compared</param>
        /// <returns>Boolean</returns>
        public bool Equals(ScopedResource input)
        {
            if (input == null)
            {
                return false;
            }
            return 
                (
                    this.Scope == input.Scope ||
                    (this.Scope != null &&
                    this.Scope.Equals(input.Scope))
                ) && 
                (
                    this.Resources == input.Resources ||
                    this.Resources != null &&
                    input.Resources != null &&
                    this.Resources.SequenceEqual(input.Resources)
                );
        }

        /// <summary>
        /// Gets the hash code
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                int hashCode = 41;
                if (this.Scope != null)
                {
                    hashCode = (hashCode * 59) + this.Scope.GetHashCode();
                }
                if (this.Resources != null)
                {
                    hashCode = (hashCode * 59) + this.Resources.GetHashCode();
                }
                return hashCode;
            }
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
