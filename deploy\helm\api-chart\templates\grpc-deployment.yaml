apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "axon-core-api.fullname" . }}-grpc
  labels:
    {{- include "axon-core-api.labels" . | nindent 4 }}
  annotations:
    checkov.io/skip1: CKV_K8S_21=The namespace is being defined as helm argument
    checkov.io/skip2: CKV_K8S_14=The tag is being defined as helm argument
    checkov.io/skip3: CKV_K8S_43=We cannot use digest since these are regenerated on every image
    checkov.io/skip4: CKV_K8S_8=There is no liveness probe for this app
    checkov.io/skip5: CKV_K8S_9=There is no readiness probe for this app
    checkov.io/skip6: CKV_K8S_35=There are no secrets defined as env vars
spec:
  replicas: {{ .Values.replicas }}
  selector:
    matchLabels:
      {{- include "axon-core-grpc.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.grpc.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "axon-core-grpc.selectorLabels" . | nindent 8 }}
        azure.workload.identity/use: 'true'
    spec:
      {{- with .Values.grpc.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      automountServiceAccountToken: false
      securityContext:
        {{- toYaml .Values.grpc.podSecurityContext | nindent 8 }}
      serviceAccountName: {{ include "axon-core-api.fullname" . }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.grpc.securityContext | nindent 12 }}
          image: "{{ .Values.grpc.repository }}:{{ .Values.grpc.tag }}"
          imagePullPolicy: {{ .Values.grpc.pullPolicy }}
          # TODO(sam): gRPC service needs liveness and readiness checks
          ports:
            - name: grpc
              containerPort: 9090
              protocol: TCP
          env:
            - name: NEW_RELIC_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  name: newreliclicensekey
                  key: KEY
            - name: NEW_RELIC_APP_NAME
              value: {{ .Values.newrelic_grpc_app_name }}
            - name: ASPNETCORE_ENVIRONMENT
              value: {{ .Values.aspNetCoreEnvironment }}
            - name: ASPNETCORE_URLS
              value: "http://+:9090"
            - name: KeyVaultName
              value: {{ .Values.keyVaultName }}
            - name: AzureAd__ClientId
              value: {{ .Values.clientId }}
            - name: Kestrel__EndpointDefaults__Protocols
              value: Http2
            - name: AzureIdentity__ManageIdentityClientId
              value: {{ .Values.managedIdentityClientId }}
            - name: EntraSettings__Issuer
              value: {{ .Values.azureIssuer }}
            - name: Gigya__ClientId
              value: {{ .Values.gigyaClientId }}
            - name: Gigya__Issuer
              value: {{ .Values.gigyaIssuer }}
            - name: AZURE_FEDERATED_TOKEN_FILE
              value: "/var/run/secrets/azure/tokens/azure-identity-token"       
            - name: ConnectionStrings__Axon-Core-ApiDb__EndpointUrl
              value:  {{ .Values.cosmosdbUrl }}                                
            - name: ConnectionStrings__Axon-Core-ApiDb__DatabaseName
              value:  {{ .Values.cosmosdbName }}
          resources:
            {{- toYaml .Values.grpc.resources | nindent 12 }}
      {{- with .Values.grpc.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.grpc.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.grpc.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
