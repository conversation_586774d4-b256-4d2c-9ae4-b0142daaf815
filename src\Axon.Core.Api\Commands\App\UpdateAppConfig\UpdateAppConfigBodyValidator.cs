using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.App.UpdateAppConfig;

[UsedImplicitly]
public class UpdateAppConfigBodyValidator : BaseCommandValidator<UpdateAppConfigBody>
{
    public UpdateAppConfigBodyValidator()
    {
        RuleFor(a => a.DisplayName)
            .MustBeAValidDisplayName();

        RuleFor(a => a.Description)
            .NotEmpty().WithMessage("{PropertyName} cannot be empty")
            .MustBeAValidDescription();
    }
}