﻿namespace Axon.Core.Api.Models.Organisation
{
    public class SetupThemeBody
    {
        public OrgColour DefaultColours { get; set; }
        public OrgColour DarkColours { get; set; }

        public bool IsEmpty()
        {
            return DefaultColours is null ||
                string.IsNullOrEmpty(DefaultColours.Highlights) &&
                string.IsNullOrEmpty(DefaultColours.Buttons) &&
                string.IsNullOrEmpty(DefaultColours.Hover)
                ||
                DarkColours is null ||
                string.IsNullOrEmpty(DarkColours.Highlights) &&
                string.IsNullOrEmpty(DarkColours.Buttons) &&
                string.IsNullOrEmpty(DarkColours.Hover);
        }
    }

    public class OrgColour
    {
        public string Highlights { get; set; }
        public string Buttons { get; set; }
        public string Hover { get; set; }
    }
}
