﻿using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Models.User;
using Axon.Core.Api.Queries.User.GetUserByEmail;
using Axon.Core.Api.Services.Authorisation;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Axon.Core.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("v{version:apiVersion}/User")]
[ProducesResponseType(401, Type = typeof(CommandResponse))]
[ProducesResponseType(403, Type = typeof(CommandResponse))]
[ProducesResponseType(500, Type = typeof(CommandResponse))]
[Authorize]
public class UserController : ApiControllerBase
{
    private readonly IValidator<GetUserByEmailQueryRequest> getUserByEmailQueryRequestValidator;
    public UserController(IMediator mediator, IValidator<GetUserByEmailQueryRequest> getUserByEmailQueryRequestValidator) : base(mediator)
    {
        this.getUserByEmailQueryRequestValidator = getUserByEmailQueryRequestValidator;
    }

    [HttpGet]
    [HasPermissions(nameof(CorePermissions.EditOrganisation))]
    [ProducesResponseType(200, Type = typeof(CommandResponse<UserModel>))]
    [ProducesResponseType(404, Type = typeof(CommandResponse))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetUserByEmailAsync([FromQuery] string email)
    {
        var request = new GetUserByEmailQueryRequest(email);
        var validationResult = await getUserByEmailQueryRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }
}