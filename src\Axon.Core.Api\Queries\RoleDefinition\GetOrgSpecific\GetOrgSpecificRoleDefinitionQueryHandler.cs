﻿using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Role;
using Axon.Core.Api.Queries.RoleDefinition.GetByAppOrg;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using MediatR;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Domain.Extensions;
using Axon.Core.Domain.Services.Access;

namespace Axon.Core.Api.Queries.AppAccess.RoleDefinitionListQuery
{
    internal sealed class GetOrgSpecificRoleDefinitionQueryHandler : IRequestHandler<GetOrgSpecificRoleDefinitionQueryRequest, CommandResponse<IEnumerable<AppRoleDefinitionPermission>>>
    {
        private readonly IRoleDefinitionRepository roleDefinitionRepository;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IMapper mapper;
        public GetOrgSpecificRoleDefinitionQueryHandler(IRoleDefinitionRepository roleDefinitionRepository, 
                                                        IOrganisationRepository organisationRepository, 
                                                        IMapper mapper)
        {
            this.roleDefinitionRepository = roleDefinitionRepository;
            this.organisationRepository = organisationRepository;
            this.mapper = mapper;
        }
        public async Task<CommandResponse<IEnumerable<AppRoleDefinitionPermission>>> Handle(GetOrgSpecificRoleDefinitionQueryRequest request, CancellationToken cancellationToken)
        {
            var organisation = await organisationRepository.GetItemByCodeNameAsync(request.OrgCodeName);
            if (organisation == null)
            {
                //As the permission check will be applied at the level above this, we should not be able to hit this scenario.
                return CommandResponse<IEnumerable<AppRoleDefinitionPermission>>.NotFound(nameof(OrganisationEntity), request.OrgCodeName);
            }

            var roleDefinition = await roleDefinitionRepository.GetItemByAppCodeAsync(request.AppCode);
            if (roleDefinition == null)
            {
                return CommandResponse<IEnumerable<AppRoleDefinitionPermission>>.NotFound(nameof(RoleDefinitionEntity), request.AppCode);
            }

            IEnumerable<AppRoleDefinitionPermission> roleDefinitionsMapped = mapper.Map<IEnumerable<RoleDefinitionEntity.Permission>, IEnumerable<AppRoleDefinitionPermission>>(
                                                        roleDefinition.FilterByAccessLevel(organisation.AccessLevel ?? AccessLevel.Restricted).Permissions);

            return new CommandResponse<IEnumerable<AppRoleDefinitionPermission>> { data = roleDefinitionsMapped };
        }
    }
}
