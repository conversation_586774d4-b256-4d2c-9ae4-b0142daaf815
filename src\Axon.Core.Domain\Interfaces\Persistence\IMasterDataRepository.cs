using Axon.Core.Domain.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Domain.Interfaces.Persistence;

public interface IMasterDataRepository : IRepository<MasterDataEntity>
{
    Task<IReadOnlyCollection<MasterDataEntity>> GetMasterDataAsync(string dataType);
    Task<IReadOnlyCollection<MasterDataEntity>> GetMasterDataAsync(string dataType, IEnumerable<string> ids);
    Task<MasterDataEntity> GetMasterDataAsync(string dataType, string id);
}