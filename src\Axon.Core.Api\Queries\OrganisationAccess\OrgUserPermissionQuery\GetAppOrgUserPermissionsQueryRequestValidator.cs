﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.OrganisationAccess.OrgUserPermissionQuery;

[UsedImplicitly]
public class GetAppOrgUserPermissionsQueryRequestValidator : AbstractValidator<GetAppOrgUserPermissionsQueryRequest>
{
    public GetAppOrgUserPermissionsQueryRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.UserEmail)
            .NotEmpty().WithMessage("Email cannot be empty.")
            .EmailAddress().WithMessage("Invalid email address format.");
    }
}