﻿using System;
using MediatR;
using System.Threading.Tasks;
using System.Threading;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.Audit;
using Axon.Core.Domain.Models.Audit;
using Microsoft.AspNetCore.Http;
using Axon.Core.Domain.Constants;
using Axon.Core.Shared.Extensions;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Microsoft.Extensions.Logging;
namespace Axon.Core.Api.Commands.Authentication.SignOut
{
    internal class SignOutCommandHandler : IRequestHandler<SignOutCommand, CommandResponse>
    {
        private readonly ILogger<SignOutCommandHandler> logger;
        private readonly IAuditService<TenantAuditExtensions> auditService;
        private readonly IAuthenticationService authenticationService;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IUserRepository userRepository;
        private readonly IOrganisationRepository organisationRepository;

        public SignOutCommandHandler(ILogger<SignOutCommandHandler> logger, 
            IAuditService<TenantAuditExtensions> auditService, 
            IAuthenticationService authenticationService, 
            IHttpContextAccessor httpContextAccessor, 
            IOrganisationRepository organisationRepository, 
            IUserRepository userRepository)
        {
            this.logger = logger;
            this.auditService = auditService;
            this.authenticationService = authenticationService;
            this.httpContextAccessor = httpContextAccessor;
            this.organisationRepository = organisationRepository;
            this.userRepository = userRepository;
        }

        public async Task<CommandResponse> Handle(SignOutCommand request, CancellationToken cancellationToken)
        {
            var claims = httpContextAccessor.HttpContext.User.Claims;
            var clientDetails = new ClientDetails(claims.ObjectId(), claims.Email(), httpContextAccessor.HttpContext.Connection?.RemoteIpAddress?.ToString());
            UserEntity userEntity = null;
            try
            {
                if (!string.IsNullOrEmpty(claims.Email()))
                {
                    userEntity = await userRepository.GetUserByEmailAsync(claims.Email());
                }

                if(userEntity == null)
                {
                    logger.LogWarning("Could not find User by email or email claim was blank, email: {Email}, objectId: {UserId}, now getting by ObjectId", claims.Email(), claims.ObjectId());
                    userEntity = await userRepository.GetByIdentityProviderObjectIdAsync(claims.ObjectId());
                }

                var tenant = await organisationRepository.GetItemAsync(userEntity.OwnerOrganisationId);

                if (tenant != null)
                {
                    var eventType = request.IsAutoLogout ? AuditEventTypes.UserAutoLogout : AuditEventTypes.UserLogout;
                    var eventDescription = request.IsAutoLogout ? "User has successfully been automatically logged out of SmartPhlex" : "User has successfully logged out of SmartPhlex";

                    var auditExtension = new TenantAuditExtensions(AuditEventCategories.User, eventDescription, Guid.NewGuid(), clientDetails, tenant.CodeName);

                    auditService.Log(eventType, auditExtension);
                }
                else
                {
                    logger.LogInformation("Could not find organisation for User: {UserId}, Email: {Email}. So logout cannot be audited", userEntity.Id, claims.Email());
                }

            }
            catch (Exception ex)
            {
                logger.LogInformation(ex,"Error occured finding organisation for User oid: {UserId}, Email: {Email}. So logout cannot be audited",claims.ObjectId(), claims.Email());
            }

            await authenticationService.SignOut();

            return CommandResponse.Success();
        }
    }
}
