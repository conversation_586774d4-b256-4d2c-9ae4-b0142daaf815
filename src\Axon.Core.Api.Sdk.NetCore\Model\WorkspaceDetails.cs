/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// WorkspaceDetails
    /// </summary>
    [DataContract(Name = "WorkspaceDetails")]
    public partial class WorkspaceDetails : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="WorkspaceDetails" /> class.
        /// </summary>
        /// <param name="appWorkspaceId">appWorkspaceId.</param>
        /// <param name="tenantWorkspaceId">tenantWorkspaceId.</param>
        /// <param name="appDashboards">appDashboards.</param>
        /// <param name="tenantDashboards">tenantDashboards.</param>
        public WorkspaceDetails(string appWorkspaceId = default(string), string tenantWorkspaceId = default(string), List<Dashboard> appDashboards = default(List<Dashboard>), List<Dashboard> tenantDashboards = default(List<Dashboard>))
        {
            this.AppWorkspaceId = appWorkspaceId;
            this.TenantWorkspaceId = tenantWorkspaceId;
            this.AppDashboards = appDashboards;
            this.TenantDashboards = tenantDashboards;
        }

        /// <summary>
        /// Gets or Sets AppWorkspaceId
        /// </summary>
        [DataMember(Name = "appWorkspaceId", EmitDefaultValue = true)]
        public string AppWorkspaceId { get; set; }

        /// <summary>
        /// Gets or Sets TenantWorkspaceId
        /// </summary>
        [DataMember(Name = "tenantWorkspaceId", EmitDefaultValue = true)]
        public string TenantWorkspaceId { get; set; }

        /// <summary>
        /// Gets or Sets AppDashboards
        /// </summary>
        [DataMember(Name = "appDashboards", EmitDefaultValue = true)]
        public List<Dashboard> AppDashboards { get; set; }

        /// <summary>
        /// Gets or Sets TenantDashboards
        /// </summary>
        [DataMember(Name = "tenantDashboards", EmitDefaultValue = true)]
        public List<Dashboard> TenantDashboards { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class WorkspaceDetails {\n");
            sb.Append("  AppWorkspaceId: ").Append(AppWorkspaceId).Append("\n");
            sb.Append("  TenantWorkspaceId: ").Append(TenantWorkspaceId).Append("\n");
            sb.Append("  AppDashboards: ").Append(AppDashboards).Append("\n");
            sb.Append("  TenantDashboards: ").Append(TenantDashboards).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
