﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.OrganisationAccess;
using MediatR;
using System.Collections.Generic;

namespace Axon.Core.Api.Queries.AppAccess.ScopeResourcesQuery
{
    public class GetScopesAndResourcesQueryRequest : IRequest<CommandResponse<IEnumerable<ScopeDataModel>>>
    {
        public string AppCodeName { get; }
        public string OrgCodeName { get; }

        public GetScopesAndResourcesQueryRequest(string orgCodeName,
                                                 string appCodeName)
        {
            OrgCodeName = orgCodeName;
            AppCodeName = appCodeName;
        }
    }
}
