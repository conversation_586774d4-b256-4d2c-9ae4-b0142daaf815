﻿using System.Collections.Generic;
using Axon.Core.Domain.Entities.Base;
using System.Linq;
using System.Threading.Tasks;
using Axon.Core.Domain.Models;

namespace Axon.Core.Domain.Interfaces.Persistence
{
    public interface IRepository<T> where T : BaseEntity
    {
        Task<T> GetItemAsync(string id);
        Task<string> AddItemAsync(T item);
        Task UpdateItemAsync(string id, T item);
        Task DeleteItemAsync(string id);
        Task<BulkOperationResponse<T>> BulkUpdateItemsAsync(List<T> items);
        Task<BulkOperationResponse<T>> BulkDeleteItemsAsync(List<T> items);
        IOrderedQueryable<T> GetAllLinqQueryable();
    }
}
