﻿using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Domain.Interfaces.Persistence;
using JetBrains.Annotations;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.AppGroup.ListUnassignedGroups
{
    [UsedImplicitly]
    internal class GetUnassignedGroupsQueryHandler : IRequestHandler<GetUnassignedGroupsQueryRequest, CommandResponse<ApiListResult<UnassignedGroupModel>>>
    {
        private readonly IAppGroupRepository appGroupRepository;
        private readonly IGroupRepository groupRepository;
        private readonly IMapper mapper;

        public GetUnassignedGroupsQueryHandler(IAppGroupRepository appGroupRepository, IGroupRepository groupRepository, IMapper mapper)
        {
            this.appGroupRepository = appGroupRepository;
            this.groupRepository = groupRepository;
            this.mapper = mapper;
        }

        public async Task<CommandResponse<ApiListResult<UnassignedGroupModel>>> Handle(GetUnassignedGroupsQueryRequest request, CancellationToken cancellationToken)
        {
            var assignedAppGroups = await appGroupRepository.GetAppGroupsAsync(request.OrganisationCodeName, request.AppCodeName);
            var orgGroups = await groupRepository.GetForOrganisationAsync(request.OrganisationCodeName);

            var appGroupEntities = orgGroups
                                    .Where(group => assignedAppGroups.All(appGroup => appGroup.GroupId != group.Id));

            return CommandResponse<ApiListResult<UnassignedGroupModel>>.Data(new ApiListResult<UnassignedGroupModel>(
                appGroupEntities.Any() ? appGroupEntities.Select(mapper.Map<UnassignedGroupModel>) : []
                ));
        }
    }
}
