﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Axon.Core.Api.Attributes;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Models.OrganisationAccess;
using Axon.Core.Api.Queries.AppAccess.ScopeResourcesQuery;
using Axon.Core.Api.Services.Authorisation;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Axon.Core.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("v{version:apiVersion}/Organisation/{orgCodeName}/app/{appCodeName}")]
[ProducesResponseType(401, Type = typeof(CommandResponse))]
[ProducesResponseType(403, Type = typeof(CommandResponse))]
[ProducesResponseType(500, Type = typeof(CommandResponse))]
[Authorize]
public class PermissionController : ApiControllerBase
{
    private readonly IValidator<GetScopesAndResourcesQueryRequest> getScopesAndResourcesQueryRequestValidator;

    public PermissionController(IMediator mediator, IValidator<GetScopesAndResourcesQueryRequest> getScopesAndResourcesQueryRequestValidator) : base(mediator)
    {
        this.getScopesAndResourcesQueryRequestValidator = getScopesAndResourcesQueryRequestValidator;
    }

    [HttpGet("scopes", Name = "GetScopesAndResourcesForOrganisation")]
    [ProducesResponseType(200, Type = typeof(CommandResponse<IEnumerable<ScopeDataModel>>))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [HasOrganisationPermissions(permissionsKey: nameof(CorePermissions.EditOrganisation))]
    public async Task<IActionResult> GetScopesAndResourcesForOrganisationAsync(string orgCodeName, string appCodeName)
    {
        var request = new GetScopesAndResourcesQueryRequest(orgCodeName, appCodeName);
        var validationResult = await getScopesAndResourcesQueryRequestValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }
}