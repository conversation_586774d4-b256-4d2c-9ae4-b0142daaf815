﻿using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.Organisation;
using Axon.Core.Shared.Api;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.Core.Api.Queries.OrganisationGroup.GetEligibleUsers;

public class GetOrganisationGroupEligibleUsersQueryRequest : IRequest<CommandResponse<ApiPagedListResult<OrganisationUserModel>>>
{
    public string OrganisationCodeName { get; }
    public string OrganisationGroupId { get; }
    public ListParams ListParams { get; }
    public string Embed { get; }

    public GetOrganisationGroupEligibleUsersQueryRequest(string organisationCodeName, string organisationGroupId, ListParams listParams, string embed)
    {
        OrganisationCodeName = organisationCodeName;
        OrganisationGroupId = organisationGroupId;
        ListParams = listParams;
        Embed = embed;
    }
}
