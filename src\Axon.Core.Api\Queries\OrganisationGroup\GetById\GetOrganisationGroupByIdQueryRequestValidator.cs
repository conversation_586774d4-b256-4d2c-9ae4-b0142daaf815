﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.OrganisationGroup.GetById;

[UsedImplicitly]
public class 
    GetOrganisationGroupByIdQueryRequestValidator : AbstractValidator<GetOrganisationGroupByIdQueryRequest>
{
    public GetOrganisationGroupByIdQueryRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName()
            .NotEmpty();
        RuleFor(x => x.OrganisationGroupId)
            .MustBeAValidGuid()
            .NotEmpty();
    }
}