﻿using System.Text.Json;
using Axon.Core.Domain.Entities;

namespace Axon.Core.Api.Validators.AppOrganisationSettings;

public class TextValidator : ISettingValidator
{
    public bool Validate(JsonElement settingValue, out string errorMessage, AppSettingsEntity.Setting setting)
    {
        var isValueAString = settingValue.ValueKind is JsonValueKind.String;
        errorMessage = isValueAString ? string.Empty : "Setting needs to be a string";
        return isValueAString;
    }
}