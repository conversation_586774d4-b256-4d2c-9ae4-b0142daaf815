﻿using System.Text.Json;
using MediatR;

namespace Axon.Core.Api.Commands.AppOrganisationSettings.UpdateAppOrganisationSettings
{
    public class UpdateAppOrganisationSettingsCommandRequest : IRequest<CommandResponse>
    {
        public UpdateAppOrganisationSettingsCommandRequest(string organisationCodeName, string appCodeName, string settingName, JsonElement value)
        {
            OrganisationCodeName = organisationCodeName;
            AppCodeName = appCodeName;
            SettingName = settingName;
            Value = value;
        }

        public string OrganisationCodeName { get; }
        public string AppCodeName { get; }
        public string SettingName { get; }
        public JsonElement Value { get; }
    }
}
