﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Queries.AppOrganisationSettings;

[UsedImplicitly]
public class GetOrganisationAppMasterDataQueryRequestValidator : AbstractValidator<GetOrganisationAppMasterDataQueryRequest>
{
    public GetOrganisationAppMasterDataQueryRequestValidator()
    {
        RuleFor(x => x.OrgCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
    }
}