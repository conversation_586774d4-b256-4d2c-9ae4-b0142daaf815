﻿using System.Collections.Generic;

namespace Axon.Core.Api.Models.Role
{
    public class AppRoleDefinitionPermission
    {
        public AppRoleDefinitionPermission(string name, bool hasScopes)
        {
            Name = name;
            HasScopes = hasScopes;
        }
        public string Name { get; set; }
        public bool HasScopes { get; set; }
        public List<ValidScopeBody> ValidScopes { get; set; }
    }
}
