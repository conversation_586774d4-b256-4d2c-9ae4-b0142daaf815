using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.App.UpdateAppConfig;

internal class UpdateAppConfigCommandHandler : BaseUpdateCommandHandler<UpdateAppConfigBody, AppEntity, AppUpdated>
{
    public UpdateAppConfigCommandHandler(IAppRepository repo, IMapper mapper, IMessageBus messageBus) :
        base(repo, mapper, messageBus)
    {
    }

    public override async Task<CommandResponse> Handle(UpdateCommandRequest<UpdateAppConfigBody> request, CancellationToken cancellationToken)
    {
        var repo = (IAppRepository) Repo;
        var existing = (await repo.GetItemsByDisplayNameAsync(request.Model.DisplayName)).ToList();

        if (existing.Any(e => e.Id != request.Id)) return CommandResponse.Failed(nameof(request.Model.DisplayName), $"App `{request.Model.DisplayName}` already exists");

        return await base.Handle(request, cancellationToken);
    }
}