﻿using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Services.Authorisation;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using CommunityToolkit.Diagnostics;
using Phlex.Core.MessageBus;

namespace Axon.Core.Api.Commands.UserPreference.Create;

internal class CreateUserPreferenceCommandHandler : BaseCommandHandler<UserPreferenceEntity, CreateUserPreferenceCommandRequest, CommandResponse>
{
    private readonly ICurrentUserProvider currentUserProvider;
    private readonly IUserPreferenceService userPreferenceService;

    public CreateUserPreferenceCommandHandler(IUserPreferenceRepository repo, IMapper mapper, IMessageBus messageBus, ICurrentUserProvider currentUserProvider,
        IUserPreferenceService userPreferenceService) : base(repo, mapper, messageBus)
    {
        Guard.IsNotNull(currentUserProvider);
        this.currentUserProvider = currentUserProvider;
        Guard.IsNotNull(userPreferenceService);
        this.userPreferenceService = userPreferenceService;
    }

    public override async Task<CommandResponse> Handle(CreateUserPreferenceCommandRequest request, CancellationToken cancellationToken)
    {
        var repo = (IUserPreferenceRepository) Repo;
        var userContext = currentUserProvider.GetUserContext();
        var preferences = await repo.GetItemAsync(userContext.ObjectId);

        if (preferences == null)
            return await userPreferenceService.CreatePreferences(userContext.ObjectId, request.Model, cancellationToken);

        foreach (var pref in request.Model.UserPreferences)
            if (preferences.UserPreferences.ContainsKey(pref.Key))
                return CommandResponse.Conflict(nameof(UserPreferenceEntity), preferences.Id, pref.Key);

        preferences.UserPreferences = preferences.UserPreferences
            .Concat(request.Model.UserPreferences)
            .ToDictionary(x => x.Key, x => x.Value);


        return await userPreferenceService.UpdatePreferences(preferences, request.Model.UserPreferences, cancellationToken);
    }
}