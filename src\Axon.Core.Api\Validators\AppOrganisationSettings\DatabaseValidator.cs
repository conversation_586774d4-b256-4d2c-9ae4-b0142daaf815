﻿using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using Axon.Core.Api.Mappers.AppOrganisationSettings;
using Axon.Core.Domain.Enums;

namespace Axon.Core.Api.Validators.AppOrganisationSettings
{
    public class DatabaseValidator : ISettingValidator
    {
        private readonly IAppOrganisationSettingsMapperFactory appOrganisationSettingsMapperFactory;

        public DatabaseValidator(IAppOrganisationSettingsMapperFactory appOrganisationSettingsMapperFactory)
        {
            this.appOrganisationSettingsMapperFactory = appOrganisationSettingsMapperFactory;
        }

        public bool Validate(JsonElement settingValue, out string errorMessage, AppSettingsEntity.Setting setting)
        {
            errorMessage = string.Empty;

            try
            {
                var mapper = appOrganisationSettingsMapperFactory.Create(SettingDataType.Database);
                if (mapper.Map(settingValue) is not IEnumerable<DatabaseSettingValue> values)
                    throw new InvalidDataException("Cannot convert provided setting value to database type.");

                foreach (var value in values)
                    ValidateSingleSetting(setting, value);
                return true;
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
                return false;
            }
        }

        private static void ValidateSingleSetting(AppSettingsEntity.Setting setting, DatabaseSettingValue settingValue)
        {
            var validationResult = new DatabaseSettingValueValidator().Validate(settingValue);
            if (!validationResult.IsValid)
                throw new InvalidDataException(validationResult.ToString());

            if (!setting.DataTypeOptions.Contains(settingValue.Type))
                throw new InvalidDataException("Provided setting type is invalid.");
        }
    }
}
