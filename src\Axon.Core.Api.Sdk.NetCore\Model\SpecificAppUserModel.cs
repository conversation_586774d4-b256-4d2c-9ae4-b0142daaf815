/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.Core.Api.Sdk.NetCore.Client.FileParameter;
using OpenAPIDateConverter = Axon.Core.Api.Sdk.NetCore.Client.OpenAPIDateConverter;

namespace Axon.Core.Api.Sdk.NetCore.Model
{
    /// <summary>
    /// SpecificAppUserModel
    /// </summary>
    [DataContract(Name = "SpecificAppUserModel")]
    public partial class SpecificAppUserModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SpecificAppUserModel" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected SpecificAppUserModel() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="SpecificAppUserModel" /> class.
        /// </summary>
        /// <param name="appCodeName">appCodeName (required).</param>
        /// <param name="organisationCodeName">organisationCodeName (required).</param>
        /// <param name="userId">userId (required).</param>
        /// <param name="userName">userName (required).</param>
        /// <param name="email">email (required).</param>
        /// <param name="role">role (required).</param>
        /// <param name="groups">groups (required).</param>
        /// <param name="scopes">scopes (required).</param>
        public SpecificAppUserModel(string appCodeName = default(string), string organisationCodeName = default(string), string userId = default(string), string userName = default(string), string email = default(string), AppUserRoleModel role = default(AppUserRoleModel), List<AppUserGroupModel> groups = default(List<AppUserGroupModel>), List<ScopeResources> scopes = default(List<ScopeResources>))
        {
            // to ensure "appCodeName" is required (not null)
            if (appCodeName == null)
            {
                throw new ArgumentNullException("appCodeName is a required property for SpecificAppUserModel and cannot be null");
            }
            this.AppCodeName = appCodeName;
            // to ensure "organisationCodeName" is required (not null)
            if (organisationCodeName == null)
            {
                throw new ArgumentNullException("organisationCodeName is a required property for SpecificAppUserModel and cannot be null");
            }
            this.OrganisationCodeName = organisationCodeName;
            // to ensure "userId" is required (not null)
            if (userId == null)
            {
                throw new ArgumentNullException("userId is a required property for SpecificAppUserModel and cannot be null");
            }
            this.UserId = userId;
            // to ensure "userName" is required (not null)
            if (userName == null)
            {
                throw new ArgumentNullException("userName is a required property for SpecificAppUserModel and cannot be null");
            }
            this.UserName = userName;
            // to ensure "email" is required (not null)
            if (email == null)
            {
                throw new ArgumentNullException("email is a required property for SpecificAppUserModel and cannot be null");
            }
            this.Email = email;
            // to ensure "role" is required (not null)
            if (role == null)
            {
                throw new ArgumentNullException("role is a required property for SpecificAppUserModel and cannot be null");
            }
            this.Role = role;
            // to ensure "groups" is required (not null)
            if (groups == null)
            {
                throw new ArgumentNullException("groups is a required property for SpecificAppUserModel and cannot be null");
            }
            this.Groups = groups;
            // to ensure "scopes" is required (not null)
            if (scopes == null)
            {
                throw new ArgumentNullException("scopes is a required property for SpecificAppUserModel and cannot be null");
            }
            this.Scopes = scopes;
        }

        /// <summary>
        /// Gets or Sets AppCodeName
        /// </summary>
        [DataMember(Name = "appCodeName", IsRequired = true, EmitDefaultValue = true)]
        public string AppCodeName { get; set; }

        /// <summary>
        /// Gets or Sets OrganisationCodeName
        /// </summary>
        [DataMember(Name = "organisationCodeName", IsRequired = true, EmitDefaultValue = true)]
        public string OrganisationCodeName { get; set; }

        /// <summary>
        /// Gets or Sets UserId
        /// </summary>
        [DataMember(Name = "userId", IsRequired = true, EmitDefaultValue = true)]
        public string UserId { get; set; }

        /// <summary>
        /// Gets or Sets UserName
        /// </summary>
        [DataMember(Name = "userName", IsRequired = true, EmitDefaultValue = true)]
        public string UserName { get; set; }

        /// <summary>
        /// Gets or Sets Email
        /// </summary>
        [DataMember(Name = "email", IsRequired = true, EmitDefaultValue = true)]
        public string Email { get; set; }

        /// <summary>
        /// Gets or Sets Role
        /// </summary>
        [DataMember(Name = "role", IsRequired = true, EmitDefaultValue = true)]
        public AppUserRoleModel Role { get; set; }

        /// <summary>
        /// Gets or Sets Groups
        /// </summary>
        [DataMember(Name = "groups", IsRequired = true, EmitDefaultValue = true)]
        public List<AppUserGroupModel> Groups { get; set; }

        /// <summary>
        /// Gets or Sets Scopes
        /// </summary>
        [DataMember(Name = "scopes", IsRequired = true, EmitDefaultValue = true)]
        public List<ScopeResources> Scopes { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class SpecificAppUserModel {\n");
            sb.Append("  AppCodeName: ").Append(AppCodeName).Append("\n");
            sb.Append("  OrganisationCodeName: ").Append(OrganisationCodeName).Append("\n");
            sb.Append("  UserId: ").Append(UserId).Append("\n");
            sb.Append("  UserName: ").Append(UserName).Append("\n");
            sb.Append("  Email: ").Append(Email).Append("\n");
            sb.Append("  Role: ").Append(Role).Append("\n");
            sb.Append("  Groups: ").Append(Groups).Append("\n");
            sb.Append("  Scopes: ").Append(Scopes).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            // AppCodeName (string) minLength
            if (this.AppCodeName != null && this.AppCodeName.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for AppCodeName, length must be greater than 1.", new [] { "AppCodeName" });
            }

            // OrganisationCodeName (string) minLength
            if (this.OrganisationCodeName != null && this.OrganisationCodeName.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for OrganisationCodeName, length must be greater than 1.", new [] { "OrganisationCodeName" });
            }

            // UserId (string) minLength
            if (this.UserId != null && this.UserId.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for UserId, length must be greater than 1.", new [] { "UserId" });
            }

            // UserName (string) minLength
            if (this.UserName != null && this.UserName.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for UserName, length must be greater than 1.", new [] { "UserName" });
            }

            // Email (string) minLength
            if (this.Email != null && this.Email.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for Email, length must be greater than 1.", new [] { "Email" });
            }

            yield break;
        }
    }

}
