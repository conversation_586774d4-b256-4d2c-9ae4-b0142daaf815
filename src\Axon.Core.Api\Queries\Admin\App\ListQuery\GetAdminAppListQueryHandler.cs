﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Models.App;
using Axon.Core.Domain.Entities.Base;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Infrastructure.Extensions;
using CommunityToolkit.Diagnostics;
using MediatR;

namespace Axon.Core.Api.Queries.Admin.App.ListQuery
{
    internal class GetAdminAppListQueryHandler : IRequestHandler<GetAdminAppListQueryRequest, CommandResponse<IEnumerable<AppModel>>>
    {
        private readonly IAppRepository appRepo;
        private readonly IMapper mapper;
        public GetAdminAppListQueryHandler(IAppRepository appRepo, IMapper mapper)
        {
            this.appRepo = appRepo;
            this.mapper = mapper;
        }

        public async Task<CommandResponse<IEnumerable<AppModel>>> Handle(GetAdminAppListQueryRequest request, CancellationToken cancellationToken)
        {
            var filtered = appRepo
                    .GetAllLinqQueryable()
                    .Where(w => !w.IsDeleted)
                    .FilteredResult(request.ListParams)
                    .AsEnumerable();
            return CommandResponse<IEnumerable<AppModel>>.Data(filtered.Select(e => mapper.Map<AppModel>(e)));
        }
    }
}
