﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Axon.Core.Api.Models.Role;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Extensions;
using Axon.Core.Domain.Interfaces.Persistence;

namespace Axon.Core.Api.Services.Role
{
    public class RoleModelService : IRoleModelService
    {
        private readonly IRoleRepository roleRepository;
        private readonly IRoleDefinitionRepository roleDefinitionRepository;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IMapper mapper;

        public RoleModelService(IRoleRepository roleRepository, IRoleDefinitionRepository roleDefinitionRepository, IOrganisationRepository organisationRepository, IMapper mapper)
        {
            this.roleRepository = roleRepository;
            this.roleDefinitionRepository = roleDefinitionRepository;
            this.organisationRepository = organisationRepository;
            this.mapper = mapper;
        }

        public async Task<IEnumerable<RoleModel>> GetRoleModelsAsync(string orgCodeName, string appCodeName, object listParams = null)
        {
            var roleModelList = new List<RoleModel>();

            var organisation = await organisationRepository.GetItemByCodeNameAsync(orgCodeName);
            var definition = await roleDefinitionRepository.GetItemByAppCodeAsync(appCodeName);

            if (organisation == null || definition == null)
            {
                return roleModelList;
            }

            var filteredDefinition = definition.FilterByAccessLevel(organisation.AccessLevel ?? Domain.Services.Access.AccessLevel.Restricted);

            var defaultRoles = listParams == null ? 
                await roleRepository.GetDefaultAndCustomRolesAsync(orgCodeName, appCodeName) : 
                await roleRepository.GetDefaultAndCustomRolesAsync(orgCodeName, appCodeName, listParams);

            roleModelList.AddRange(MapRoles(defaultRoles));

            var overriddenRoles = await roleRepository.GetOverriddenRolesAsync(orgCodeName, appCodeName);
            LayerOverridenRoles(overriddenRoles, roleModelList);

            foreach(var model in roleModelList)
            {
                model.Permissions = model.Permissions.Where(mp => filteredDefinition.Permissions.Exists(dp => dp.Name.Equals(mp.Name,
                                                                                                              System.StringComparison.OrdinalIgnoreCase))).ToList();
            }

            return roleModelList;
        }

        private void LayerOverridenRoles(IList<RoleEntity> overriddenRoles, List<RoleModel> roleModelList)
        {
            foreach (var overriddenRole in overriddenRoles)
            {
                var role = roleModelList.Find(x => x.Id == overriddenRole.InheritRoleId);
                if (role != null)
                {
                    role.OrganisationCodeName = overriddenRole.OrganisationCodeName;
                    role.IsEnabled = overriddenRole.IsEnabled;

                    foreach (var overriddenPermission in overriddenRole.Permissions)
                    {
                        LayerOverriddenPermission(role, overriddenPermission);
                    }
                }
            }
        }

        private void LayerOverriddenPermission(RoleModel role, RoleEntity.Permission overriddenPermission)
        {
            var permission = role.Permissions.Find(x => x.Name.Equals(overriddenPermission.Name));
            if (permission != null && permission.IsInherited)
            {
                permission.Scopes = overriddenPermission.Scopes == null ? null : overriddenPermission?.Scopes.Select(mapper.Map<RoleModel.ScopeResources>).ToArray();
                permission.Allow = overriddenPermission.Allow;
                permission.IsInherited = false;
            }
            else
            {
                var newPermission = mapper.Map<RoleModel.Permission>(overriddenPermission);
                newPermission.IsInherited = false;
                role.Permissions.Add(newPermission);
            }
        }

        private List<RoleModel> MapRoles(IList<RoleEntity> roleEntities)
        {
            return roleEntities.Select(x =>
            {
                var roleModel = mapper.Map<RoleModel>(x);
                var isDefaultRole = roleModel.OrganisationCodeName == null;

                foreach (var rolePermission in roleModel.Permissions)
                {
                    rolePermission.IsInherited = isDefaultRole;
                }
                return roleModel;
            }).ToList();
        }
    }
}
