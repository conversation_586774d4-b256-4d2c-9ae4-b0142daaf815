﻿using Microsoft.AspNetCore.Http;
using Microsoft.Net.Http.Headers;
using System.Threading.Tasks;
using System.Threading;

namespace Axon.Core.Api.Services.FileUpload
{
    public class HttpContextStreamFileReader : IHttpContextStreamFileReader
    {
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IMultipartReaderWrapper multipartReaderWrapper;

        public HttpContextStreamFileReader(IHttpContextAccessor httpContextAccessor, IMultipartReaderWrapper multipartReaderWrapper)
        {
            this.httpContextAccessor = httpContextAccessor;
            this.multipartReaderWrapper = multipartReaderWrapper;
        }

        public async Task<StreamFileData> GetStreamFileDataAsync(CancellationToken cancellationToken)
        {
            var boundary = HeaderUtilities.RemoveQuotes(
                MediaTypeHeaderValue.Parse(httpContextAccessor.HttpContext?.Request.ContentType).Boundary
            ).Value;
            var body = httpContextAccessor.HttpContext?.Request.Body;
            var contentLength = httpContextAccessor.HttpContext?.Request.ContentLength;

            var section = await multipartReaderWrapper.ReadMultipartSectionAsync(boundary, body, cancellationToken);

            if (section != null)
            {
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(
                    section.ContentDisposition, out var contentDisposition
                );

                if (hasContentDispositionHeader && contentDisposition.DispositionType.Equals("form-data") &&
                    !string.IsNullOrEmpty(contentDisposition.FileName.Value))
                {
                    return new StreamFileData
                    {
                        FileName = contentDisposition.FileName.Value,
                        FileStream = section.Body,
                        ContentType = section.ContentType,
                        ContentLength = contentLength
                    };
                }
            }

            return null;
        }
    }
}
