﻿using Axon.Core.Api.Models.Role;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.AppUser
{
    public class AppUserModel
    {
        [Required] public string AppCodeName { get; set; }
        [Required] public string OrganisationCodeName { get; set; }
        [Required] public string UserId { get; set; }
        [Required] public string UserName { get; set; }
        [Required] public string Email { get; set; }
        [Required] public AppUserRoleModel[] Roles { get; set; }
        [Required] public AppUserGroupModel[] Groups { get; set; }
        [Required] public bool HasDirectRole { get; set; }
    }

    public class SpecificAppUserModel
    {
        [Required] public string AppCodeName { get; set; }
        [Required] public string OrganisationCodeName { get; set; }
        [Required] public string UserId { get; set; }
        [Required] public string UserName { get; set; }
        [Required] public string Email { get; set; }
        [Required] public AppUserRoleModel Role { get; set; }
        [Required] public AppUserGroupModel[] Groups { get; set; }
        [Required] public RoleModel.ScopeResources[] Scopes { get; set; }
    }

    public record AppUserRoleModel
    {
        [Required]
        public string Id { get; set; }
        [Required]
        public string RoleName { get; set; }
    }

    public record AppUserGroupModel
    {
        [Required]
        public string Id { get; set; }
        [Required]
        public string GroupName { get; set; }
    }
}
