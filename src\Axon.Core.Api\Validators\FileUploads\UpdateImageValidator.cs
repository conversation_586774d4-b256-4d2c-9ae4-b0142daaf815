﻿using Axon.Core.Api.Services.FileUpload;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using SkiaSharp;

namespace Axon.Core.Api.Validators.FileUploads
{
    public class UpdateImageValidator : IUpdateImageValidator
    {
        public const int MAX_FILENAME_LENGTH = 200;
        private const string ALLOWED_FILENAME_PATTERN = @"^[a-zA-Z0-9_\-]+\.[a-zA-Z]+$";
        private const string ALLOWED_CHARS_FOR_FILENAME = @"[^a-zA-Z0-9_.-]+";
        private readonly string[] AllowedFileExtensions = { ".jpg", ".jpeg", ".png" };
        private readonly Dictionary<string, byte[]> ImageHeaders = new()
        {
            { ".jpg", new byte[] { 0xFF, 0xD8, 0xFF }},
            { ".jpeg", new byte[] { 0xFF, 0xD8, 0xFF }},
            { ".png", new byte[] { 0x89, 0x50, 0x4E, 0x47 }}
        };
        private readonly Dictionary<string, string[]> AllowedContentType = new()
        {
            { ".jpg", new[] { "image/jpg", "image/jpeg" }},
            { ".jpeg", new[] { "image/jpg", "image/jpeg" }},
            { ".png", new[] { "image/png" }}
        };

        private readonly IHttpContextStreamFileReader httpContextStreamFileReader;

        public UpdateImageValidator(IHttpContextStreamFileReader httpContextStreamFileReader)
        {
            this.httpContextStreamFileReader = httpContextStreamFileReader;
        }

        public async Task<(bool isUploadedFileValid, string newFileName, Stream fileStream, string errorMessage)> Validate(
            EntityImageRequirements imageRequirements,
            CancellationToken cancellationToken)
        {
            try
            {
                var fileData = await httpContextStreamFileReader.GetStreamFileDataAsync(cancellationToken);
                if (fileData != null && fileData.FileStream != null)
                {
                    if (fileData.ContentLength > imageRequirements.MaxFileSizeInBytes)
                    {
                        return (false, null, null, $"File size should not exceed {BytesToFriendlyUnits(imageRequirements.MaxFileSizeInBytes)}.");
                    }

                    ReadFileIntoMemoryStream(fileData);

                    var (isFileNameValid, errorMessage, fileExtension) = ValidateFileName(fileData.FileName);

                    if (!isFileNameValid)
                    {
                        return (false, null, null, errorMessage);
                    }

                    if (!ValidateFileContent(fileData.FileStream, fileExtension))
                    {
                        return (false, null, null, "Provided file content does not match the specified type.");
                    }

                    if (!ValidateContentType(fileData.ContentType, fileExtension))
                    {
                        return (false, null, null, "Provided content type is not allowed or does not match the specified type.");
                    }

                    if (imageRequirements.ShouldBeSquare && !ValidateImageAspectRatio(fileData.FileStream))
                    {
                        return (false, null, null, "Provided image should be square in size.");
                    }

                    return (true, $@"{Guid.NewGuid()}{fileExtension}", fileData.FileStream, null);
                }

                return (false, null, null, "An error occurred unable to get image file.");
            }
            catch (Exception)
            {
                return (false, null, null, "An error with image upload occurred.");
            }
        }

        private static void ReadFileIntoMemoryStream(StreamFileData headerLogoData)
        {
            var memoryStream = new MemoryStream();
            headerLogoData.FileStream.CopyToAsync(memoryStream);
            headerLogoData.FileStream = memoryStream;
        }

        private (bool, string, string) ValidateFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
            {
                return (false, "File name not found.", null);
            }

            if (fileName.Length > MAX_FILENAME_LENGTH)
            {
                return (false, $"File name should not exceed {MAX_FILENAME_LENGTH} characters.", null);
            }

            var validFileName = Regex.Replace(fileName, ALLOWED_CHARS_FOR_FILENAME, "", RegexOptions.None, TimeSpan.FromMilliseconds(100));

            if (!Regex.IsMatch(validFileName, ALLOWED_FILENAME_PATTERN, RegexOptions.None, TimeSpan.FromMilliseconds(100)))
            {
                return (false, "Provided file name format is not valid.", null);
            }

            var fileExtension = Path.GetExtension(validFileName).ToLowerInvariant();
            if (!AllowedFileExtensions.Contains(fileExtension))
            {
                return (false, "Unsupported image format. Please upload a JPEG or PNG file.", null);
            }

            return (true, validFileName, fileExtension);
        }

        private bool ValidateFileContent(Stream fileStream, string fileExtension)
        {
            var expectedHeader = ImageHeaders[fileExtension];
            var actualHeader = new byte[expectedHeader.Length];

            fileStream.Seek(0, SeekOrigin.Begin);
            fileStream.Read(actualHeader, 0, actualHeader.Length);
            fileStream.Seek(0, SeekOrigin.Begin);

            return !expectedHeader.Where((x, i) => x != actualHeader[i]).Any();
        }

        private bool ValidateContentType(string contentType, string fileExtension)
        {
            if (AllowedContentType.TryGetValue(fileExtension, out var expectedContentTypes))
            {
                return expectedContentTypes.Contains(contentType);
            }

            return false;
        }

        private bool ValidateImageAspectRatio(Stream imageStream)
        {
            var isSquare = true;

            imageStream.Seek(0, SeekOrigin.Begin);
            using (var skData = SKData.Create(imageStream))
            {
                using (var skImage = SKBitmap.Decode(skData))
                {
                    if (skImage.Width != skImage.Height)
                    {
                        isSquare = false;
                    }
                }
            }
            imageStream.Seek(0, SeekOrigin.Begin);

            return isSquare;
        }

        /// <summary>
        /// Converts a byte count into a string, selecting the most appropriate unit.
        /// Uses windows convention rather than SI for naming so KB is 1024 bytes.
        /// </summary>
        /// <param name="bytes">Byte count to represent</param>
        /// <returns>Byte count as a normalised string with units, e.g 1048576 input would come out as 1MB</returns>
        private static string BytesToFriendlyUnits(int bytes)
        {
            var kb = bytes / 1024;
            if (kb < 1024)
            {
                return $"{kb}KB";
            }

            return $"{kb / 1024}MB";
        }
    }
}
