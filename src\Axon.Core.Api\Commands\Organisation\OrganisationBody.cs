﻿using Axon.Core.Api.Models.Organisation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation
{
    public class OrganisationBody 
    {
        public string DisplayName { get; }
        public string CodeName { get; }
        public string Description { get; }
        public string Icon { get; }
        public string AccessLevel { get; }
        public bool IsDeleted { get; }
        public bool IsEnabled { get; }
        public SetupAppBody[] Apps { get; }
        public SetupThemeBody Theme { get; }
        [CanBeNull]
        public string ParentOrganisationId { get; }

        public OrganisationBody(string displayName, string description, string icon, string codeName, string accessLevel, bool isDeleted, bool isEnabled, SetupAppBody[] apps, SetupThemeBody theme = default(SetupThemeBody), string parentOrganisationId = null)
        {
            DisplayName = displayName;
            Description = description;
            Icon = icon;
            CodeName = codeName;
            AccessLevel = accessLevel;
            IsDeleted = isDeleted;
            IsEnabled = isEnabled;
            Apps = apps;
            Theme = theme;
            ParentOrganisationId = parentOrganisationId;
        }
    }
}
