﻿using System.Threading.Tasks;
using Axon.Core.Api.Commands;
using Axon.Core.Api.Extensions;
using Axon.Core.Api.Queries.UserAccess.CurrentUserPermissionsQuery;
using Axon.Core.Api.Queries.UserAccess.ValidateCurrentUserPermissions;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Axon.Core.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("v{version:apiVersion}/Users/<USER>")]
[ProducesResponseType(401, Type = typeof(CommandResponse))]
[ProducesResponseType(403, Type = typeof(CommandResponse))]
[ProducesResponseType(500, Type = typeof(CommandResponse))]
[Authorize]
public class UserAccessController : ApiControllerBase
{
    private readonly IValidator<GetCurrentUserPermissionAccessQuery> getCurrentUserPermissionAccessQueryValidator;

    public UserAccessController(IMediator mediator, IValidator<GetCurrentUserPermissionAccessQuery> getCurrentUserPermissionAccessQueryValidator) : base(mediator)
    {
        this.getCurrentUserPermissionAccessQueryValidator = getCurrentUserPermissionAccessQueryValidator;
    }

    [HttpGet("{permission}", Name = "GetCurrentUserPermissionAccess")]
    [ProducesResponseType(200, Type = typeof(CommandResponse<bool>))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetCurrentUserPermissionAccessQueryAsync(string permission)
    {
        var request = new GetCurrentUserPermissionAccessQuery(permission);
        var validationResult = await getCurrentUserPermissionAccessQueryValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.ToErrorDictionary());
        }

        return await Send(request);
    }

    [HttpPost("Validate", Name = "ValidateCurrentUserPermissionsAccess")]
    [ProducesResponseType(200, Type = typeof(CommandResponse<ValidateCurrentUserPermissionsAccessQueryResponse>))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
    public async Task<IActionResult> ValidateCurrentUserPermissionsAccessQueryAsync([FromBody] ValidateCurrentUserPermissionsAccessQuery request)
    {
        return await Send(request);
    }
}