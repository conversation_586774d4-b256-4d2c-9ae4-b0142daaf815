/*
 * Axon.Core.Api
 *
 * A REST API for Axon.Core.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Axon.Core.Api.Sdk.NetCore.Client;
using Axon.Core.Api.Sdk.NetCore.Model;

namespace Axon.Core.Api.Sdk.NetCore.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppUserApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="createAppUserModel"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse CreateAppUser(string appCodeName, string orgCodeName, string emailAddress, CreateAppUserModel? createAppUserModel = default(CreateAppUserModel?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="createAppUserModel"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> CreateAppUserWithHttpInfo(string appCodeName, string orgCodeName, string emailAddress, CreateAppUserModel? createAppUserModel = default(CreateAppUserModel?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <returns>CommandResponse</returns>
        CommandResponse DeleteAppUser(string appCodeName, string orgCodeName, string emailAddress);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> DeleteAppUserWithHttpInfo(string appCodeName, string orgCodeName, string emailAddress);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <returns>SpecificAppUserModelCommandResponse</returns>
        SpecificAppUserModelCommandResponse GetAppUser(string orgCodeName, string appCodeName, string emailAddress);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <returns>ApiResponse of SpecificAppUserModelCommandResponse</returns>
        ApiResponse<SpecificAppUserModelCommandResponse> GetAppUserWithHttpInfo(string orgCodeName, string appCodeName, string emailAddress);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>AppUserModelApiPagedListResultCommandResponse</returns>
        AppUserModelApiPagedListResultCommandResponse GetFilteredAppUsers(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of AppUserModelApiPagedListResultCommandResponse</returns>
        ApiResponse<AppUserModelApiPagedListResultCommandResponse> GetFilteredAppUsersWithHttpInfo(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>UserModelApiPagedListResultCommandResponse</returns>
        UserModelApiPagedListResultCommandResponse GetFilteredEligibleUsers(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of UserModelApiPagedListResultCommandResponse</returns>
        ApiResponse<UserModelApiPagedListResultCommandResponse> GetFilteredEligibleUsersWithHttpInfo(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="updateAppUserModel"> (optional)</param>
        /// <returns>CommandResponse</returns>
        CommandResponse UpdateAppUser(string appCodeName, string orgCodeName, string emailAddress, UpdateAppUserModel? updateAppUserModel = default(UpdateAppUserModel?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="updateAppUserModel"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        ApiResponse<CommandResponse> UpdateAppUserWithHttpInfo(string appCodeName, string orgCodeName, string emailAddress, UpdateAppUserModel? updateAppUserModel = default(UpdateAppUserModel?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppUserApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="createAppUserModel"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> CreateAppUserAsync(string appCodeName, string orgCodeName, string emailAddress, CreateAppUserModel? createAppUserModel = default(CreateAppUserModel?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="createAppUserModel"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> CreateAppUserWithHttpInfoAsync(string appCodeName, string orgCodeName, string emailAddress, CreateAppUserModel? createAppUserModel = default(CreateAppUserModel?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> DeleteAppUserAsync(string appCodeName, string orgCodeName, string emailAddress, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> DeleteAppUserWithHttpInfoAsync(string appCodeName, string orgCodeName, string emailAddress, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of SpecificAppUserModelCommandResponse</returns>
        System.Threading.Tasks.Task<SpecificAppUserModelCommandResponse> GetAppUserAsync(string orgCodeName, string appCodeName, string emailAddress, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (SpecificAppUserModelCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<SpecificAppUserModelCommandResponse>> GetAppUserWithHttpInfoAsync(string orgCodeName, string appCodeName, string emailAddress, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppUserModelApiPagedListResultCommandResponse</returns>
        System.Threading.Tasks.Task<AppUserModelApiPagedListResultCommandResponse> GetFilteredAppUsersAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppUserModelApiPagedListResultCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<AppUserModelApiPagedListResultCommandResponse>> GetFilteredAppUsersWithHttpInfoAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UserModelApiPagedListResultCommandResponse</returns>
        System.Threading.Tasks.Task<UserModelApiPagedListResultCommandResponse> GetFilteredEligibleUsersAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UserModelApiPagedListResultCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<UserModelApiPagedListResultCommandResponse>> GetFilteredEligibleUsersWithHttpInfoAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="updateAppUserModel"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        System.Threading.Tasks.Task<CommandResponse> UpdateAppUserAsync(string appCodeName, string orgCodeName, string emailAddress, UpdateAppUserModel? updateAppUserModel = default(UpdateAppUserModel?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="updateAppUserModel"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommandResponse>> UpdateAppUserWithHttpInfoAsync(string appCodeName, string orgCodeName, string emailAddress, UpdateAppUserModel? updateAppUserModel = default(UpdateAppUserModel?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IAppUserApi : IAppUserApiSync, IAppUserApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class AppUserApi : IDisposable, IAppUserApi
    {
        private Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="AppUserApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public AppUserApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppUserApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public AppUserApi(string basePath)
        {
            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppUserApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public AppUserApi(Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppUserApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppUserApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppUserApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppUserApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                new Axon.Core.Api.Sdk.NetCore.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppUserApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public AppUserApi(HttpClient client, Axon.Core.Api.Sdk.NetCore.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.Core.Api.Sdk.NetCore.Client.Configuration.MergeConfigurations(
                Axon.Core.Api.Sdk.NetCore.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.Core.Api.Sdk.NetCore.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppUserApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public AppUserApi(Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient client, Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient asyncClient, Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Axon.Core.Api.Sdk.NetCore.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Axon.Core.Api.Sdk.NetCore.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Axon.Core.Api.Sdk.NetCore.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="createAppUserModel"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse CreateAppUser(string appCodeName, string orgCodeName, string emailAddress, CreateAppUserModel? createAppUserModel = default(CreateAppUserModel?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = CreateAppUserWithHttpInfo(appCodeName, orgCodeName, emailAddress, createAppUserModel);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="createAppUserModel"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> CreateAppUserWithHttpInfo(string appCodeName, string orgCodeName, string emailAddress, CreateAppUserModel? createAppUserModel = default(CreateAppUserModel?))
        {
            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->CreateAppUser");

            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->CreateAppUser");

            // verify the required parameter 'emailAddress' is set
            if (emailAddress == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'emailAddress' when calling AppUserApi->CreateAppUser");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("emailAddress", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(emailAddress)); // path parameter
            localVarRequestOptions.Data = createAppUserModel;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateAppUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="createAppUserModel"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> CreateAppUserAsync(string appCodeName, string orgCodeName, string emailAddress, CreateAppUserModel? createAppUserModel = default(CreateAppUserModel?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await CreateAppUserWithHttpInfoAsync(appCodeName, orgCodeName, emailAddress, createAppUserModel, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="createAppUserModel"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> CreateAppUserWithHttpInfoAsync(string appCodeName, string orgCodeName, string emailAddress, CreateAppUserModel? createAppUserModel = default(CreateAppUserModel?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->CreateAppUser");

            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->CreateAppUser");

            // verify the required parameter 'emailAddress' is set
            if (emailAddress == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'emailAddress' when calling AppUserApi->CreateAppUser");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("emailAddress", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(emailAddress)); // path parameter
            localVarRequestOptions.Data = createAppUserModel;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateAppUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <returns>CommandResponse</returns>
        public CommandResponse DeleteAppUser(string appCodeName, string orgCodeName, string emailAddress)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = DeleteAppUserWithHttpInfo(appCodeName, orgCodeName, emailAddress);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> DeleteAppUserWithHttpInfo(string appCodeName, string orgCodeName, string emailAddress)
        {
            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->DeleteAppUser");

            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->DeleteAppUser");

            // verify the required parameter 'emailAddress' is set
            if (emailAddress == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'emailAddress' when calling AppUserApi->DeleteAppUser");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("emailAddress", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(emailAddress)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<CommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteAppUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> DeleteAppUserAsync(string appCodeName, string orgCodeName, string emailAddress, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await DeleteAppUserWithHttpInfoAsync(appCodeName, orgCodeName, emailAddress, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> DeleteAppUserWithHttpInfoAsync(string appCodeName, string orgCodeName, string emailAddress, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->DeleteAppUser");

            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->DeleteAppUser");

            // verify the required parameter 'emailAddress' is set
            if (emailAddress == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'emailAddress' when calling AppUserApi->DeleteAppUser");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("emailAddress", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(emailAddress)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<CommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteAppUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <returns>SpecificAppUserModelCommandResponse</returns>
        public SpecificAppUserModelCommandResponse GetAppUser(string orgCodeName, string appCodeName, string emailAddress)
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<SpecificAppUserModelCommandResponse> localVarResponse = GetAppUserWithHttpInfo(orgCodeName, appCodeName, emailAddress);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <returns>ApiResponse of SpecificAppUserModelCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<SpecificAppUserModelCommandResponse> GetAppUserWithHttpInfo(string orgCodeName, string appCodeName, string emailAddress)
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->GetAppUser");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->GetAppUser");

            // verify the required parameter 'emailAddress' is set
            if (emailAddress == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'emailAddress' when calling AppUserApi->GetAppUser");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("emailAddress", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(emailAddress)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<SpecificAppUserModelCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of SpecificAppUserModelCommandResponse</returns>
        public async System.Threading.Tasks.Task<SpecificAppUserModelCommandResponse> GetAppUserAsync(string orgCodeName, string appCodeName, string emailAddress, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<SpecificAppUserModelCommandResponse> localVarResponse = await GetAppUserWithHttpInfoAsync(orgCodeName, appCodeName, emailAddress, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (SpecificAppUserModelCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<SpecificAppUserModelCommandResponse>> GetAppUserWithHttpInfoAsync(string orgCodeName, string appCodeName, string emailAddress, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->GetAppUser");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->GetAppUser");

            // verify the required parameter 'emailAddress' is set
            if (emailAddress == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'emailAddress' when calling AppUserApi->GetAppUser");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("emailAddress", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(emailAddress)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<SpecificAppUserModelCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetAppUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>AppUserModelApiPagedListResultCommandResponse</returns>
        public AppUserModelApiPagedListResultCommandResponse GetFilteredAppUsers(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppUserModelApiPagedListResultCommandResponse> localVarResponse = GetFilteredAppUsersWithHttpInfo(orgCodeName, appCodeName, filter, orderBy, offset, limit);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of AppUserModelApiPagedListResultCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppUserModelApiPagedListResultCommandResponse> GetFilteredAppUsersWithHttpInfo(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->GetFilteredAppUsers");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->GetFilteredAppUsers");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<AppUserModelApiPagedListResultCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetFilteredAppUsers", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of AppUserModelApiPagedListResultCommandResponse</returns>
        public async System.Threading.Tasks.Task<AppUserModelApiPagedListResultCommandResponse> GetFilteredAppUsersAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppUserModelApiPagedListResultCommandResponse> localVarResponse = await GetFilteredAppUsersWithHttpInfoAsync(orgCodeName, appCodeName, filter, orderBy, offset, limit, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (AppUserModelApiPagedListResultCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<AppUserModelApiPagedListResultCommandResponse>> GetFilteredAppUsersWithHttpInfoAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->GetFilteredAppUsers");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->GetFilteredAppUsers");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<AppUserModelApiPagedListResultCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetFilteredAppUsers", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>UserModelApiPagedListResultCommandResponse</returns>
        public UserModelApiPagedListResultCommandResponse GetFilteredEligibleUsers(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<UserModelApiPagedListResultCommandResponse> localVarResponse = GetFilteredEligibleUsersWithHttpInfo(orgCodeName, appCodeName, filter, orderBy, offset, limit);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <returns>ApiResponse of UserModelApiPagedListResultCommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<UserModelApiPagedListResultCommandResponse> GetFilteredEligibleUsersWithHttpInfo(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->GetFilteredEligibleUsers");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->GetFilteredEligibleUsers");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<UserModelApiPagedListResultCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user/eligible", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetFilteredEligibleUsers", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UserModelApiPagedListResultCommandResponse</returns>
        public async System.Threading.Tasks.Task<UserModelApiPagedListResultCommandResponse> GetFilteredEligibleUsersAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<UserModelApiPagedListResultCommandResponse> localVarResponse = await GetFilteredEligibleUsersWithHttpInfoAsync(orgCodeName, appCodeName, filter, orderBy, offset, limit, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="orgCodeName"></param>
        /// <param name="appCodeName"></param>
        /// <param name="filter"> (optional, default to &quot;&quot;)</param>
        /// <param name="orderBy"> (optional, default to &quot;&quot;)</param>
        /// <param name="offset"> (optional, default to 0)</param>
        /// <param name="limit"> (optional, default to 20)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UserModelApiPagedListResultCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<UserModelApiPagedListResultCommandResponse>> GetFilteredEligibleUsersWithHttpInfoAsync(string orgCodeName, string appCodeName, string? filter = default(string?), string? orderBy = default(string?), int? offset = default(int?), int? limit = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->GetFilteredEligibleUsers");

            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->GetFilteredEligibleUsers");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            if (filter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "filter", filter));
            }
            if (orderBy != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "orderBy", orderBy));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<UserModelApiPagedListResultCommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user/eligible", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetFilteredEligibleUsers", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="updateAppUserModel"> (optional)</param>
        /// <returns>CommandResponse</returns>
        public CommandResponse UpdateAppUser(string appCodeName, string orgCodeName, string emailAddress, UpdateAppUserModel? updateAppUserModel = default(UpdateAppUserModel?))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = UpdateAppUserWithHttpInfo(appCodeName, orgCodeName, emailAddress, updateAppUserModel);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="updateAppUserModel"> (optional)</param>
        /// <returns>ApiResponse of CommandResponse</returns>
        public Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> UpdateAppUserWithHttpInfo(string appCodeName, string orgCodeName, string emailAddress, UpdateAppUserModel? updateAppUserModel = default(UpdateAppUserModel?))
        {
            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->UpdateAppUser");

            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->UpdateAppUser");

            // verify the required parameter 'emailAddress' is set
            if (emailAddress == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'emailAddress' when calling AppUserApi->UpdateAppUser");

            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("emailAddress", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(emailAddress)); // path parameter
            localVarRequestOptions.Data = updateAppUserModel;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<CommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="updateAppUserModel"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommandResponse</returns>
        public async System.Threading.Tasks.Task<CommandResponse> UpdateAppUserAsync(string appCodeName, string orgCodeName, string emailAddress, UpdateAppUserModel? updateAppUserModel = default(UpdateAppUserModel?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse> localVarResponse = await UpdateAppUserWithHttpInfoAsync(appCodeName, orgCodeName, emailAddress, updateAppUserModel, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.Core.Api.Sdk.NetCore.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="appCodeName"></param>
        /// <param name="orgCodeName"></param>
        /// <param name="emailAddress"></param>
        /// <param name="updateAppUserModel"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.Core.Api.Sdk.NetCore.Client.ApiResponse<CommandResponse>> UpdateAppUserWithHttpInfoAsync(string appCodeName, string orgCodeName, string emailAddress, UpdateAppUserModel? updateAppUserModel = default(UpdateAppUserModel?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'appCodeName' is set
            if (appCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'appCodeName' when calling AppUserApi->UpdateAppUser");

            // verify the required parameter 'orgCodeName' is set
            if (orgCodeName == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'orgCodeName' when calling AppUserApi->UpdateAppUser");

            // verify the required parameter 'emailAddress' is set
            if (emailAddress == null)
                throw new Axon.Core.Api.Sdk.NetCore.Client.ApiException(400, "Missing required parameter 'emailAddress' when calling AppUserApi->UpdateAppUser");


            Axon.Core.Api.Sdk.NetCore.Client.RequestOptions localVarRequestOptions = new Axon.Core.Api.Sdk.NetCore.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("appCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(appCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("orgCodeName", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(orgCodeName)); // path parameter
            localVarRequestOptions.PathParameters.Add("emailAddress", Axon.Core.Api.Sdk.NetCore.Client.ClientUtils.ParameterToString(emailAddress)); // path parameter
            localVarRequestOptions.Data = updateAppUserModel;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<CommandResponse>("/v1/organisation/{orgCodeName}/app/{appCodeName}/user/{emailAddress}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateAppUser", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
