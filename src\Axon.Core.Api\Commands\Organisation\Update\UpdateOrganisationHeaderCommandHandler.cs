﻿using AutoMapper;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Microsoft.Extensions.Logging;
using Phlex.Core.MessageBus;
using Axon.Core.Domain.Services.AzureBlobStorage;
using Axon.Core.Api.Validators.FileUploads;
using Axon.Core.Api.Services.FileUpload;

namespace Axon.Core.Api.Commands.Organisation.Update
{
    internal class UpdateOrganisationHeaderCommandHandler : BaseEntityImageUpdateCommandHandler<UpdateOrganisationHeaderCommandRequest, OrganisationEntity, OrganisationUpdated>
    {
        private static EntityImageRequirements imageRequirements = new EntityImageRequirements { MaxFileSizeInBytes = 512000, ShouldBeSquare = false };

        public UpdateOrganisationHeaderCommandHandler(
            IOrganisationRepository repo,
            IMapper mapper,
            IMessageBus messageBus,
            IOrganisationThemeAzureBlobStorageManager organisationThemeAzureBlobStorageManager,
            IUpdateImageValidator updateImageValidator,
            IImageResizer imageResizer,
            ILogger<UpdateOrganisationHeaderCommandHandler> logger) :
            base(repo, mapper, messageBus, organisationThemeAzureBlobStorageManager, updateImageValidator, imageResizer, imageRequirements, logger)
        {
        }

        protected override string GetEntityCodeName(OrganisationEntity entity)
        {
            return entity.CodeName;
        }

        protected override void AppendImageUrlToEntity(OrganisationEntity entity, string blobUri)
        {
            if (entity.Theme != null)
            {
                entity.Theme.HeaderUrl = blobUri;
            }
            else
            {
                entity.Theme = new OrganisationEntity.ThemeConfigEntity
                {
                    HeaderUrl = blobUri
                };
            }
        }
    }
}
