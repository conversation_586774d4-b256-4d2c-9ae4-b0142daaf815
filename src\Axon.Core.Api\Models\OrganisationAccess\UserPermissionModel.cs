﻿using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Api.Models.OrganisationAccess
{
    public class UserPermissionModel
    {
        public UserPermissionModel(string id, string name, string email)
        {
            Id = id;
            Name = name;
            Email = email;
        }

        [Required]
        public string Id { get; }
        [Required]
        public string Name { get; }
        [Required]
        public string Email { get;  }
    }
}
