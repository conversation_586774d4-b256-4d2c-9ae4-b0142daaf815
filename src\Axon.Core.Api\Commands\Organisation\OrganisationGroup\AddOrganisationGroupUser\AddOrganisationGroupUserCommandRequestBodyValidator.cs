﻿using Axon.Core.Api.Validators;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.Core.Api.Commands.Organisation.OrganisationGroup.AddOrganisationGroupUser;

[UsedImplicitly]
public class AddOrganisationGroupUserCommandRequestBodyValidator : AbstractValidator<AddOrganisationGroupUserCommandRequestBody>
{
    public AddOrganisationGroupUserCommandRequestBodyValidator()
    {
        RuleForEach(x => x.UserIds)
            .MustBeAValidGuid();
    }
}