﻿using Axon.Core.Api.Validators;
using FluentValidation;

namespace Axon.Core.Api.Queries.Role.ListQuery;

public class GetFilteredRoleListQueryRequestValidator : AbstractValidator<GetFilteredRoleListQueryRequest>
{
    public GetFilteredRoleListQueryRequestValidator()
    {
        RuleFor(x => x.OrganisationCodeName)
            .MustBeAValidCodeName();
        RuleFor(x => x.AppCodeName)
            .MustBeAValidCodeName();
    }
}