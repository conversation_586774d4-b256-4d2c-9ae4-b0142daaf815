﻿using System;
using AutoMapper;
using Axon.Core.Api.Models.AppGroup;
using Axon.Core.Contracts;
using Axon.Core.Domain.Entities;
using Axon.Core.Domain.Interfaces.Persistence;
using Axon.Core.Domain.Models.Audit;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Phlex.Core.MessageBus;
using System.Threading.Tasks;
using System.Threading;
using System.Collections.Generic;
using Axon.Core.Shared.Audit.Services;
using System.Net;
using Axon.Core.Domain.Constants;
using Axon.Core.Api.Services.AppGroup;
using Axon.Core.Api.Validators.Role;
using System.Linq;
using Axon.Core.Domain.Extensions;

namespace Axon.Core.Api.Commands.AppGroup.Update
{
    internal class UpdateAppGroupCommandHandler : BaseUpdateCommandHandler<UpdateAppGroupRequest, AppGroupEntity, AppGroupUpdatedEvent>
    {
        private readonly IRoleRepository roleRepository;
        private readonly IClientDetailsProvider clientDetailsProvider;
        private readonly ICorrelationIdProvider correlationIdProvider;
        private readonly IAppGroupMemberSynchroniser appGroupMemberSynchroniser;
        private readonly IAuditService<TenantAuditExtensions> auditService;
        private readonly IUserAndGroupAccessRoleValidator userAndGroupAccessRoleValidator;
        private readonly IOrganisationRepository organisationRepository;
        private readonly IAppRepository appRepository;

        public UpdateAppGroupCommandHandler(
            IRoleRepository roleRepository,
            IAppGroupMemberSynchroniser appGroupMemberSynchroniser,
            IAppGroupRepository repo, 
            IMapper mapper, 
            IMessageBus messageBus,
            IClientDetailsProvider clientDetailsProvider,
            ICorrelationIdProvider correlationIdProvider,
            IAuditService<TenantAuditExtensions> auditService,
            IUserAndGroupAccessRoleValidator userAndGroupAccessRoleValidator,
            IOrganisationRepository organisationRepository,
            IAppRepository appRepository)
            : base(repo, mapper, messageBus)
        {
            this.roleRepository = roleRepository;
            this.appGroupMemberSynchroniser = appGroupMemberSynchroniser;
            this.clientDetailsProvider = clientDetailsProvider;
            this.correlationIdProvider = correlationIdProvider;
            this.auditService = auditService;
            this.userAndGroupAccessRoleValidator = userAndGroupAccessRoleValidator;
            this.organisationRepository = organisationRepository;
            this.appRepository = appRepository;
        }

        public override async Task<CommandResponse> Handle(UpdateCommandRequest<UpdateAppGroupRequest> request, CancellationToken cancellationToken)
        {
            var appGroupRepository = (IAppGroupRepository)Repo;
            AppGroupEntity existingEntity = await appGroupRepository.GetItemAsync(request.Id);

            var validationResult = await ValidateRequest(request, existingEntity);
            if (!validationResult.isValid)
            {
                return validationResult.response;
            }

            var org = await organisationRepository.GetItemByCodeNameAsync(request.Model.OrganisationCodeName);
            if (org == default(OrganisationEntity))
            {
                return CommandResponse.Failed(nameof(request.Model.OrganisationCodeName), $"Organisation `{request.Model.OrganisationCodeName}` does not exist");
            }

            var appId = await appRepository.GetAppIdByAppCode(request.Model.AppCodeName);
            if (appId == default(string))
            {
                return CommandResponse.NotFound(nameof(AppEntity), request.Model.AppCodeName);
            }

            var correlationId = correlationIdProvider.Provide();
            var clientDetails = clientDetailsProvider.Provide();

            await UpdateAppGroup(request, existingEntity, correlationId, clientDetails);
            await appGroupMemberSynchroniser.SynchroniseUpdatedGroupRoleWithUsers(existingEntity, correlationId, clientDetails);

            AppGroupUpdatedEvent appGroupEvent = new AppGroupUpdatedEvent
            {
                OrgId = org.Id,
                OrgCodeName = org.CodeName,
                AppCodeName = existingEntity.AppCodeName,
                AppId = appId,
                GroupId = existingEntity.GroupId,
                Action = EventActionType.Created,

            };

            await MessageBus.PublishAsync(appGroupEvent, cancellationToken: cancellationToken);

            return CommandResponse.Success();
        }

        private async Task<(bool isValid, CommandResponse response)> ValidateRequest(UpdateCommandRequest<UpdateAppGroupRequest> request, AppGroupEntity existingEntity)
        {
            if (existingEntity == null)
            {
                return (false, CommandResponse<AppGroupModel>.Failed(new Dictionary<string, string[]> { { nameof(request.Id), new[] { $"App Group with Id `{request.Id}` does not exist" } } }, HttpStatusCode.NotFound));
            }

            if (existingEntity.OrganisationCodeName != request.Model.OrganisationCodeName || existingEntity.AppCodeName != request.Model.AppCodeName)
            {
                return (false, CommandResponse<AppGroupModel>.Failed(new Dictionary<string, string[]> { { nameof(request), new[] { $"App Group with Id `{request.Id}` does not belong to App `{request.Model.AppCodeName}` within Organisation `{request.Model.OrganisationCodeName}`" } } }));
            }
            var roleValidation = await userAndGroupAccessRoleValidator.Validate(request.Model.UpdateAppGroupBody.RoleId, request.Model.AppCodeName, request.Model.OrganisationCodeName, request.Model.UpdateAppGroupBody.Scopes);
            if (!roleValidation.valid)
            {
                return (false, CommandResponse.BadRequest(nameof(AppGroup), roleValidation.message));
            }

            var roleExistsForApp = await roleRepository.RoleExistsForAppAsync(
                request.Model.OrganisationCodeName,
                request.Model.AppCodeName,
                request.Model.UpdateAppGroupBody.RoleName,
                request.Model.UpdateAppGroupBody.RoleId);

            if (!roleExistsForApp)
            {
                return (false, CommandResponse<AppGroupModel>.Failed(new Dictionary<string, string[]> { { nameof(request.Model.UpdateAppGroupBody), new[] { $"Role `{request.Model.UpdateAppGroupBody.RoleName}` with Id `{request.Model.UpdateAppGroupBody.RoleId}` does not belong to App `{request.Model.AppCodeName}` within Organisation `{request.Model.OrganisationCodeName}`" } } }));
            }

            return (true, null);
        }

        private async Task UpdateAppGroup(UpdateCommandRequest<UpdateAppGroupRequest> request, AppGroupEntity existingEntity, Guid correlationId, ClientDetails clientDetails)
        {
            var tenantAuditExtensions = new TenantAuditExtensions(AuditEventCategories.AppGroup, AuditEventDescriptions.AppGroupUpdated, correlationId, clientDetails, request.Model.OrganisationCodeName);

            await auditService.LogAsync(AuditEventTypes.AppGroupUpdated, tenantAuditExtensions, existingEntity,
                async () =>
                {
                    existingEntity.RoleId = request.Model.UpdateAppGroupBody.RoleId;
                    existingEntity.Role = request.Model.UpdateAppGroupBody.RoleName;
                    existingEntity.LastUpdatedDate = DateTime.Now;
                    existingEntity.Scopes = request.Model.UpdateAppGroupBody.Scopes?.Select(s => new RoleEntity.ScopeResources { Scope = s.Scope, Resources = s.Resources?.Select(r => new RoleEntity.Resource { Id = r.Id, Name = r.Name }).ToArray() }).ToArray();

                    await Repo.UpdateItemAsync(request.Id, existingEntity);
                });
        }
    }
}
