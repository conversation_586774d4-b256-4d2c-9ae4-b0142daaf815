apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: axon-core-api-scaledobject
  annotations:
    checkov.io/skip1: CKV_K8S_21=The namespace is being defined as helm argument
spec:
  scaleTargetRef:
    name: {{ include "axon-core-api.fullname" . }}-api
  pollingInterval: {{ .Values.scaledObject.pollingInterval }}
  cooldownPeriod: {{ .Values.scaledObject.cooldownPeriod }}
  minReplicaCount: {{ .Values.scaledObject.minReplicas }}
  maxReplicaCount: {{ .Values.scaledObject.maxReplicas }}
  triggers:
  - type: cpu
    metadata:
      type: Utilization
      value: "{{ .Values.scaledObject.utilisation }}"